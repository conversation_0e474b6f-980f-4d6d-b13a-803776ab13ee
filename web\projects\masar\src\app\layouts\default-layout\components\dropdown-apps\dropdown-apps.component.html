<div class="grid grid-cols-2 gap-3 p-3 md:grid-cols-3 lg:grid-cols-4">
    <ng-container *ngFor="let app of apps; let idx = index">
        <ng-container *ngIf="!app.isHidden">
            <a
                *appHasAnyPermissionId="
                    app.permissionIdList;
                    else noLinkTemplate
                "
                [routerLink]="app.link"
                (click)="done.emit(true)"
                class="flex flex-col items-center justify-center no-underline hover:no-underline"
            >
                <ng-container
                    *ngTemplateOutlet="
                        appContentTemplate;
                        context: {
                            bg: app.bg,
                            icon: app.icon,
                            title: app.title
                        }
                    "
                ></ng-container>
            </a>
        </ng-container>
        <ng-template #noLinkTemplate>
            <div
                class="flex cursor-not-allowed flex-col items-center justify-center no-underline opacity-50 hover:no-underline"
            >
                <ng-container
                    *ngTemplateOutlet="
                        appContentTemplate;
                        context: {
                            bg: app.bg,
                            icon: app.icon,
                            title: app.title
                        }
                    "
                ></ng-container>
            </div>
        </ng-template>
    </ng-container>
</div>

<ng-template #appContentTemplate let-bg="bg" let-icon="icon" let-title="title">
    <p
        class="mx-auto mb-1 inline-flex h-24 w-24 items-center justify-center rounded-md text-white"
        [ngClass]="bg"
    >
        <em [ngClass]="(icon || 'fa-light fa-circle-small') + ' fa-2xl'"></em>
    </p>

    <p class="text-center text-sm text-gray-800 md:text-base">
        {{ title | translate }}
    </p>
</ng-template>
