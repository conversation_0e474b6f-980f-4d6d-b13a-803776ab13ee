import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AttachmentNewComponent } from './attachment-new/attachment-new.component';
import { SharedAttachmentService } from './shared-attachment.service';
import { AttachmentListComponent } from './attachment-list/attachment-list.component';
import { MasarModule } from '@masar/features/masar/masar.module';
import { SharedModule } from '@masar/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';

const sharedComponents = [AttachmentListComponent, AttachmentNewComponent];

@NgModule({
    declarations: [sharedComponents],
    imports: [
        CommonModule,
        MasarModule,
        SharedModule,
        TranslateModule,
        SweetAlert2Module,
    ],
    exports: [sharedComponents],
    providers: [SharedAttachmentService],
})
export class AttachmentModule {}
