import { StatisticalReport } from './statistical-report.model';
import { Department } from './department.model';
import { StatisticalReportCategoryResult } from './statistical-report-category-result.model';
import { StatisticalReportCategoryYealryResult } from './statistical-report-category-yearly-result.model';

export interface StatisticalReportCategory {
    id: string;
    name: string;
    nameAr: string;
    nameEn: string;
    parent: StatisticalReportCategory;
    report: StatisticalReport;
    children: StatisticalReportCategory[];
    departments: Department[];
    results: StatisticalReportCategoryResult[];
    yearlyResults: StatisticalReportCategoryYealryResult[];
}
