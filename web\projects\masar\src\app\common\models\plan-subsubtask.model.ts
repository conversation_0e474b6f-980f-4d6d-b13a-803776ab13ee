import { LibraryFile } from './library-file.model';
import { PlanSubtask } from './plan-subtask.model';
import { PlanSubsubtaskApproval } from './plan-subsubtask-approval.model';
import { Plan } from './plan.model';
import { PlanTask } from './plan-task.model';

export interface PlanSubsubtask {
    id: string;
    from: Date;
    to: Date;
    status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'final';
    progress: number;
    expectedProgress: number;
    libraryFiles: LibraryFile[];
    canSubmit: boolean;
    canReject: boolean;
    canApprove: boolean;
    canFinalize: boolean;
    notPerformingReason: string;
    alternative: string;
    plan: Plan;
    task: PlanTask;
    subtask: PlanSubtask;
    approvals: PlanSubsubtaskApproval[];
    attachmentCount: number;
}
