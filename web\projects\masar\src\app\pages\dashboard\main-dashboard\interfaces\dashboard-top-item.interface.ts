export interface DashboardTopItem {
    id: string;
    labelEn: string;
    labelAr: string;
    iconClass: string;
    descriptionEn?: string;
    descriptionAr?: string;
    tagsEn?: string[];
    tagsAr?: string[];
    label: string;
    description?: string;
    tags?: string[];
}
export interface DashboardTopItemEnAr {
    id: string;
    labelEn: string;
    labelAr: string;
    iconClass: string;
    descriptionEn?: string;
    descriptionAr?: string;
    tagsEn?: string[];
    tagsAr?: string[];
}
