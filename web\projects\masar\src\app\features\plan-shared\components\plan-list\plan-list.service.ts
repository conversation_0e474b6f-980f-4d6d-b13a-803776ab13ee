import { Injectable } from '@angular/core';
import { miscFunctions, Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Item, Plan } from '@masar/common/models';
import { environment } from '@masar/env/environment';
import { TableResult } from '@masar/common/misc/table';

@Injectable()
export class PlanListService {
    public constructor(private httpClient: HttpClient) {}

    public list(
        keyword: string,
        years: string[],
        assigneeType: string | null,
        departmentIds: string[],
        includeChildDepartments: boolean,
        teamIds: string[],
        userIds: string[],
        categoryIds: string[],
        from: Date,
        to: Date,
        partneringDepartmentIds: Item[],
        governmentStrategicGoalIds: string[],
        strategicGoalIds: string[],
        kpiIds: string[],
        partnerIds: string[],
        initiatives: string,
        status: '' | 'new' | 'approved',
        progressStatus: '' | 'in_progress' | 'completed',
        onlyApprovable: boolean,
        orderBy: string,
        pageNumber: number,
        pageSize: number = 20,
        categoryTypes?: string[]
    ): Observable<TableResult<Plan>> {
        let params = new HttpParams();
        params = params
            .append('keyword', keyword)
            .append('initiatives', initiatives)
            .append('onlyApprovable', `${onlyApprovable}`)
            .append('from', from?.toISOString())
            .append('to', to?.toISOString())
            .append('status', status)
            .append('progressStatus', progressStatus)
            .append('assigneeType', assigneeType || '');

        years.forEach(item => (params = params.append('years', item)));
        departmentIds.forEach(
            item => (params = params.append('departmentIds', item))
        );
        params = params.append(
            'includeChildDepartments',
            includeChildDepartments
        );
        teamIds.forEach(item => (params = params.append('teamIds', item)));
        userIds.forEach(item => (params = params.append('userIds', item)));
        categoryTypes?.forEach(
            item => (params = params.append('categoryTypes', item))
        );
        categoryIds.forEach(
            item => (params = params.append('categoryIds', item))
        );
        governmentStrategicGoalIds.forEach(
            item => (params = params.append('governmentStrategicGoalIds', item))
        );

        partneringDepartmentIds?.forEach(
            item => (params = params.append('partneringDepartmentIds', item.id))
        );

        strategicGoalIds.forEach(
            item => (params = params.append('strategicGoalIds', item))
        );
        kpiIds.forEach(item => (params = params.append('kpiIds', item)));
        partnerIds.forEach(
            item => (params = params.append('partnerIds', item))
        );

        params = params.append('orderBy', orderBy);
        params = params.append('pageNumber', `${pageNumber}`);
        params = params.append('pageSize', `${pageSize}`);
        return this.httpClient
            .get<Result<TableResult<Plan>>>(environment.apiUrl + '/plan', {
                params,
            })
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(environment.apiUrl + '/plan/' + id)
            .pipe(map(result => result.messages[0]));
    }

    public duplicate(id: string): Observable<Plan> {
        return this.httpClient
            .post<Result<Plan>>(
                environment.apiUrl + '/plan/duplicate/' + id,
                null
            )
            .pipe(map(result => result.extra));
    }

    public export(id: string): Observable<Blob> {
        return this.httpClient.get(`${environment.apiUrl}/plan/export/${id}`, {
            responseType: 'blob',
        });
    }

    public togglePin(id: string, pinned: boolean): Observable<string> {
        return this.httpClient
            .put<Result>(
                `${environment.apiUrl}/plan/toggle-pin`,
                miscFunctions.objectToURLParams({
                    pin: JSON.stringify({ id: id, pinned: pinned }),
                })
            )
            .pipe(map(result => result.messages[0]));
    }
}
