export interface StrategicGoal {
    id: string;
    code: string;
    name: string;
    nameAr?: string;
    nameEn?: string;
    description?: string;
    descriptionAr?: string;
    descriptionEn?: string;
    order?: number;
    toYear?: number;
    fromYear?: number;
    category?: string;
    weight?: number;
    strategicPlan?: any;
    strategicPillar?: any;
}

export interface StrategicPillar {
    id: string;
    name: string;
    nameAr?: string;
    nameEn?: string;
    iconClass?: string | null;
    goals: StrategicGoal[];
}

export interface StrategicValue {
    id: string;
    name: string | null;
}
