import { Component, Input, OnInit } from '@angular/core';
import { FlowItemType } from '@masar/features/flow/types/flow-item-type.type';
import { FlowService } from '@masar/features/flow/services/flow.service';
import { FlowTransaction } from '@masar/common/models';

@Component({
    selector: 'app-flow-transaction-history',
    templateUrl: './flow-transaction-history.component.html',
})
export class FlowTransactionHistoryComponent implements OnInit {
    @Input() public itemId: string;
    @Input() public itemType: FlowItemType;
    @Input() public isWrapped: boolean = true;

    public items: FlowTransaction[];

    public constructor(private flowService: FlowService) {}

    public ngOnInit(): void {
        this.flowService
            .history(this.itemId, this.itemType)
            .subscribe(items => {
                this.items = items;
                this.items.forEach(x => {
                    x.note = x.note?.replace('\n', '<br />');
                });
            });
    }
}
