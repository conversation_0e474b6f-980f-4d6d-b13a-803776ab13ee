import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TableController } from '@masar/common/misc/table';
import { Item, KpiResultDataEntryRequest } from '@masar/common/models';
import { KpiResultDataEntryRequestService } from '../../../../../kpi-result-data-entry-request.service';
import { NotificationService } from 'mnm-webapp';
import { MiscApiService } from '@masar/core/services';
import { TranslateService } from '@ngx-translate/core';
import { getFromToDate } from '@masar/common/utils';
import { finalize } from 'rxjs/operators';
import { permissionList } from '@masar/common/constants';

interface HiddenFilters {
    kpiType?: boolean;
}

@Component({
    selector: 'app-data-entry-request-list-table',
    templateUrl: './data-entry-request-list-table.component.html',
})
export class DataEntryRequestListTableComponent implements OnInit, OnD<PERSON>roy {
    @Input() public kpiId?: string;
    @Input() public hiddenFilter: HiddenFilters = {};

    public tableController: TableController<
        KpiResultDataEntryRequest,
        {
            years: number[];
            startTime: Date[];
            endTime: Date[];
            measurementCycles: string[];
            paths: string[];
            departmentIds: string[];
            kpiTypeIds: string[];
        }
    >;
    public currentlyDeleting: string[] = [];

    public years: number[];
    public measurementCycles: Item[];
    public paths: Item[];
    public departments: Item[];
    public kpiTypes: Item[];
    public permissionList = permissionList;

    public constructor(
        private kpiResultDataEntryRequestService: KpiResultDataEntryRequestService,
        private notificationService: NotificationService,
        miscApiService: MiscApiService,
        translateService: TranslateService
    ) {
        miscApiService.years().subscribe(items => (this.years = items));

        miscApiService
            .getList('kpi-cycle')
            .subscribe(items => (this.measurementCycles = items));

        miscApiService
            .getList('kpi-result-data-entry-request-path')
            .subscribe(items => (this.paths = items));

        miscApiService.departments().subscribe(items => {
            this.departments = [
                {
                    id: '00000000-0000-0000-0000-000000000000',
                    name: translateService.instant('translate_all_departments'),
                },
                ...items,
            ];
        });
        miscApiService.kpiTypes().subscribe(items => (this.kpiTypes = items));
    }

    public ngOnInit(): void {
        this.tableController = new TableController<
            KpiResultDataEntryRequest,
            {
                years: number[];
                startTime: Date[];
                endTime: Date[];
                measurementCycles: string[];
                paths: string[];
                departmentIds: string[];
                kpiTypeIds: string[];
            }
        >(
            filter =>
                this.kpiResultDataEntryRequestService.list(
                    filter.data.years,
                    getFromToDate(filter.data.startTime)?.[0],
                    getFromToDate(filter.data.startTime)?.[1],
                    getFromToDate(filter.data.endTime)?.[0],
                    getFromToDate(filter.data.endTime)?.[1],
                    filter.data.measurementCycles,
                    filter.data.paths,
                    filter.data.departmentIds,
                    filter.data.kpiTypeIds,
                    this.kpiId ? [this.kpiId] : null,
                    filter.pageNumber,
                    filter.pageSize
                ),
            {
                data: {
                    years: [],
                    startTime: [],
                    endTime: [],
                    measurementCycles: [],
                    paths: [],
                    departmentIds: [],
                    kpiTypeIds: [],
                },
            }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public delete(item: KpiResultDataEntryRequest): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id);
        this.kpiResultDataEntryRequestService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }
}
