import { MnmFormField } from '@masar/shared/components';
import { Validators } from '@angular/forms';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },

    {
        fields: [
            {
                name: 'visitDate',
                type: 'date',
                label: 'translate_visit_date',
                size: 4,
                validators: [Validators.required],
            },

            {
                name: 'language',
                type: 'select',
                label: 'translate_preferred_language',
                size: 4,
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
                validators: [Validators.required],
            },
        ],
    },

    {
        name: 'department',
        label: 'translate_department',
        validators: [Validators.required],
    },

    {
        fields: [
            {
                name: 'type',
                label: 'translate_benchmark_type',
                type: 'select',
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
                size: 3,
                validators: [Validators.required],
            },

            {
                name: 'method',
                label: 'translate_benchmark_methodology',
                type: 'select',
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
                size: 3,
                validators: [Validators.required],
            },
        ],
    },
    {
        fields: [
            {
                name: 'goals',
                label: 'translate_strategic_goals_for_organization',
                type: 'select',
                bindLabel: 'name',
                multiple: true,
                size: 3,
                groupBy: 'yearRange',
                validators: [Validators.required],
            },

            {
                name: 'managementType',
                label: 'translate_management_type',
                type: 'select',
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
                size: 3,
                validators: [Validators.required],
            },
        ],
    },
    {
        fields: [
            {
                name: 'benchmarkPoints',
                label: 'translate_benchmark_points',
                type: 'textarea',
                hide: true,
            },
            {
                name: 'benefitRate',
                label: 'translate_benefit_rate',
                hide: true,
            },
        ],
    },

    {
        fields: [
            {
                name: 'isPreviouslyCompared',
                label: 'translate_is_previously_compared',
                type: 'checkbox',
                size: 3,
                hide: true,
            },
            {
                name: 'comparedWith',
                type: 'text',
                size: 11,
                hide: true,
            },
        ],
    },
    {
        fields: [
            {
                name: 'reasonForComparison',
                label: 'translate_reason_for_comparison',
                type: 'textarea',
                hide: true,
            },
            {
                name: 'currentPerformanceSummary',
                label: 'translate_current_performance_summary',
                type: 'textarea',
                hide: true,
            },
        ],
    },
    {
        name: 'operations',
        isDisabled: true,
        // validators: [Validators.required],
    },

    {
        name: 'otherManagements',
        isDisabled: true,
        // validators: [Validators.required],
    },

    {
        fields: [
            {
                name: 'entityType',
                label: 'translate_benchmark_entity_type',
                type: 'select',
                bindLabel: 'name',
                bindValue: 'id',
                compareWith: (a, b) => a.id === b,
                size: 4,
                validators: [Validators.required],
            },

            {
                name: 'entityName',
                label: 'translate_entity_name',
                type: 'text',
                size: 4,
                isDisabled: true,
            },

            {
                name: 'partner',
                label: 'translate_partner',
                type: 'select',
                bindLabel: 'name',
                size: 4,
                isDisabled: true,
            },
        ],
    },

    {
        fields: [
            {
                name: 'requestReasons',
                label: 'translate_reasons_for_requesting_benchmark',
                type: 'select',
                bindLabel: 'name',
                multiple: true,
                size: 3,
            },

            {
                name: 'otherRequestReasons',
                label: 'translate_other_requesting_reasons',
                type: 'textarea',
                size: 3,
            },

            {
                name: 'selectionReasons',
                label: 'translate_reasons_for_entity_selection',
                type: 'select',
                bindLabel: 'name',
                multiple: true,
                size: 3,
            },

            {
                name: 'otherSelectionReasons',
                label: 'translate_other_selection_reasons',
                type: 'textarea',
                size: 3,
            },
        ],
    },

    {
        sectionTitle: 'translate_visit_coordinator',
        fields: [
            {
                fields: [
                    {
                        name: 'coordinatorEmployeeNumber',
                        label: 'translate_employee_number',
                        type: 'text',
                        size: 4,
                        validators: [Validators.required],
                    },

                    {
                        name: 'coordinatorRank',
                        label: 'translate_employment_title',
                        type: 'text',
                        size: 4,
                        validators: [Validators.required],
                    },

                    {
                        name: 'coordinatorFullName',
                        label: 'translate_full_name',
                        type: 'text',
                        size: 4,
                        validators: [Validators.required],
                    },
                ],
            },

            {
                fields: [
                    {
                        name: 'coordinatorEmail',
                        label: 'translate_email',
                        type: 'text',
                        size: 4,
                        validators: [Validators.required],
                    },

                    {
                        name: 'coordinatorPhone',
                        label: 'translate_phone',
                        type: 'text',
                        size: 4,
                        validators: [Validators.required],
                    },

                    {
                        name: 'coordinatorOfficeNumber',
                        label: 'translate_office_number',
                        type: 'text',
                        size: 4,
                        validators: [Validators.required],
                    },
                ],
            },
        ],
    },

    {
        name: 'visitors',
        validators: [Validators.required],
    },

    {
        name: 'agenda',
        label: 'translate_visit_agenda_and_topics_to_be_discussed',
        type: 'textarea',
        validators: [Validators.required],
    },

    {
        name: 'kpiResults',
        label: 'translate_required_kpi_results',
        validators: [Validators.required],
    },
];
