<div class="grid grid-cols-2 gap-4 md:grid-cols-4">
    <div
        class="bg-blur flex flex-col p-3"
        *ngFor="let item of items; let i = index"
        [ngStyle]="{ backgroundColor: colors[i] }"
    >
        <p class="line-clamp-1 text-center text-sm">{{ item.label }}</p>

        <div class="flex flex-grow items-center justify-center">
            <p class="text-center text-3xl font-bold" *ngIf="!isLoadingEntries">
                {{ item.value | number }}
            </p>

            <!-- Is Loading -->
            <div class="loading-2 text-center" *ngIf="isLoadingEntries">
                <div class="lds-ellipsis">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
        </div>
    </div>
</div>
