import { NgModule } from '@angular/core';
import { MnmWebappModule } from 'mnm-webapp';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@masar/shared/shared.module';
import { MasarModule } from '@masar/features/masar/masar.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { CapabilityListComponent } from './components/capability-list/capability-list.component';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { CapabilityListFullComponent } from './components/capability-list-full/capability-list-full.component';
import { CapabilityLinkerComponent } from './components/capability-linker/capability-linker.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { TranslationModule } from '@ng-omar/translation';

@NgModule({
    imports: [
        CommonModule,
        TranslationModule,
        MnmWebappModule,
        SharedModule,
        MasarModule,
        FormsModule,
        ReactiveFormsModule,
        SweetAlert2Module,
        RouterModule,
        NgSelectModule,
        DragDropModule,
    ],
    declarations: [
        CapabilityListComponent,
        CapabilityListFullComponent,
        CapabilityLinkerComponent,
    ],
    exports: [
        CapabilityListComponent,
        CapabilityListFullComponent,
        CapabilityLinkerComponent,
    ],
})
export class CapabilitySharedModule {}
