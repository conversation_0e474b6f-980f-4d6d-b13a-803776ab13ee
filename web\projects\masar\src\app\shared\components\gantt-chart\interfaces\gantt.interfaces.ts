// Generic interfaces for reusability
export interface GanttItem<T = unknown> {
    id: string;
    name: string;
    from: Date | string;
    to: Date | string;
    progress?: number;
    children?: GanttItem<T>[];
    data?: T; // Store original data for reference
}

export interface GanttDataMapper<T> {
    getId: (item: T) => string;
    getName: (item: T) => string;
    getFromDate: (item: T) => Date | string;
    getToDate: (item: T) => Date | string;
    getProgress?: (item: T) => number;
    getChildren?: (item: T) => T[];
}

export interface GanttConfig {
    taskHeight?: number;
    subtaskHeight?: number;
    taskColors?: {
        background?: string;
        progress?: string;
        progressBadge?: string;
    };
    subtaskColors?: {
        background?: string;
        progress?: string;
        progressBadge?: string;
    };
    showProgress?: boolean;
    allowExpansion?: boolean;
    dateFormat?: string;
    initiallyExpanded?: boolean; // If true, all tasks with children will be expanded initially
}

export interface GanttTemplateContext<T> {
    $implicit: GanttItem<T>;
    item: GanttItem<T>;
}

export interface MonthHeader {
    month: string;
    colspan: number; // cspell:disable-line
}
