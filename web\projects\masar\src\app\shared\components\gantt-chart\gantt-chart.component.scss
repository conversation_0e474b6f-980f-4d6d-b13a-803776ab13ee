.gantt-chart-wrapper {
    width: 100%;
}

.gantt-container {
    min-width: 800px;
    background: #fff;
}

.gantt-container::-webkit-scrollbar {
    height: 8px;
}

.gantt-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.gantt-container::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: #c1c1c1;
}

.gantt-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Hover effects for task rows */
.gantt-container .border-b:hover {
    background-color: rgb(249 250 251 / 50%);
}

/* Progress bar animation */
.gantt-chart-wrapper .rounded-md {
    transition: all 0.3s ease-in-out;
}

/* Custom loading styles */
.gantt-chart-wrapper .text-gray-500 {
    font-size: 1rem;
}

/* Responsive adjustments */
@media (width <= 768px) {
    .gantt-container {
        min-width: 100%;
    }

    /* Target the task name column specifically */
    .gantt-chart-wrapper .w-1\/3 {
        width: 250px;
        min-width: 250px;
    }
}

@media (width <= 640px) {
    .gantt-container {
        min-width: 100%;
    }

    /* Even smaller screens */
    .gantt-chart-wrapper .w-1\/3 {
        width: 200px;
        min-width: 200px;
    }
}
