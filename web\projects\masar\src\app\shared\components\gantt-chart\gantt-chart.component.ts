import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    OnChanges,
    OnInit,
    Output,
    SimpleChanges,
    TemplateRef,
    ViewChild,
    ElementRef,
    OnDestroy,
} from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Import from services
import {
    GanttItem,
    GanttDataMapper,
    GanttConfig,
    GanttTemplateContext,
    MonthHeader,
    GanttChartService,
    GanttState,
    TaskPosition,
} from './services';

/**
 * TODO: Enterprise-Grade Gantt Chart Component
 *
 * TODO: ENHANCEMENTS TO APPLY:
 * ✅ SOLID Principles Implementation
 * ✅ Performance Optimizations
 * ✅ Error Handling & Input Validation
 * ✅ Better Loading States
 * 🔄 Keyboard Navigation Support
 *
 * TODO: COMPONENT ENHANCEMENTS:
 * ✅ Component Extraction (header, task-row, subtask-row)
 * 🔄 Responsive Design Improvements
 * ✅ RTL/LTR Text Direction Support (Arabic/English)
 * ✅ Smart Async Validation (handles loading states properly)
 *
 * TODO: FUTURE ROADMAP (for next iterations):
 * 🔄 Virtual Scrolling for large datasets
 * 🔄 Advanced Keyboard Shortcuts
 * 🔄 Animation & Interaction Enhancements
 * 🔄 Export Functionality
 * 🔄 Complete Internationalization (translations)
 * 🔄 Theme System
 * 🔄 Drag & Drop Task Reordering
 * 🔄 Task Dependencies Visualization
 */

@Component({
    selector: 'app-gantt-chart',
    templateUrl: './gantt-chart.component.html',
    styleUrls: ['./gantt-chart.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GanttChartComponent<T = unknown>
    implements OnInit, OnChanges, OnDestroy
{
    @ViewChild('ganttContainer', { static: false })
    public ganttContainer: ElementRef<HTMLElement>;

    // Generic inputs
    @Input() public rawData: T[] = [];
    @Input() public dataMapper: GanttDataMapper<T>;
    @Input() public config: GanttConfig = {};
    @Input() public startDate?: Date;
    @Input() public endDate?: Date;
    @Input() public title?: string;
    @Input() public isLoading = false;
    @Input() public taskTemplate?: TemplateRef<GanttTemplateContext<T>>;
    @Input() public subtaskTemplate?: TemplateRef<GanttTemplateContext<T>>;
    @Input() public adjustDatesBy?: number = 1;
    @Input() public progress?: number = 0;

    // For backward compatibility - will be converted using dataMapper
    @Input() public data: GanttItem<T>[] = [];

    @Output() public taskClick = new EventEmitter<GanttItem<T>>();
    @Output() public taskToggle = new EventEmitter<string>();
    @Output() public rawTaskClick = new EventEmitter<T>();

    // Public properties exposed from orchestrator state
    public processedData: GanttItem<T>[] = [];
    public adjustedStartDate: Date | null = null;
    public adjustedEndDate: Date | null = null;
    public displayDates: Date[] = [];
    public displayMonths: MonthHeader[] = [];
    public mergedConfig: GanttConfig = {};

    // Private properties
    private destroy$ = new Subject<void>();
    private orchestrator: GanttChartService<T>;

    public constructor(
        private cdr: ChangeDetectorRef,
        private orchestratorService: GanttChartService<T>
    ) {
        this.orchestrator = this.orchestratorService;
    }

    public ngOnInit(): void {
        // Subscribe to orchestrator state changes
        this.orchestrator.state$
            .pipe(takeUntil(this.destroy$))
            .subscribe((state: GanttState<T>) => {
                this.updateComponentFromState(state);
                this.cdr.markForCheck();
            });

        // Initialize chart with current inputs
        this.initializeChart();
    }

    public ngOnChanges(changes: SimpleChanges): void {
        if (
            changes['rawData'] ||
            changes['dataMapper'] ||
            changes['data'] ||
            changes['startDate'] ||
            changes['endDate'] ||
            changes['config']
        ) {
            this.initializeChart();
        }
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.orchestrator.resetState();
    }

    public getTaskPosition(item: GanttItem<T>): TaskPosition {
        return this.orchestrator.getTaskPosition(item);
    }

    public toggleTask(taskId: string): void {
        this.orchestrator.toggleTask(taskId);
        this.taskToggle.emit(taskId);
    }

    public isTaskExpanded(taskId: string): boolean {
        return this.orchestrator.isTaskExpanded(taskId);
    }

    public hasChildren(item: GanttItem<T>): boolean {
        return this.orchestrator.hasChildren(item);
    }

    public onTaskClick(item: GanttItem<T>): void {
        this.taskClick.emit(item);
        if (item.data) {
            this.rawTaskClick.emit(item.data);
        }
    }

    // TrackBy functions for better performance
    public trackByItemId(index: number, item: GanttItem<T>): string {
        // Include index for better tracking when items can be reordered
        return `${item.id}-${index}`;
    }

    public trackByChildId(index: number, child: GanttItem<T>): string {
        return `${child.id}-${index}`;
    }

    public trackByMonth(index: number, month: MonthHeader): string {
        return `${month.month}-${index}`;
    }

    // Method to get Gantt container element for PDF export
    public getGanttElementForExport(): HTMLElement[] {
        return this.ganttContainer?.nativeElement
            ? [this.ganttContainer.nativeElement]
            : [];
    }

    // Method to format tooltip with item name and dates
    public formatTooltip(item: GanttItem<T>): string {
        return this.orchestrator.formatTooltip(item);
    }

    public getAdjustedStartDate(): Date {
        return this.adjustedStartDate;
    }

    public getAdjustedEndDate(): Date | null {
        return this.adjustedEndDate;
    }

    /**
     * Initializes the chart with current input values
     */
    private initializeChart(): void {
        try {
            // Validate inputs before initialization
            const validationResult = this.validateInputs();
            if (!validationResult.isValid) {
                return;
            }

            this.orchestrator.initializeChart(
                this.rawData?.length ? this.rawData : null,
                this.dataMapper,
                this.data?.length ? this.data : null,
                this.config,
                this.startDate,
                this.endDate,
                this.adjustDatesBy
            );
        } catch (error) {
            // Optionally emit an error event or show user-friendly error
        }
    }

    /**
     * Validates component inputs with improved async loading handling
     */
    private validateInputs(): {
        isValid: boolean;
        shouldWarn: boolean;
        message?: string;
    } {
        // Check if we have either rawData with mapper or processed data
        const hasRawData = this.rawData?.length > 0;
        const hasProcessedData = this.data?.length > 0;
        const hasDataMapper = !!this.dataMapper;

        // If no data at all, check if we're in a loading state
        if (!hasRawData && !hasProcessedData) {
            // During loading, this is expected - don't warn
            if (this.isLoading) {
                return { isValid: false, shouldWarn: false };
            }
            // Not loading but no data - could be valid (empty state) or still loading async
            // Don't warn immediately, let the parent component handle empty states
            return { isValid: false, shouldWarn: false };
        }

        // If rawData is provided, dataMapper must be provided
        if (hasRawData && !hasDataMapper) {
            return {
                isValid: false,
                shouldWarn: true,
                message: 'dataMapper is required when rawData is provided',
            };
        }

        return { isValid: true, shouldWarn: false };
    }

    /**
     * Updates component properties from orchestrator state
     */
    private updateComponentFromState(state: GanttState<T>): void {
        this.processedData = state.processedData;
        this.adjustedStartDate = state.adjustedStartDate;
        this.adjustedEndDate = state.adjustedEndDate;
        this.isLoading = state.isLoading;
        this.mergedConfig = state.mergedConfig;

        if (state.timelineData) {
            this.displayDates = state.timelineData.dates;
            this.displayMonths = state.timelineData.monthHeaders;
        } else {
            this.displayDates = [];
            this.displayMonths = [];
        }
    }
}
