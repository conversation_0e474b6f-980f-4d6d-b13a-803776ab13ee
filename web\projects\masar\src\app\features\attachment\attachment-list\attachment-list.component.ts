import {
    Component,
    EventEmitter,
    Input,
    NgModuleRef,
    OnInit,
    Output,
} from '@angular/core';
import { ModalService } from 'mnm-webapp';
import { permissionList } from '@masar/common/constants';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { AttachmentNewComponent } from '../attachment-new/attachment-new.component';
import { SharedAttachmentService } from '../shared-attachment.service';
import { AttachmentFile, AttachmentOptions } from '../interfaces';

@Component({ templateUrl: './attachment-list.component.html' })
export class AttachmentListComponent implements OnInit {
    @Input() public options: AttachmentOptions;

    @Output() public update = new EventEmitter<number>();

    public items: AttachmentFile[] = [];

    public currentlyDeleting: string[] = [];

    public permissionList = permissionList;

    public constructor(
        private readonly sharedAttachmentService: SharedAttachmentService,
        private readonly modalService: ModalService,
        private readonly moduleRef: NgModuleRef<any>
    ) {}

    public ngOnInit(): void {
        this.sharedAttachmentService
            .listAttachments(
                this.options.id,
                this.options.endPoint,
                this.options.httpQueryParams
            )
            .subscribe(items => (this.items = items));
    }

    public async showCreateAttachmentDialog(): Promise<void> {
        let subscription: Subscription;

        const component = await this.modalService.show(AttachmentNewComponent, {
            beforeInit: c => {
                c.options = this.options;
            },
            onDismiss: () => subscription.unsubscribe(),
            moduleRef: this.moduleRef,
        });

        subscription = component.created.subscribe(file => {
            this.items.push(file);
            this.modalService.dismiss(component);
            this.update.emit(this.items.length);
        });
    }

    public download(id: string, contentType: string): void {
        this.sharedAttachmentService
            .downloadAttachment(id, this.options.endPoint)
            .subscribe(file => {
                this.sharedAttachmentService.downloadBlob(file, contentType);
            });
    }

    public delete(id: string): void {
        this.currentlyDeleting.push(id);

        this.sharedAttachmentService
            .deleteAttachment(id, this.options.endPoint)
            .pipe(
                finalize(() => {
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== id
                    );
                })
            )
            .subscribe(() => {
                this.items = this.items.filter(x => x.id !== id);
                this.update.emit(this.items.length);
            });
    }
}
