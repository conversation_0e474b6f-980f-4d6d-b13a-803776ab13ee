import { Component, NgM<PERSON>uleRef, OnDestroy } from '@angular/core';
import { TableController } from '@masar/common/misc/table';
import { Benchmark, Item } from '@masar/common/models';
import { finalize, takeUntil } from 'rxjs/operators';
import { BenchmarkService } from '../benchmark.service';
import { ModalService, NotificationService } from 'mnm-webapp';
import {
    ImageApiService,
    MiscApiService,
    AppSettingFetcherService,
} from '@masar/core/services';
import { permissionList } from '@masar/common/constants';
import { SubmitReportComponent } from './helpers/components/submit-report/submit-report.component';
import { TranslateService } from '@ngx-translate/core';
import { ShowReportComponent } from './helpers/components/show-report/show-report.component';
import { ActivatedRoute, Router } from '@angular/router';
import { getFromToDate } from '@masar/common/utils';
import { Subject } from 'rxjs';

interface BenchmarkFilter {
    visitDateFromTo?: Date[];
    departmentIds?: string[];
    methods?: string[];
    goalIds?: string[];
    managementTypes?: string[];
    requestReasonIds?: string[];
    operationIds?: string[];
    kpiKeyword?: string;
    entityNameKeyword?: string;
    agendaKeyword?: string;
    approvalStatuses?: string[];
    years?: number[];
    includeChildDepartments?: boolean;
}

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
})
export class ListComponent implements OnDestroy {
    public tableController: TableController<Benchmark, BenchmarkFilter>;
    public currentlyProcessing: string[] = [];

    public departments: Item[] = [];
    public visitYears: Item[] = [];
    public methods: Item[] = [];
    public goals: Item[] = [];
    public types: Item[] = [];
    public managementTypes: Item[] = [];
    public requestReasons: Item[] = [];
    public operations: Item[] = [];

    public permissionList = permissionList;

    // Optional field visibility flags
    public optionalFields = {
        benefitRate: true,
    };

    private unsubscribeAll = new Subject();

    public constructor(
        public imageApiService: ImageApiService,
        private benchmarkService: BenchmarkService,
        private notificationService: NotificationService,
        private miscApiService: MiscApiService,
        private modalService: ModalService,
        private ngModuleRef: NgModuleRef<any>,
        private translateService: TranslateService,
        private appSettingFetcherService: AppSettingFetcherService,
        router: Router,
        activatedRoute: ActivatedRoute
    ) {
        this.loadItems();
        this.updateOptionalFieldsVisibility();

        this.tableController = new TableController<Benchmark, BenchmarkFilter>(
            filter =>
                benchmarkService.list(
                    getFromToDate(filter.data.visitDateFromTo)?.[0],
                    getFromToDate(filter.data.visitDateFromTo)?.[1],
                    filter.data.departmentIds,
                    filter.data.includeChildDepartments,
                    filter.data.methods,
                    filter.data.goalIds,
                    filter.data.managementTypes,
                    filter.data.requestReasonIds,
                    filter.data.operationIds,
                    filter.data.kpiKeyword,
                    filter.data.entityNameKeyword,
                    filter.data.agendaKeyword,
                    filter.data.approvalStatuses,
                    filter.data.years,
                    filter.pageNumber,
                    filter.pageSize
                ),
            {
                data: {
                    visitDateFromTo: [],
                    departmentIds: [],
                    methods: [],
                    goalIds: [],
                    managementTypes: [],
                    requestReasonIds: [],
                    operationIds: [],
                    kpiKeyword: '',
                    entityNameKeyword: '',
                    agendaKeyword: '',
                    approvalStatuses: [],
                    years: [],
                },
            },
            {
                routingControls: {
                    activatedRoute,
                    router,
                },
            }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public process(
        item: Benchmark,
        type: 'delete' | 'first_approve' | 'second_approve' = 'delete'
    ): void {
        this.currentlyProcessing.push(item.id);

        const observable =
            type === 'delete'
                ? this.benchmarkService.delete(item.id)
                : type === 'first_approve'
                ? this.benchmarkService.firstApprove(item.id)
                : this.benchmarkService.secondApprove(item.id);

        observable
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyProcessing = this.currentlyProcessing.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }

    public showSubmitReportDialog(item: Benchmark): void {
        this.translateService
            .get('translate_submit_report')
            .subscribe(async str => {
                const component = await this.modalService.show(
                    SubmitReportComponent,
                    {
                        title: str,
                        moduleRef: this.ngModuleRef,
                        beforeInit: c => {
                            c.benchmarkId = item.id;
                        },
                    }
                );

                component.submitted.subscribe(_ => {
                    this.tableController.filter$.next(false);
                    this.modalService.dismiss(component);
                });
            });
    }

    public showReport(item: Benchmark): void {
        this.translateService
            .get('translate_report_details')
            .subscribe(async str => {
                await this.modalService.show(ShowReportComponent, {
                    title: str,
                    beforeInit: c => {
                        c.benchmark = item;
                    },
                });
            });
    }

    private loadItems(): void {
        this.miscApiService
            .getList('benchmark-management-type')
            .subscribe(items => (this.managementTypes = items));

        this.miscApiService
            .getList('benchmark-method')
            .subscribe(items => (this.methods = items));

        this.miscApiService
            .getList('benchmark-request-reason', undefined, true)
            .subscribe(items => (this.requestReasons = items));

        this.miscApiService
            .departments()
            .subscribe(items => (this.departments = items));

        this.miscApiService
            .getList('strategic-goal', undefined, true)
            .subscribe(items => (this.goals = items));

        this.miscApiService
            .operations()
            .subscribe(items => (this.operations = items));

        this.miscApiService
            .getList('benchmark-selected-visit-year')
            .subscribe(items => (this.visitYears = items));
    }

    private updateOptionalFieldsVisibility(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(item => {
                const optionalFields = item.benchmarkSetting?.optionalFields;

                const isFieldEnabled: (name: string) => boolean = (
                    name: string
                ) =>
                    optionalFields.find(x => x.name === name)?.isEnabled ??
                    true;

                this.optionalFields = {
                    benefitRate: isFieldEnabled('benefit_rate'),
                };
            });
    }
}
