import {
    Component,
    EventEmitter,
    Input,
    Output,
    TemplateRef,
    ViewChild,
    OnInit,
    OnDestroy,
} from '@angular/core';
import { EvaluationStatisticsComponent } from '@masar/features/evaluate/components';
import { EvaluationType } from '@masar/features/evaluate/types';
import { EvaluateService } from '@masar/features/evaluate/services/evaluate.service';
import { EvaluateItem } from '@masar/features/evaluate/interfaces';
import {
    Evaluation,
    Item,
    Department,
    KpiEvaluationStatistics,
} from '@masar/common/models';
import { KpiMeasurementCycleType } from '@masar/common/types';
import { YearService } from '@masar/core/services';
import { Subject, Observable } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
@Component({
    selector: 'app-evaluation-dashboard-page',
    templateUrl: './evaluation-dashboard-page.component.html',
})
export class EvaluationDashboardPageComponent implements OnInit, OnD<PERSON>roy {
    @Input() public title: string;
    @Input() public type: EvaluationType;
    @Input() public hasListContent = true;
    @Input() public showDepartmentStatistics = false;
    @Input() public departmentStatisticsLoader: (
        evaluationId: string,
        measurementCycle: KpiMeasurementCycleType,
        year: number,
        parentId?: string
    ) => Observable<KpiEvaluationStatistics[]>;
    @Input() public measurementCyclesLoader: () => Observable<Item[]>;
    @Input()
    public evaluationTypesLoader: () => Observable<Evaluation[]>;

    @Output() public update = new EventEmitter();
    @Output() public evaluationChange = new EventEmitter<Evaluation>();

    @ViewChild(EvaluationStatisticsComponent)
    private statistics: EvaluationStatisticsComponent;

    @ViewChild('controlsTemplate')
    private controlsTemplate: TemplateRef<unknown>;

    // Properties for department statistics
    public evaluationTypes: Evaluation[] = [];
    public selectedEvaluationType: Evaluation | null = null;
    public measurementCycles: Item[] = [];
    public selectedMeasurementCycle: KpiMeasurementCycleType | null = null;
    public departmentStatistics: KpiEvaluationStatistics[] = [];
    public isLoadingStatistics = false;
    public currentYear: number;

    // Department navigation properties
    public currentParentDepartment: Department | null = null;
    public previousParents: Department[] = [];

    private destroy$ = new Subject<void>();

    public constructor(
        public readonly evaluateService: EvaluateService,
        private readonly yearService: YearService
    ) {
        this.currentYear = this.yearService.get();
    }

    public ngOnInit(): void {
        // Subscribe to year changes
        this.yearService.changes$
            .pipe(takeUntil(this.destroy$))
            .subscribe(year => {
                this.currentYear = year;
                this.resetDepartmentNavigation();
                this.loadDepartmentStatistics();
            });

        if (this.showDepartmentStatistics) {
            // Initialize with default values and load immediately
            this.initializeWithDefaults();

            // Load evaluation types
            if (this.evaluationTypesLoader) {
                this.evaluationTypesLoader().subscribe(
                    (response: Evaluation[]) => {
                        // Extract the items array from the response
                        this.evaluationTypes = response || [];
                        // Set to default if available, otherwise keep current selection
                        const defaultType = this.evaluationTypes.find(
                            t => t.isDefault
                        );
                        if (defaultType) {
                            this.selectedEvaluationType = defaultType;
                        } else if (
                            this.evaluationTypes.length > 0 &&
                            !this.selectedEvaluationType
                        ) {
                            this.selectedEvaluationType =
                                this.evaluationTypes[0];
                        }
                        // Load department statistics after evaluation type is set
                        this.loadDepartmentStatistics();
                    }
                );
            }

            // Load measurement cycles
            if (this.measurementCyclesLoader) {
                this.measurementCyclesLoader().subscribe((cycles: Item[]) => {
                    this.measurementCycles = cycles;
                    // Set to 'quarter' if available, otherwise keep current selection
                    const annualCycle = cycles.find(c => c.id === 'quarter');
                    if (annualCycle) {
                        this.selectedMeasurementCycle =
                            annualCycle.id as KpiMeasurementCycleType;
                    } else if (
                        cycles.length > 0 &&
                        !this.selectedMeasurementCycle
                    ) {
                        this.selectedMeasurementCycle = cycles[0]
                            .id as KpiMeasurementCycleType;
                    }
                    // Load department statistics after measurement cycle is set
                    this.loadDepartmentStatistics();
                });
            }
        }
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    public getControlsTemplate(): TemplateRef<unknown> {
        return this.controlsTemplate;
    }

    public onClickEvaluate(item: EvaluateItem): void {
        this.evaluateService
            .showEvaluationsList(item, this.type, undefined, item.id)
            .then(obs =>
                obs.subscribe(() => {
                    this.reload();
                    this.statistics?.reload();
                })
            );
    }

    public reload(): void {
        this.update.emit();
    }

    public onEvaluationTypeChange(evaluationType: Evaluation): void {
        this.selectedEvaluationType = evaluationType;
        this.resetDepartmentNavigation();
        this.loadDepartmentStatistics();
    }

    public onMeasurementCycleChange(
        measurementCycle: KpiMeasurementCycleType
    ): void {
        this.selectedMeasurementCycle = measurementCycle;
        this.resetDepartmentNavigation();
        this.loadDepartmentStatistics();
    }

    public goToNextDepartmentStatistics(parentDepartment: Department): void {
        if (this.currentParentDepartment) {
            this.previousParents.push(this.currentParentDepartment);
        }
        this.currentParentDepartment = parentDepartment;
        this.loadDepartmentStatistics();
    }

    public goToPreviousDepartmentStatistics(): void {
        this.currentParentDepartment = this.previousParents.pop() || null;
        this.loadDepartmentStatistics();
    }

    private initializeWithDefaults(): void {
        // Initialize arrays properly to prevent ng-select errors
        this.evaluationTypes = [];
        this.measurementCycles = [];

        // Don't set selectedEvaluationType to an empty object - wait for actual data
        this.selectedEvaluationType = null;
        this.selectedMeasurementCycle = 'quarter' as KpiMeasurementCycleType;
    }

    private resetDepartmentNavigation(): void {
        this.currentParentDepartment = null;
        this.previousParents = [];
    }

    private loadDepartmentStatistics(): void {
        if (
            this.selectedEvaluationType &&
            this.selectedMeasurementCycle &&
            this.departmentStatisticsLoader &&
            this.currentYear
        ) {
            this.isLoadingStatistics = true;
            this.departmentStatisticsLoader(
                this.selectedEvaluationType.id,
                this.selectedMeasurementCycle,
                this.currentYear,
                this.currentParentDepartment?.id
            ).subscribe(
                (statistics: KpiEvaluationStatistics[]) => {
                    this.departmentStatistics = statistics;
                    this.isLoadingStatistics = false;
                },
                () => {
                    this.isLoadingStatistics = false;
                }
            );
        }
    }
}
