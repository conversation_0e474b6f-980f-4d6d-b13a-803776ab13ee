<ng-container [ngSwitch]="displayStyle">
    <!-- Style 1 -->
    <div *ngSwitchCase="'style_1'" class="flex flex-col items-center gap-4">
        <h2 class="text-3xl font-bold text-primary">
            {{ 'translate_strategic_goals' | translate }}
        </h2>
        <ng-container [ngTemplateOutlet]="gridTemplate"></ng-container>
    </div>

    <!-- Default -->
    <ng-container *ngSwitchDefault>
        <app-content
            *ngIf="goalsResponse?.goals"
            [contentTitle]="'translate_strategic_goals' | translate"
        >
            <ng-container
                content
                [ngTemplateOutlet]="gridTemplate"
            ></ng-container>
        </app-content>
    </ng-container>
</ng-container>

<ng-template #gridTemplate>
    <div
        content
        #cardGrid
        class="cards grid w-full grid-cols-1 gap-2 md:grid-cols-3"
        [@flippingCardGrid]="goalsResponse.goals"
    >
        <!-- Card -->
        <a
            [routerLink]="['', 'kpi']"
            [queryParams]="{
                goalIds: [item.id],
                typeIds: goalKpiTypeIds
            }"
            #card
            *ngFor="let item of goalsResponse.goals"
            class="card"
            [@flippingCard]
        >
            <div class="card-content px-4 py-8">
                <!-- Weight and S.G Code -->
                <div
                    class="absolute left-2 right-2 top-2 flex items-start justify-between"
                >
                    <div
                        class="flex items-center gap-2 rounded-lg bg-[#46718a] p-2 px-2 py-1 text-center"
                    >
                        <div class="">{{ item.code }}</div>
                        <i class="fa-solid fa-bullseye-pointer"></i>
                    </div>
                    <div
                        *ngIf="item.weight"
                        class="flex items-center gap-2 rounded-lg bg-[#46718a] px-2 py-1 text-base"
                    >
                        <span class="">
                            {{ item.weight * 100 | round : 2 }}%
                        </span>
                        <i class="fa-regular fa-weight-hanging"></i>
                    </div>
                </div>

                <!-- Gauge -->
                <app-achievement-gauge
                    [radius]="150"
                    [value]="item.achieved"
                    [gaugeStyle]="gaugeStyle"
                    appearance="light"
                ></app-achievement-gauge>

                <!-- Name -->
                <h3 class="text-center">
                    {{ item.name }}
                </h3>
                <p
                    *ngIf="isOverallPerformanceEnabled"
                    class="text-lg font-semibold"
                >
                    (
                    {{
                        'translate_overall_performance_contribution'
                            | translate
                    }}:
                    <span
                        >{{
                            item.contributionToOverallPerformance * 100
                                | round : 0
                        }}%</span
                    >
                    )
                </p>
            </div>
        </a>
    </div>
</ng-template>
