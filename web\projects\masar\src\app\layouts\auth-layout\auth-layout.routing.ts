import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthLayoutComponent } from './auth-layout.component';
import { LoadTranslationsResolver } from '@ng-omar/translation';

const routes: Routes = [
    {
        path: '',
        component: AuthLayoutComponent,
        children: [
            {
                path: 'login',
                loadChildren: () =>
                    import('@masar/pages/login/login.module').then(
                        m => m.LoginModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { module: 'account' },
            },

            {
                path: 'forget-password',
                loadChildren: () =>
                    import(
                        '@masar/pages/forget-password/forget-password.module'
                    ).then(m => m.ForgetPasswordModule),
                resolve: { translations: LoadTranslationsResolver },
                data: { module: 'account' },
            },

            {
                path: 'reset-password',
                loadChildren: () =>
                    import(
                        '@masar/pages/reset-password/reset-password.module'
                    ).then(m => m.ResetPasswordModule),
                resolve: { translations: LoadTranslationsResolver },
                data: { module: 'account' },
            },

            {
                path: 'new-user-request',
                loadChildren: () =>
                    import(
                        '@masar/pages/new-user-request/new-user-request.module'
                    ).then(m => m.NewUserRequestModule),
                resolve: { translations: LoadTranslationsResolver },
                data: { module: 'account' },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class AuthLayoutRouting {}
