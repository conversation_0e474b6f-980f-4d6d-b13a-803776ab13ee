<app-page pageTitle="{{ 'translate_benchmark_details' | translate }}">
    <ng-container tools>
        <!-- List -->
        <a class="btn btn-sm btn-success" [routerLink]="['', 'benchmark']">
            <i class="fa-light fa-share"></i>
            <span class="hidden md:inline">
                {{ 'translate_benchmarks_list' | translate }}
            </span>
        </a>

        <!-- New -->
        <a
            *appHasPermissionId="permissionList.benchmarkWrite"
            class="btn btn-sm btn-primary"
            [routerLink]="['', 'benchmark', 'new']"
        >
            <i class="fa-light fa-plus"></i>
            <span class="hidden md:inline">
                {{ 'translate_add_new' | translate }}
            </span>
        </a>

        <!-- Edit -->
        <a
            *appHasPermissionId="permissionList.benchmarkWrite"
            class="btn btn-sm btn-info"
            [routerLink]="['', 'benchmark', 'edit', benchmarkId]"
            [appTooltip]="'translate_edit' | translate"
        >
            <i class="fa-light fa-edit"></i>
            <span class="hidden md:inline">
                {{ 'translate_edit' | translate }}
            </span>
        </a>
    </ng-container>

    <ng-container content>
        <!-- Basic info + coordinator + strategic goals -->
        <div class="mb-5 flex flex-col gap-5 md:flex-row">
            <!-- Basic info -->
            <app-content class="flex-1">
                <table content>
                    <tbody>
                        <!-- Visit date -->
                        <tr>
                            <td>{{ 'translate_visit_date' | translate }}</td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{
                                        benchmark.visitDate
                                            | date : 'yyyy-MMM-dd'
                                    }}
                                </span>
                            </td>
                        </tr>

                        <!-- Language -->
                        <tr>
                            <td>
                                {{ 'translate_preferred_language' | translate }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{
                                        benchmark.language
                                            | translateItem
                                                : 'benchmark-language'
                                            | async
                                    }}
                                </span>
                            </td>
                        </tr>

                        <!-- Type -->
                        <tr>
                            <td>
                                {{ 'translate_benchmark_type' | translate }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{
                                        benchmark.type
                                            | translateItem : 'benchmark-type'
                                            | async
                                    }}
                                </span>
                            </td>
                        </tr>

                        <!-- Method -->
                        <tr>
                            <td>
                                {{
                                    'translate_benchmark_methodology'
                                        | translate
                                }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{
                                        benchmark.method
                                            | translateItem : 'benchmark-method'
                                            | async
                                    }}
                                </span>
                            </td>
                        </tr>

                        <!-- Management type -->
                        <tr>
                            <td>
                                {{ 'translate_management_type' | translate }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{
                                        benchmark.managementType
                                            | translateItem
                                                : 'benchmark-management-type'
                                            | async
                                    }}
                                </span>
                            </td>
                        </tr>

                        <!-- Entity type -->
                        <tr>
                            <td>
                                {{
                                    'translate_benchmark_entity_type'
                                        | translate
                                }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{
                                        benchmark.entityType
                                            | translateItem
                                                : 'benchmark-entity-type'
                                            | async
                                    }}
                                </span>
                            </td>
                        </tr>

                        <!-- Entity name -->
                        <tr>
                            <td>
                                {{ 'translate_entity_name' | translate }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{
                                        benchmark.partner?.name ??
                                            benchmark.entityName
                                    }}
                                </span>
                            </td>
                        </tr>

                        <!-- Department -->
                        <tr>
                            <td>
                                {{ 'translate_department' | translate }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{ benchmark.department.name }}
                                </span>
                            </td>
                        </tr>

                        <!-- Benefit Rate -->
                        <tr *ngIf="optionalFields.benefitRate">
                            <td>
                                {{ 'translate_benefit_rate' | translate }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    % {{ benchmark.benefitRate }}
                                </span>
                            </td>
                        </tr>

                        <!-- Compared With -->
                        <tr *ngIf="optionalFields.isPreviouslyCompared">
                            <td>
                                {{ 'translate_compared_with' | translate }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{ benchmark.comparedWith }}
                                </span>
                            </td>
                        </tr>

                        <!-- Agendas -->
                        <tr>
                            <td>
                                {{
                                    'translate_visit_agenda_and_topics_to_be_discussed'
                                        | translate
                                }}
                            </td>
                            <td>
                                <span *appWaitUntilLoaded="benchmark">
                                    {{ benchmark.agenda }}
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </app-content>

            <div class="flex flex-1 flex-col gap-5">
                <!-- Coordinator info -->
                <app-content
                    contentTitle="{{
                        'translate_visit_coordinator' | translate
                    }}"
                >
                    <table content>
                        <tbody>
                            <!-- Coordinator employee number -->
                            <tr>
                                <td>
                                    {{
                                        'translate_employee_number' | translate
                                    }}
                                </td>
                                <td>
                                    <span *appWaitUntilLoaded="benchmark">
                                        {{
                                            benchmark.coordinatorEmployeeNumber
                                        }}
                                    </span>
                                </td>
                            </tr>

                            <!-- Coordinator full name -->
                            <tr>
                                <td>
                                    {{ 'translate_full_name' | translate }}
                                </td>
                                <td>
                                    <span *appWaitUntilLoaded="benchmark">
                                        {{ benchmark.coordinatorFullName }}
                                    </span>
                                </td>
                            </tr>

                            <!-- Coordinator email -->
                            <tr>
                                <td>
                                    {{ 'translate_email' | translate }}
                                </td>
                                <td>
                                    <span *appWaitUntilLoaded="benchmark">
                                        {{ benchmark.coordinatorEmail }}
                                    </span>
                                </td>
                            </tr>

                            <!-- Coordinator phone -->
                            <tr>
                                <td>
                                    {{ 'translate_phone' | translate }}
                                </td>
                                <td>
                                    <span *appWaitUntilLoaded="benchmark">
                                        {{ benchmark.coordinatorPhone }}
                                    </span>
                                </td>
                            </tr>

                            <!-- Coordinator office number -->
                            <tr>
                                <td>
                                    {{ 'translate_office_number' | translate }}
                                </td>
                                <td>
                                    <span *appWaitUntilLoaded="benchmark">
                                        {{ benchmark.coordinatorOfficeNumber }}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </app-content>

                <!-- Strategic goals -->
                <app-content
                    contentTitle="{{ 'translate_strategic_goals' | translate }}"
                >
                    <table content *appWaitUntilLoaded="benchmark">
                        <tbody>
                            <tr *ngFor="let item of benchmark.goals">
                                <td>{{ item.name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </app-content>
            </div>
        </div>

        <!-- Reason For Comparison + Current Performance Summary -->
        <div
            *ngIf="
                optionalFields.reasonForComparison ||
                optionalFields.currentPerformanceSummary
            "
            class="mb-5 flex flex-col gap-5 md:flex-row"
        >
            <!-- Reason For Comparison-->
            <app-content
                *ngIf="optionalFields.reasonForComparison"
                contentTitle="{{
                    'translate_reason_for_comparison' | translate
                }}"
                class="flex-1"
            >
                <table content *appWaitUntilLoaded="benchmark">
                    <tbody>
                        <tr *ngIf="benchmark.reasonForComparison">
                            <td style="white-space: pre-line">
                                {{ benchmark.reasonForComparison }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </app-content>

            <!-- Current Performance Summary-->
            <app-content
                *ngIf="optionalFields.currentPerformanceSummary"
                contentTitle="{{
                    'translate_current_performance_summary' | translate
                }}"
                class="flex-1"
            >
                <table content class="table-bordered table-striped table">
                    <tbody>
                        <tr *appWaitUntilLoaded="benchmark">
                            <td style="white-space: pre-line">
                                {{ benchmark.currentPerformanceSummary }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </app-content>
        </div>

        <!-- Request and selection reasons -->
        <div class="mb-5 flex flex-col gap-5 md:flex-row">
            <!-- Request reasons-->
            <app-content
                contentTitle="{{
                    'translate_reasons_for_requesting_benchmark' | translate
                }}"
                class="flex-1"
            >
                <table content *appWaitUntilLoaded="benchmark">
                    <tbody>
                        <tr *ngFor="let item of benchmark.requestReasons">
                            <td>{{ item.name }}</td>
                        </tr>
                        <tr *ngIf="benchmark.otherRequestReasons">
                            <td>
                                {{ benchmark.otherRequestReasons }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </app-content>

            <!-- Selection reasons-->
            <app-content
                contentTitle="{{
                    'translate_reasons_for_entity_selection' | translate
                }}"
                class="flex-1"
            >
                <app-list-loading content [items]="benchmark?.selectionReasons">
                    <table *appWaitUntilLoaded="benchmark">
                        <tbody>
                            <tr *ngFor="let item of benchmark.selectionReasons">
                                <td>{{ item.name }}</td>
                            </tr>
                            <tr *ngIf="benchmark.otherSelectionReasons">
                                <td>
                                    {{ benchmark.otherSelectionReasons }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </app-list-loading>
            </app-content>
        </div>

        <!-- benchmark Points -->
        <app-content
            *ngIf="optionalFields.benchmarkPoints"
            class="mb-5"
            contentTitle="{{ 'translate_benchmark_points' | translate }}"
        >
            <table content class="table-bordered table-striped table">
                <tbody>
                    <tr *appWaitUntilLoaded="benchmark">
                        <td style="white-space: pre-line">
                            {{ benchmark.benchmarkPoints }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>
        <!-- Operations -->
        <app-content
            class="mb-5"
            *ngIf="benchmark && benchmark.managementType === 'operation'"
            contentTitle="{{ 'translate_operations' | translate }}"
        >
            <app-list-loading content [items]="benchmark?.operations">
                <table class="table-bordered table-striped table">
                    <tbody>
                        <tr *ngFor="let item of benchmark.operations">
                            <td>
                                <a
                                    *appHasPermissionId="
                                        permissionList.operationRead;
                                        else justNameTemplateRef
                                    "
                                    [routerLink]="[
                                        '',
                                        'operation',
                                        'detail',
                                        item.id
                                    ]"
                                >
                                    {{ item.name }}
                                </a>
                                <ng-template #justNameTemplateRef>
                                    {{ item.name }}
                                </ng-template>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </app-list-loading>
        </app-content>

        <!-- opportunity -->
        <app-content
            class="mb-5"
            contentTitle="{{ 'translate_opportunities' | translate }}"
        >
            <app-list-loading
                content
                [items]="benchmark?.improvementOpportunities"
            >
                <div class="table-responsive">
                    <table class="table-bordered table-striped table">
                        <thead>
                            <th>{{ 'translate_progress' | translate }}</th>
                            <th>
                                {{ 'translate_inputs_description' | translate }}
                            </th>
                            <th>{{ 'translate_assigned' | translate }}</th>
                            <th>
                                {{ 'translate_inputs_sources' | translate }}
                            </th>
                            <th>
                                {{ 'translate_closing_date' | translate }}
                            </th>
                            <th style="width: 0%">
                                <i class="fa-light fa-gear"></i>
                            </th>
                        </thead>
                        <tbody>
                            <tr
                                *ngFor="
                                    let item of benchmark?.improvementOpportunities
                                "
                            >
                                <!-- Completion Rate -->
                                <td class="text-center">
                                    {{ item.completionRate }}
                                </td>

                                <!-- Inputs Description -->
                                <td>
                                    <span class="line-clamp-3 text-opacity-80">
                                        {{ item.inputsDescription }}
                                    </span>
                                </td>

                                <!-- Assigned -->
                                <td>
                                    <app-assigned-entity
                                        [item]="item"
                                    ></app-assigned-entity>
                                </td>

                                <!-- Input Source -->
                                <td class="text-center">
                                    <span
                                        *ngFor="
                                            let inputSource of item.inputSources
                                        "
                                        class="mr-2 rounded-md bg-primary-100 px-2.5 py-1 text-sm font-medium text-primary-800"
                                    >
                                        {{ inputSource.name }}
                                    </span>
                                </td>

                                <!-- Planned closing Date -->
                                <td class="text-center">
                                    {{
                                        item.plannedClosingDate
                                            | date : 'yyyy-MM-dd'
                                    }}
                                </td>

                                <td>
                                    <!-- View link -->
                                    <a
                                        [routerLink]="[
                                            '',
                                            'opportunity',
                                            'detail',
                                            item.id
                                        ]"
                                        class="btn btn-sm btn-success"
                                        [appTooltip]="
                                            'translate_show' | translate
                                        "
                                    >
                                        <i class="fa-light fa-eye fa-fw"></i>
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </app-list-loading>
        </app-content>

        <!-- Other management -->
        <app-content
            class="mb-5"
            *ngIf="benchmark && benchmark.managementType === 'other'"
            contentTitle="{{ 'translate_other_management' | translate }}"
        >
            <table content class="table-bordered table-striped table">
                <tbody>
                    <tr *ngFor="let item of benchmark.otherManagements">
                        <td>
                            {{
                                item.type
                                    | translateItem
                                        : 'benchmark-other-management-type'
                                    | async
                            }}
                        </td>
                        <td>
                            {{ item.detail }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <!-- Visitor -->
        <app-content
            class="mb-5"
            contentTitle="{{ 'translate_visitor_details' | translate }}"
        >
            <app-list-loading content [items]="benchmark?.visitors">
                <div class="table-responsive">
                    <table class="table-bordered table-striped table">
                        <thead>
                            <th>
                                {{ 'translate_employee_number' | translate }}
                            </th>
                            <th>{{ 'translate_rank' | translate }}</th>
                            <th>{{ 'translate_full_name' | translate }}</th>
                            <th>{{ 'translate_department' | translate }}</th>
                            <th>
                                {{ 'translate_employment_title' | translate }}
                            </th>
                            <th>{{ 'translate_description' | translate }}</th>
                            <th>{{ 'translate_phone' | translate }}</th>
                            <th>{{ 'translate_email' | translate }}</th>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of benchmark?.visitors">
                                <td>{{ item.employeeNumber }}</td>
                                <td>{{ item.rank }}</td>
                                <td>{{ item.fullName }}</td>
                                <td>{{ item.department }}</td>
                                <td>{{ item.employmentTitle }}</td>
                                <td>{{ item.description }}</td>
                                <td>{{ item.phone }}</td>
                                <td>{{ item.email }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </app-list-loading>
        </app-content>

        <!-- Kpi results -->
        <app-content
            contentTitle="{{ 'translate_required_kpi_results' | translate }}"
        >
            <app-list-loading content [items]="benchmark?.kpiResults">
                <div class="table-responsive">
                    <table class="table-bordered table-striped table">
                        <thead>
                            <th>
                                {{ 'translate_year' | translate }}
                            </th>
                            <th>{{ 'translate_kpi_name' | translate }}</th>
                            <th>{{ 'translate_target' | translate }}</th>
                            <th>{{ 'translate_result' | translate }}</th>
                            <th>
                                {{ 'translate_achieved' | translate }}
                            </th>
                            <th>
                                {{ 'translate_partner_result' | translate }}
                            </th>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of benchmark?.kpiResults">
                                <td>{{ item.result.year }}</td>
                                <td>{{ item.result.kpi.name }}</td>
                                <td>{{ item.result.target }}</td>
                                <td>{{ item.result.result | round : 2 }}</td>
                                <td>
                                    {{
                                        item.result.achieved * 100 | round : 2
                                    }}%
                                </td>
                                <td>{{ item.partnerResult }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </app-list-loading>
        </app-content>
    </ng-container>
</app-page>
