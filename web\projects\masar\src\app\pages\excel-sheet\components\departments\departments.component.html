<div class="flex h-full items-stretch">
    <!-- List -->
    <div
        class="flex w-1/2 flex-col items-center justify-center border-e px-2 py-4"
    >
        <div class="w-full overflow-auto">
            <h2 class="mb-3 px-2 text-xl font-bold">الإدارات</h2>

            <!-- Item -->
            <div
                class="bg-blur mb-2 flex items-center justify-between rounded-full p-4"
                *ngFor="let department of departments"
            >
                <div class="text-sm">
                    <p class="line-clamp-1" [title]="department.name">
                        {{ department.name }}
                    </p>
                </div>
                <button
                    class="rounded bg-red-500 px-2 text-xs text-white"
                    type="button"
                    (click)="deleteDepartment(department.id)"
                >
                    X
                </button>
            </div>

            <!-- No Items -->
            <ng-container *ngIf="!departments.length">
                <!-- Is Loading -->
                <div class="loading text-center" *ngIf="isLoadingDepartments">
                    <div class="lds-dual-ring"></div>
                </div>

                <!-- No Items Found -->
                <p
                    class="my-2 text-sm italic text-gray-400"
                    *ngIf="!isLoadingDepartments"
                >
                    لا يوجد عناصر
                </p>
            </ng-container>
        </div>
    </div>

    <!-- Create -->
    <form
        class="flex w-1/2 flex-col items-center justify-center px-2 py-4"
        [formGroup]="departmentForm"
        (ngSubmit)="createDepartment()"
        autocomplete="off"
    >
        <h2 class="mb-3 px-2 text-xl font-bold">إنشاء إدارة جديدة</h2>
        <input
            type="text"
            id="department_name"
            formControlName="name"
            class="bg-blur mb-2 w-full rounded-full border-none"
            placeholder="اسم الادارة"
        />
        <button
            type="submit"
            class="bg-blur w-full rounded-full p-3"
            [disabled]="isSubmittingDepartments"
        >
            انشاء
        </button>
    </form>
</div>
