import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    Input,
    NgModuleRef,
    TemplateRef,
    ViewChild,
} from '@angular/core';
import { ModalService, NotificationService } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { finalize, tap } from 'rxjs/operators';
import { TableResult } from '@masar/common/misc/table';
import { Capability } from '@masar/common/models';
import { TranslateService } from '@ngx-translate/core';
import { CapabilityListFullComponent } from '../capability-list-full/capability-list-full.component';

@Component({
    selector: 'app-capability-linker',
    templateUrl: './capability-linker.component.html',
    styles: [':host {display: block}'],
})
export class CapabilityLinkerComponent implements AfterViewInit {
    @ViewChild('controlColumnTemplateRef')
    public controlColumnTemplateRef: TemplateRef<any>;

    @Input() public mode: 'default' | 'view' = 'default';
    @Input() public listLinkedCapabilitiesCallback: (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Capability>>;
    @Input() public listUnlinkedCapabilitiesCallback?: (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Capability>>;
    @Input() public linkingCallback: (fileId: string) => Observable<string>;
    @Input() public unlinkingCallback: (fileId: string) => Observable<string>;

    @Input() public orderCallback: (
        orderedItems: Capability[],
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<string>;

    @Input() public requiredPermissionForLinking: string;
    @Input() public requiredPermissionForUnlinking: string;

    @Input() public kpiResultFromYear: number = null;
    @Input() public kpiResultToYear: number = null;

    @ViewChild(CapabilityListFullComponent)
    private list: CapabilityListFullComponent;
    @ViewChild('selectButtonTemplate')
    private selectButtonTemplate: TemplateRef<any>;

    public currentlyProcessing = new Set<string>();
    private unlinkedListComponentModal: CapabilityListFullComponent;

    public constructor(
        private notificationService: NotificationService,
        private modalService: ModalService,
        private moduleRef: NgModuleRef<any>,
        private readonly translateService: TranslateService,
        private changeDetectorRef: ChangeDetectorRef
    ) {}

    public ngAfterViewInit(): void {
        this.list.alternateListCallback = filter => {
            return this.listLinkedCapabilitiesCallback(
                filter.data.keyword,
                filter.pageNumber,
                filter.pageSize
            );
        };

        this.list.orderCallback = this.orderCallback
            ? (orderedItems, filter) => {
                  return this.orderCallback(
                      orderedItems,
                      filter.data.keyword,
                      filter.pageNumber,
                      filter.pageSize
                  ).pipe(
                      tap(message =>
                          this.notificationService.notifySuccess(message)
                      )
                  );
              }
            : null;

        if (this.mode === 'default') {
            this.list.list.customFields = [
                {
                    name: '',
                    template: this.controlColumnTemplateRef,
                },
            ];
        }

        // Changing input programmatically does not
        // trigger ng on changes, should manually
        // call it.
        this.list.ngOnChanges({
            alternateListCallback: {
                previousValue: undefined,
                currentValue: this.list.alternateListCallback,
            } as any,
        });
        this.changeDetectorRef.detectChanges();
    }

    public linkCapability(item: Capability): void {
        this.currentlyProcessing.add(item.id);
        this.linkingCallback(item.id)
            .pipe(finalize(() => this.currentlyProcessing.delete(item.id)))
            .subscribe(message => {
                this.notificationService.notifySuccess(message);

                // Refresh the unlinked capability list.
                this.unlinkedListComponentModal.refreshItems();

                // Refresh the linked capability list.
                this.list.refreshItems();
            });
    }

    public unlinkCapability(capabilityId: string): void {
        this.currentlyProcessing.add(capabilityId);

        this.unlinkingCallback(capabilityId)
            .pipe(finalize(() => this.currentlyProcessing.delete(capabilityId)))
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.list.refreshItems(true);
            });
    }

    public async showCapabilityDialog(): Promise<void> {
        this.unlinkedListComponentModal = await this.modalService.show(
            CapabilityListFullComponent,
            {
                size: { width: '70%' },
                moduleRef: this.moduleRef,
                title: this.translateService.instant(
                    'translate_link_with_capabilities'
                ),
                beforeInit: c => {
                    c.shownFilters = ['keyword'];
                    c.alternateListCallback = this
                        .listUnlinkedCapabilitiesCallback
                        ? filter => {
                              return this.listUnlinkedCapabilitiesCallback(
                                  filter.data.keyword,
                                  filter.pageNumber,
                                  filter.pageSize
                              );
                          }
                        : null;
                },
                onDismiss: () => {
                    this.unlinkedListComponentModal = null;
                },
            }
        );

        this.unlinkedListComponentModal.list.shownFields = [
            'name',
            'year',
            'type',
        ];
        this.unlinkedListComponentModal.list.customFields = [
            {
                name: '',
                template: this.selectButtonTemplate,
            },
        ];
    }
}
