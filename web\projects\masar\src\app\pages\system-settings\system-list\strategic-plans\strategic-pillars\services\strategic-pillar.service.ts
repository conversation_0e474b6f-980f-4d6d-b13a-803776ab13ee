import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';
import { miscFunctions, Result } from 'mnm-webapp';
import { TableResult } from '@masar/common/misc/table';
import { StrategicPillar } from '@masar/common/models/strategic-pillar.model';

@Injectable({
    providedIn: 'root',
})
export class StrategicPillarService {
    private readonly baseUrl = `${environment.apiUrl}/strategic-pillar`;

    public constructor(private http: HttpClient) {}

    public list(
        keyword: string,
        pageNumber: number,
        pageSize: number = 20
    ): Observable<TableResult<StrategicPillar>> {
        let params = new HttpParams();
        params = params.append('keyword', keyword || '');
        params = params.append('pageNumber', `${pageNumber}`);
        params = params.append('pageSize', `${pageSize}`);

        return this.http
            .get<Result<TableResult<StrategicPillar>>>(this.baseUrl, {
                params,
            })
            .pipe(map(result => result.extra));
    }

    public create(data: StrategicPillar): Observable<StrategicPillar> {
        return this.http
            .post<Result<StrategicPillar>>(
                this.baseUrl,
                miscFunctions.objectToURLParams({
                    'strategicPillar': JSON.stringify(data),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(data: StrategicPillar): Observable<StrategicPillar> {
        return this.http
            .put<Result<StrategicPillar>>(
                this.baseUrl,
                miscFunctions.objectToURLParams({
                    'strategicPillar': JSON.stringify(data),
                })
            )
            .pipe(map(result => result.extra));
    }

    public get(id: string, forEdit: boolean): Observable<StrategicPillar> {
        return this.http
            .get<Result<StrategicPillar>>(`${this.baseUrl}/${id}`, {
                params: new HttpParams().append('forEdit', `${forEdit}`),
            })
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.http
            .delete<Result<any>>(`${this.baseUrl}/${id}`)
            .pipe(map(result => result.messages[0]));
    }
}
