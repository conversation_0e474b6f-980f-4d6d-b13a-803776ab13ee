import {
    Component,
    EventEmitter,
    Input,
    NgModuleRef,
    Output,
    Renderer2,
} from '@angular/core';
import { TableController } from '@masar/common/misc/table';
import { ModalService } from 'mnm-webapp';
import { takeUntil } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { NewFormComponent } from '@masar/features/dynamic-page/components/new-form/new-form.component';
import { FormBuilder } from '@angular/forms';
import { MnmFormState } from '@masar/shared/components';
import { MiscApiService } from '@masar/core/services';
import { ListSectionData } from '@masar/features/dynamic-page/interfaces';

@Component({
    selector: 'app-list-section',
    templateUrl: './list-section.component.html',
})
export class ListSectionComponent {
    @Input() public data: ListSectionData<
        unknown,
        unknown & { submitted: EventEmitter<void> }
    >;

    @Input() public newComponentInputs?: { [key: string]: any };

    @Input() public item: unknown & { id: string };

    @Input() public editIdPropertyName?: string;

    @Input() public tableController: TableController<unknown, unknown>;

    @Input() public getForEditCb?: <T>(id: string) => Observable<T>;

    @Input() public createCb: <T>(item: T) => Observable<T>;

    @Input() public updateCb?: <T>(item: T) => Observable<T>;

    @Output() public delete = new EventEmitter<string>();

    @Output() public submitted = new EventEmitter<void>();

    public constructor(
        private readonly modalService: ModalService,
        private readonly moduleReference: NgModuleRef<any>,
        private readonly translateService: TranslateService,
        private readonly formBuilder: FormBuilder,
        private readonly miscApiService: MiscApiService,
        private readonly renderer: Renderer2
    ) {}

    public async showCreationDialog(id?: string): Promise<void> {
        if (!this.data.newComponent && !this.data.newForm) return;

        // TODO: Add option in mnm-webapp library to add custom class to the modal container via ModalOptions
        if (this.data.newForm?.isOverflowVisible)
            this.renderer.addClass(document.body, 'modal-no-overlay');

        const subject = new Subject();
        const title = id ? this.data.editLabel : this.data.newLabel;
        const translatedTitle = await this.translateService.instant(title);
        const newComponent = this.data.newComponent ?? NewFormComponent;

        let newComponentInputs = { ...this.newComponentInputs };

        const componentReference = await this.modalService.show(newComponent, {
            moduleRef: this.moduleReference,
            title: translatedTitle,
            beforeInit: c => {
                Object.assign(c, newComponentInputs);

                if (id) Object.assign(c, { [this.editIdPropertyName]: id });

                if (this.data.newForm) {
                    const formState = new MnmFormState(
                        this.data.newForm.fields(),
                        this.formBuilder
                    );

                    c['id'] = id;
                    c['formState'] = formState;
                    c['getForEditCb'] = this.getForEditCb;
                    c['createCb'] = this.createCb;
                    c['updateCb'] = this.updateCb;

                    if (this.data.newForm.misc) {
                        this.miscApiService.setMiscItems(
                            formState,
                            this.data.newForm.misc
                        );
                    }
                }
            },
            onDismiss: () => {
                subject.next();
                subject.complete();

                // TODO: Add option in mnm-webapp library to add custom class to the modal container via ModalOptions
                if (this.data.newForm?.isOverflowVisible)
                    this.renderer.removeClass(
                        document.body,
                        'modal-no-overlay'
                    );
            },
        });

        componentReference.submitted.pipe(takeUntil(subject)).subscribe(() => {
            this.submitted.emit();
            this.tableController.filter$.next();
            this.modalService.dismiss(componentReference);
        });
    }
}
