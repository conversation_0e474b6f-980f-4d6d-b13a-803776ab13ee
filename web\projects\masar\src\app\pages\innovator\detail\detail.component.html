<app-page pageTitle="{{ 'translate_innovator_profile' | translate }}">
    <!-- Tools -->
    <ng-container tools *appHasPermissionId="permissionList.innovation">
        <!-- List -->
        <a class="btn btn-sm btn-success" [routerLink]="['', 'innovator']">
            <i class="fa-light fa-share"></i>
            <span class="hidden md:inline">
                {{ 'translate_innovators_list' | translate }}
            </span>
        </a>

        <!-- New -->
        <a
            class="btn btn-sm btn-primary"
            [routerLink]="['', 'innovator', 'new']"
        >
            <i class="fa-light fa-plus"></i>
            <span class="hidden md:inline">
                {{ 'translate_add_new' | translate }}
            </span>
        </a>

        <!-- Edit -->
        <a
            class="btn btn-sm btn-info"
            *ngIf="innovator"
            [routerLink]="['', 'innovator', 'edit', innovator.id]"
            [appTooltip]="'translate_edit' | translate"
        >
            <i class="fa-light fa-edit"></i>
            <span class="hidden md:inline">
                {{ 'translate_edit' | translate }}
            </span>
        </a>
    </ng-container>

    <div
        content
        class="grid grid-cols-1 gap-3 md:grid-cols-4"
        *ngIf="isInnovator !== false"
    >
        <!-- Innovator overview -->
        <app-innovator-overview
            [innovator]="innovator"
            class="col-span-4 block"
        ></app-innovator-overview>

        <!-- Innovator Details -->
        <app-content
            class="col-span-3"
            [contentTitle]="'translate_innovator_details' | translate"
        >
            <div content class="grid grid-cols-4">
                <div class="flex items-center justify-center">
                    <div
                        class="h-32 w-32 overflow-hidden rounded-full border-8 border-primary bg-white"
                    >
                        <app-image
                            *ngIf="innovator?.employeeNumber"
                            #avatar
                            [imageObservable]="
                                imageApiService.user(
                                    null,
                                    innovator.employeeNumber
                                )
                            "
                            style="height: 100%"
                        ></app-image>
                    </div>
                </div>

                <div class="col-span-3">
                    <table>
                        <tbody>
                            <!-- Employee Name -->
                            <tr>
                                <td>
                                    {{ 'translate_employee_name' | translate }}
                                </td>
                                <td>
                                    <ng-container
                                        *appWaitUntilLoaded="innovator"
                                    >
                                        {{ innovator.name }}
                                    </ng-container>
                                </td>
                            </tr>

                            <!-- Rank -->
                            <tr>
                                <td>
                                    {{ 'translate_rank' | translate }}
                                </td>
                                <td>
                                    <ng-container
                                        *appWaitUntilLoaded="innovator"
                                    >
                                        {{ innovator.rank }}
                                    </ng-container>
                                </td>
                            </tr>

                            <!-- Employee Number -->
                            <tr>
                                <td>
                                    {{
                                        'translate_employee_number' | translate
                                    }}
                                </td>
                                <td>
                                    <ng-container
                                        *appWaitUntilLoaded="innovator"
                                    >
                                        {{ innovator.employeeNumber }}
                                    </ng-container>
                                </td>
                            </tr>

                            <!-- Innovation Logo -->
                            <tr>
                                <td>
                                    {{
                                        'translate_innovation_logo' | translate
                                    }}
                                </td>
                                <td>
                                    <ng-container
                                        *appWaitUntilLoaded="innovator"
                                    >
                                        {{
                                            (innovator.hasALogo
                                                ? 'translate_has_a_logo'
                                                : 'translate_does_not_have_a_logo'
                                            ) | translate
                                        }}
                                    </ng-container>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </app-content>

        <!-- Innovation Logo -->
        <app-content [contentTitle]="'translate_innovation_logo' | translate">
            <!-- Toggle Logo -->
            <div *ngIf="innovator">
                <button
                    *appHasPermissionId="permissionList.innovation"
                    tools
                    class="btn btn-sm"
                    [class.btn-success]="!innovator.hasALogo"
                    [class.btn-danger]="innovator.hasALogo"
                    [disabled]="isTogglingLogo"
                    (confirm)="toggleLogo(innovator)"
                    [swal]="{
                        title: 'translate_innovation_toggle_logo' | translate,
                        confirmButtonText: 'translate_yes' | translate,
                        cancelButtonText: 'translate_cancel' | translate,
                        showCancelButton: true,
                        showCloseButton: true
                    }"
                >
                    <span>{{
                        (innovator?.hasALogo
                            ? 'translate_disable_innovation_logo'
                            : 'translate_enable_innovation_logo'
                        ) | translate
                    }}</span>
                </button>
            </div>

            <div content>
                <ng-container *appWaitUntilLoaded="innovator">
                    <div class="flex h-full w-full items-center justify-center">
                        <span
                            class="italic text-gray-500"
                            *ngIf="!innovator.hasALogo"
                        >
                            {{ 'translate_does_not_have_a_logo' | translate }}
                        </span>

                        <div
                            *ngIf="innovator.hasALogo"
                            class="mx-auto h-40 w-40"
                        >
                            <img
                                src="assets/img/innovator-logo.png"
                                [alt]="'translate_has_a_logo' | translate"
                                [title]="'translate_has_a_logo' | translate"
                                class="mx-auto h-full"
                            />
                        </div>
                    </div>
                </ng-container>
            </div>
        </app-content>

        <!-- Awards -->
        <app-content
            class="col-span-2"
            [contentTitle]="'translate_awards' | translate"
        >
            <!-- Tools -->
            <button
                tools
                [disabled]="!innovator"
                (click)="showAwardDialog()"
                class="btn btn-sm btn-outline-white"
            >
                <i class="fa-light fa-plus"></i>
                {{ 'translate_add_new_award' | translate }}
            </button>

            <div content>
                <ng-container *appWaitUntilLoaded="innovator">
                    <app-award-list
                        [awards]="innovator?.awards"
                        (editAward)="showAwardDialog($event)"
                        (deleteAward)="getInnovator()"
                    ></app-award-list>
                </ng-container>
            </div>
        </app-content>

        <!-- Training Programs -->
        <app-content
            class="col-span-2"
            [contentTitle]="'translate_training_programs' | translate"
        >
            <!-- Tools -->
            <button
                tools
                [disabled]="!innovator"
                (click)="showTrainingProgramDialog()"
                class="btn btn-sm btn-outline-white"
            >
                <i class="fa-light fa-plus"></i>
                {{ 'translate_add_new_training_program' | translate }}
            </button>

            <div content>
                <ng-container *appWaitUntilLoaded="innovator">
                    <app-training-program-list
                        [trainingPrograms]="innovator?.trainingPrograms"
                        (editTrainingProgram)="
                            showTrainingProgramDialog($event)
                        "
                        (deleteTrainingProgram)="getInnovator()"
                    ></app-training-program-list>
                </ng-container>
            </div>
        </app-content>

        <!-- Innovations -->
        <app-content
            class="col-span-4"
            [contentTitle]="'translate_innovations' | translate"
        >
            <!-- Tools -->
            <a
                tools
                [routerLink]="['', 'innovation', 'new']"
                class="btn btn-sm btn-outline-white"
            >
                <i class="fa-light fa-plus"></i>
                {{ 'translate_add_new_innovation' | translate }}
            </a>

            <div content>
                <ng-container *appWaitUntilLoaded="innovator">
                    <table>
                        <thead>
                            <tr>
                                <th>
                                    {{
                                        'translate_innovation_title' | translate
                                    }}
                                </th>
                                <th>{{ 'translate_year' | translate }}</th>
                                <th>
                                    {{
                                        'translate_suggestion_number'
                                            | translate
                                    }}
                                </th>
                                <th
                                    *appHasPermissionId="
                                        permissionList.innovation
                                    "
                                ></th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr *ngFor="let item of innovator.innovations">
                                <!-- Title -->
                                <td>
                                    <a
                                        [routerLink]="[
                                            '',
                                            'innovation',
                                            'detail',
                                            item.id
                                        ]"
                                    >
                                        {{ item.title }}
                                    </a>
                                </td>

                                <!-- Year -->
                                <td class="text-center">{{ item.year }}</td>

                                <!-- Suggestion Number -->
                                <td class="text-center">
                                    {{ item.suggestionNumber }}
                                </td>

                                <!-- Links & Buttons -->
                                <td
                                    class="whitespace-nowrap"
                                    *appHasPermissionId="
                                        permissionList.innovation
                                    "
                                >
                                    <!-- Edit -->
                                    <a
                                        [routerLink]="[
                                            '',
                                            'innovation',
                                            'edit',
                                            item.id
                                        ]"
                                        [appTooltip]="
                                            'translate_edit' | translate
                                        "
                                        class="btn btn-sm btn-info me-2"
                                    >
                                        <i class="fa-light fa-edit"></i>
                                    </a>

                                    <!-- Delete -->
                                    <button
                                        class="btn btn-sm btn-danger me-2"
                                        [disabled]="
                                            currentlyDeletingInnovations.includes(
                                                item.id
                                            )
                                        "
                                        [appTooltip]="
                                            'translate_delete' | translate
                                        "
                                        (confirm)="deleteInnovation(item)"
                                        [swal]="{
                                            title:
                                                'translate_delete_this_item_question_mark'
                                                | translate,
                                            confirmButtonText:
                                                'translate_yes' | translate,
                                            cancelButtonText:
                                                'translate_cancel' | translate,
                                            showCancelButton: true,
                                            showCloseButton: true
                                        }"
                                    >
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>

                            <tr
                                *ngIf="
                                    innovator?.innovations &&
                                    !innovator.innovations.length
                                "
                            >
                                <td colspan="5" class="text-center">
                                    <span class="text-gray-500">
                                        {{ 'translate_not_found' | translate }}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </ng-container>
            </div>
        </app-content>
    </div>

    <div content *ngIf="isInnovator === false">
        <app-alert
            mode="warning"
            label="translate_you_are_not_innovator_title"
            description="translate_you_are_not_innovator_text"
        ></app-alert>
    </div>
</app-page>
