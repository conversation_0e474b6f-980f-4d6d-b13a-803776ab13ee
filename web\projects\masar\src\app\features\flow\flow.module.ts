import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
    FlowMoveButtonComponent,
    FlowMoveFormComponent,
    FlowTransactionHistoryComponent,
} from '@masar/features/flow/components';
import { SharedModule } from '@masar/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { FlowService } from '@masar/features/flow/services/flow.service';
import { MasarModule } from '@masar/features/masar/masar.module';
import { FormatDistanceToNowPipeModule } from 'ngx-date-fns';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';

@NgModule({
    imports: [
        CommonModule,
        SharedModule,
        TranslateModule,
        MasarModule,
        FormatDistanceToNowPipeModule,
        SweetAlert2Module,
    ],
    declarations: [
        FlowMoveButtonComponent,
        FlowMoveFormComponent,
        FlowTransactionHistoryComponent,
    ],
    exports: [FlowMoveButtonComponent, FlowTransactionHistoryComponent],
    providers: [FlowService],
})
export class FlowModule {}
