import { Item, Team, User } from './index';

export interface LibraryFile {
    id: string;
    name: string;
    fileSize?: number;
    nameAr: string;
    nameEn: string;
    contentType: string;
    link: string;
    hasFile: boolean;
    team: Team;
    owner: User;
    latestModificationTime: string;
    latestModificationBy: string;
    usageCount: number;
    tags: Item[];
    creationTime: Date;
}
