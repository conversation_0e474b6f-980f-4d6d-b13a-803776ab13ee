import { Component, OnDestroy } from '@angular/core';
import { Center } from '../centers/centers.component';
import { forkJoin, Subscription } from 'rxjs';
import { Department } from '../departments/departments.component';
import { DataEntry } from '../data-entry/data-entry.component';
import { Service } from '../services/services.component';
import { Chart } from 'chart.js';
import { ExcelSheetService } from '../../excel-sheet.service';
import { finalize } from 'rxjs/operators';

@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
})
export class DashboardComponent implements OnDestroy {
    public centers: Center[] = [];
    public services: Service[] = [];
    public departments: Department[] = [];
    public dataEntries: DataEntry[] = [];

    public isLoadingEntries = true;

    public selectedDepartmentId = 0;

    private subs$: Subscription[] = [];

    public constructor(private readonly api: ExcelSheetService) {
        this.getData();
        Chart.defaults.global.defaultFontFamily = "'Cairo', sans-serif";
        Chart.defaults.global.defaultFontSize = 14;
        Chart.defaults.global.plugins.datalabels = { font: { size: 10 } };
    }

    public ngOnDestroy(): void {
        this.subs$.forEach(sub => sub.unsubscribe());
    }

    public getDataEntries(): void {
        if (this.selectedDepartmentId === 0) return;

        this.isLoadingEntries = true;

        this.api
            .getDataEntries(`?departmentId=${this.selectedDepartmentId}`)
            .pipe(finalize(() => (this.isLoadingEntries = false)))
            .subscribe(dataEntries => (this.dataEntries = dataEntries));
    }

    private getData(): void {
        const sub$ = forkJoin([
            this.api.getCenters(),
            this.api.getServices(),
            this.api.getDepartments(),
        ]).subscribe(([centers, services, departments]) => {
            this.centers = centers;
            this.departments = departments;
            this.services = services;
            this.selectedDepartmentId = departments[0]?.id || 0;
            this.getDataEntries();
        });

        this.subs$.push(sub$);
    }
}
