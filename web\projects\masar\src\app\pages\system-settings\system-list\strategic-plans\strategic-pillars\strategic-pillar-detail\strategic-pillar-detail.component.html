<app-page pageTitle="{{ 'translate_strategic_pillar_details' | translate }}">
    <!-- Tools -->
    <ng-container tools>
        <a
            class="btn btn-sm btn-white flex items-center gap-1"
            [routerLink]="[
                '',
                'system-list',
                'strategic-plans',
                'strategic-pillar'
            ]"
            ><i class="fas fa-left"></i> {{ 'translate_back' | translate }}</a
        >
        <a
            class="btn btn-sm btn-primary"
            [routerLink]="[
                '',
                'system-list',
                'strategic-plans',
                'strategic-pillar',
                'new'
            ]"
            ><i class="fa-light fa-plus"></i>
            {{ 'translate_add_new' | translate }}</a
        >
        <a
            class="btn btn-sm btn-info"
            *ngIf="item"
            [routerLink]="[
                '',
                'system-list',
                'strategic-plans',
                'strategic-pillar',
                'edit',
                item.id
            ]"
            ><i class="fa-light fa-edit"></i>
            {{ 'translate_edit' | translate }}</a
        >
    </ng-container>

    <!-- Content -->
    <ng-container content>
        <!-- Details -->
        <app-content class="mb-5">
            <table content>
                <tbody>
                    <!-- Name -->
                    <tr>
                        <td>
                            {{ 'translate_name' | translate }}
                        </td>
                        <td>
                            <app-value-loading
                                [isLoading]="!item"
                                [value]="item?.name"
                            ></app-value-loading>
                        </td>
                    </tr>
                    <!-- Icon -->
                    <tr *ngIf="item?.iconClass">
                        <td>
                            {{ 'translate_icon_class' | translate }}
                        </td>
                        <td>
                            <i class="{{ item?.iconClass }}"></i>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>
    </ng-container>
</app-page>
