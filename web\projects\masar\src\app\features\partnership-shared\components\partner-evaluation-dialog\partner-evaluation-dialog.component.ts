import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { PartnerEvaluationService } from './partner-evaluation.service';
import { PartnershipContractPartnerEvaluationDto } from '@masar/pages/partnership-contract/dto';
import { finalize } from 'rxjs/operators';
import { ActionDialogData } from '@masar/features/dynamic-page/types';

@Component({
    templateUrl: './partner-evaluation-dialog.component.html',
    providers: [PartnerEvaluationService],
})
export class PartnerEvaluationDialogComponent implements OnInit {
    @Input() public id?: string;

    @Input() public data?: ActionDialogData;

    @Output() public submitted = new EventEmitter<void>();

    public isSubmitting = false;

    public isEvaluatedAlready = true;
    public totalTarget: number;
    public partnershipContractPartnerEvaluations: PartnershipContractPartnerEvaluationDto[] =
        [];

    public constructor(
        private readonly partnerEvaluationService: PartnerEvaluationService
    ) {}

    public ngOnInit(): void {
        this.partnerEvaluationService.getRating(this.id).subscribe(list => {
            this.partnershipContractPartnerEvaluations = list;
            this.totalTarget = list.reduce(
                (total, partner) => total + partner?.target,
                0
            );
            this.isEvaluatedAlready = list.every(x => x.value !== null);
        });
    }

    public submit(): void {
        const isExceed = this.partnershipContractPartnerEvaluations.some(
            item => item.value > item.target
        );

        if (isExceed) return;

        this.isSubmitting = true;

        this.partnerEvaluationService
            .updateRating(this.id, this.partnershipContractPartnerEvaluations)
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(() => this.submitted.emit());
    }
}
