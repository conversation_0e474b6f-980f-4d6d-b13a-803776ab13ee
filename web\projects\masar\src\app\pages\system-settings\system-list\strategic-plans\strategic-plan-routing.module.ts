import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { StrategicPlanComponent } from './strategic-plan.component';
import { ListComponent } from './strategic-plan/list/list.component';
import { DetailComponent } from './strategic-plan/detail/detail.component';
import { StrategicPillarDetailComponent } from './strategic-pillars/strategic-pillar-detail/strategic-pillar-detail.component';
import { StrategicPillarListComponent } from './strategic-pillars/strategic-pillar-list/strategic-pillar-list.component';
import { NewComponent } from './strategic-plan/new/new.component';
import { StrategicPillarNewComponent } from './strategic-pillars/strategic-pillar-new/strategic-pillar-new.component';

const routes: Routes = [
    {
        path: '',
        component: StrategicPlanComponent,
        children: [
            {
                path: '',
                redirectTo: 'list',
                pathMatch: 'full',
            },
            {
                path: 'list',
                component: ListComponent,
                data: {
                    title: 'translate_strategic_plans',
                },
            },
            {
                path: 'new',
                component: NewComponent,
                data: {
                    title: 'translate_new_strategic_plan',
                },
            },
            {
                path: 'edit/:id',
                component: NewComponent,
                data: {
                    title: 'translate_edit_strategic_plan',
                },
            },
            {
                path: 'detail/:id',
                component: DetailComponent,
                data: {
                    title: 'translate_strategic_plan_details',
                },
            },
            // strategic pillar routes
            {
                path: 'strategic-pillar/detail/:id',
                component: StrategicPillarDetailComponent,
                data: {
                    title: 'translate_strategic_plan_details',
                },
            },
            {
                path: 'strategic-pillar',
                component: StrategicPillarListComponent,
                data: {
                    title: 'translate_strategic_plan_details',
                },
            },
            {
                path: 'strategic-pillar/edit/:id',
                component: StrategicPillarNewComponent,
                data: {
                    title: 'translate_strategic_plan_details',
                },
            },
            {
                path: 'strategic-pillar/new',
                component: StrategicPillarNewComponent,
                data: {
                    title: 'translate_strategic_plan_details',
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class StrategicPlanRoutingModule {}
