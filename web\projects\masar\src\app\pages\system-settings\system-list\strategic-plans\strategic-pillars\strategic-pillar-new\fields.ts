import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },

    {
        name: 'nameAr',
        type: 'text',
        label: 'translate_name_in_arabic',
        validators: [Validators.required],
    },

    {
        name: 'nameEn',
        type: 'text',
        label: 'translate_name_in_english',
    },
    {
        name: 'iconClass',
        type: 'text',
        label: 'translate_icon_class',
    },
];
