import {
    AfterViewInit,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { KpiResultDataEntryResponse } from '@masar/common/models';
import { MnmFormState } from '@masar/shared/components';
import { Subject } from 'rxjs';
import { finalize, takeUntil } from 'rxjs/operators';
import { KpiResultDataEntrySharedService } from '../../../kpi-result-data-entry-shared.service';
import { fields as periodFields } from './period-fields';
import { fields as moveFields } from './move-fields';
import { ActivatedRoute } from '@angular/router';
import { permissionList } from '@masar/common/constants';

@Component({
    selector: 'app-data-entry-response-detail',
    templateUrl: './data-entry-response-detail.component.html',
})
export class DataEntryResponseDetailComponent
    implements OnInit, AfterViewInit, OnDestroy
{
    @Input() public kpiResultDataEntryResponseId: string;
    @Input() public readOnly: boolean = false;

    @Output() public transferMove = new EventEmitter();

    @ViewChild('periodsRef') private periodsRef: ElementRef;

    // public kpiResultDataEntryResponseId: string;
    public item?: KpiResultDataEntryResponse;
    // public readOnly: boolean = true;
    public isSaving = false;
    public isMoving: 'transfer' | 'reject' | 'forward_to_kpi_manager' = null;
    public periodFormState: MnmFormState;
    public moveFormState: MnmFormState;
    public actionMode: 'save-and-move' | 'move-and-reject' | 'none' = 'none';
    public permissionList = permissionList;
    public showDirectApprovalButton: boolean = true;
    public currentLevel = '';
    public isReturnedFromKpiManager: boolean = false;
    private unsubscribeAll = new Subject();

    public constructor(
        private notificationService: NotificationService,
        private kpiResultDataEntrySharedService: KpiResultDataEntrySharedService,
        private translateService: TranslateService,
        private fb: FormBuilder,
        private readonly activatedRoute: ActivatedRoute
    ) {}

    public ngOnInit(): void {
        this.periodFormState = new MnmFormState(periodFields(), this.fb);
        this.moveFormState = new MnmFormState(moveFields(), this.fb);

        this.monitorForm();

        if (this.kpiResultDataEntryResponseId) this.getItem();

        this.activatedRoute.params
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(params => {
                if (params['id']) {
                    this.kpiResultDataEntryResponseId = params['id'];
                    this.getItem();
                }
            });
    }

    public ngAfterViewInit(): void {
        this.periodFormState.get('periods').customField = this.periodsRef;
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public getItem(): void {
        if (this.readOnly) this.getResponseAsKpiManager();
        else this.getResponse();
    }

    public getResponseAsKpiManager(): void {
        this.kpiResultDataEntrySharedService
            .getResponseAsKpiManager(this.kpiResultDataEntryResponseId, true)
            .subscribe(item => {
                this.item = item;
                this.fillPeriodForm(item);
                this.fillMoveForm(item);

                if (this.item.transfers[0]?.assignee === 'kpi_manager') {
                    this.actionMode = 'move-and-reject';
                    this.moveFormState.group.controls['notes'].enable();
                }
            });
    }

    public getResponse(): void {
        this.kpiResultDataEntrySharedService
            .get(this.kpiResultDataEntryResponseId, true)
            .subscribe(item => {
                this.item = item;
                this.fillPeriodForm(item);
                this.fillMoveForm(item);
                this.getNext();
                this.checkIfReturnedFromKpiManager(item);
            });
    }

    public getNext(): void {
        this.kpiResultDataEntrySharedService
            .getNext(this.kpiResultDataEntryResponseId)
            .subscribe(({ users, next }) => {
                this.moveFormState.get('users').items = users;
                this.currentLevel = next;
                switch (this.item.transfers[0]?.assignee) {
                    case 'data_entry':
                        this.actionMode = 'save-and-move';
                        break;
                    case 'done':
                        this.actionMode = 'none';
                        break;
                    default:
                        this.actionMode = 'move-and-reject';
                        break;
                }

                if (['level1', 'level2'].includes(next)) {
                    this.moveFormState.group.controls['users'].enable();
                }

                if (this.item.transfers[0]?.assignee !== 'done') {
                    this.moveFormState.group.controls['notes'].enable();
                }
            });
    }

    public save(withMove = false): void {
        this.periodFormState.setTriedToSubmit();
        if (this.periodFormState.group.invalid) return;

        if (withMove) {
            this.moveFormState.setTriedToSubmit();
            if (this.moveFormState.group.invalid) return;
        } else {
            this.isSaving = true;
        }

        this.kpiResultDataEntrySharedService
            .update(this.periodFormState.group.getRawValue())
            .pipe(finalize(() => (this.isSaving = false)))
            .subscribe(item => {
                this.item = item;
                this.fillPeriodForm(item);

                // XXX: Why fill in the move form? This line prevents users
                // from moving as it will always clear out the next user field.
                // this.fillMoveForm(item);

                this.translateService
                    .get('translate_item_updated_successfully')
                    .subscribe(str => {
                        this.notificationService.notifySuccess(str);
                    });

                if (withMove) this.move();
            });
    }

    public move(
        decision: 'transfer' | 'reject' | 'forward_to_kpi_manager' = 'transfer'
    ): void {
        this.moveFormState.setTriedToSubmit();
        if (this.moveFormState.group.invalid) return;

        this.isMoving = decision;

        this.kpiResultDataEntrySharedService
            .move(this.moveFormState.group.getRawValue(), decision)
            .pipe(finalize(() => (this.isMoving = null)))
            .subscribe(_ => {
                this.translateService
                    .get('translate_item_updated_successfully')
                    .subscribe(str => {
                        this.notificationService.notifySuccess(str);
                    });

                this.transferMove.emit();
            });
    }

    private fillPeriodForm(item: KpiResultDataEntryResponse): void {
        for (const key of Object.keys(this.periodFormState.group.controls)) {
            this.periodFormState.group.controls[key].setValue(item[key]);
        }
    }

    private fillMoveForm(item: KpiResultDataEntryResponse): void {
        for (const key of Object.keys(this.moveFormState.group.controls)) {
            this.moveFormState.group.controls[key].setValue(item[key]);
        }
    }

    private monitorForm(): void {
        this.moveFormState.group.controls['users'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                this.showDirectApprovalButton = !value || value.length === 0;
            });
    }

    private checkIfReturnedFromKpiManager(
        item: KpiResultDataEntryResponse
    ): void {
        if (item.transfers && item.transfers.length >= 2) {
            if (
                item.transfers[0]?.assignee === 'data_entry' &&
                item.transfers[1]?.assignee === 'kpi_manager'
            ) {
                this.isReturnedFromKpiManager = true;
            }
        }
    }
}
