import { StrategicPillar } from './strategic-pillar.model';
import { StrategicPlan } from './strategic-plan.model';
import { StrategicTheme } from './strategic-theme.model';

export interface StrategicGoal {
    id: string;
    code: string;
    name: string;
    nameAr: string;
    nameEn: string;
    description: string;
    descriptionAr: string;
    descriptionEn: string;
    order: number;
    category: string;
    fromYear: number;
    toYear: number;
    weight?: number;
    yearRange: string;
    theme: StrategicTheme;
    kpiCount: number;
    achieved: number;
    plan: StrategicPlan;
    pillar: StrategicPillar;
}
