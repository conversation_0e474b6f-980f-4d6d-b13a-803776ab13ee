import { Component, Input } from '@angular/core';
import { DetailPageData } from '@masar/features/dynamic-page/interfaces';

@Component({
    selector: 'app-detail-sections',
    templateUrl: './detail-sections.component.html',
    styles: [
        `
            @media (max-width: 768px) {
                .detail-section-content {
                    grid-column: span 12 !important;
                }
            }
        `,
    ],
})
export class DetailSectionsComponent {
    @Input() public sections!: DetailPageData<unknown>['sections'];

    @Input() public item?: unknown;
}
