<app-page
    pageTitle="{{
        (mode === 'new'
            ? 'translate_add_new_innovation'
            : 'translate_update_existing_innovation'
        ) | translate
    }}"
>
    <!-- Tools -->
    <ng-container tools *appHasPermissionId="permissionList.innovation">
        <!-- List -->
        <a
            [routerLink]="['', 'innovation']"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-share me-2"></i>
            <span>{{ 'translate_innovations_list' | translate }}</span>
        </a>

        <!-- Detail -->
        <a
            *ngIf="mode === 'edit'"
            [routerLink]="[
                '',
                'innovation',
                'detail',
                formState.group.controls['id'].value
            ]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-eye me-2"></i>
            <span>{{ 'translate_preview' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <mnm-form
        content
        *ngIf="formState && isInnovator !== false"
        [state]="formState"
        [translateLabels]="true"
    >
        <!-- Tools -->

        <div class="mt-2 text-center">
            <button
                type="submit"
                class="btn-lg btn btn-primary"
                (click)="submit()"
                [disabled]="isSubmitting"
            >
                <app-loading-ring
                    *ngIf="isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
    </mnm-form>

    <div content *ngIf="isInnovator === false">
        <app-alert
            mode="warning"
            label="translate_you_are_not_innovator_title"
            description="translate_you_are_not_innovator_text"
        ></app-alert>
    </div>
</app-page>
