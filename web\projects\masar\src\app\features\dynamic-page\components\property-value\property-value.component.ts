import { Component, Input, OnChanges } from '@angular/core';
import { getNestedProperty } from '@masar/common/utils';
import { PermissionValue } from '@masar/common/types';
import {
    ItemNavigate,
    PropertyValueOptions,
} from '@masar/features/dynamic-page/interfaces';

@Component({
    selector: 'app-property-value',
    templateUrl: './property-value.component.html',
})
export class PropertyValueComponent implements OnChanges {
    @Input() public item: any;

    @Input() public propertyValueOptions: PropertyValueOptions<unknown>;

    public property: {
        value?: any;
        itemNavigate?: {
            link: string | string[];
            permission?: PermissionValue;
        };
    } = {};

    public ngOnChanges(): void {
        if (!this.item) {
            this.property = {};
            return;
        }

        if (this.propertyValueOptions.property) {
            this.property.value = getNestedProperty(
                this.item,
                this.propertyValueOptions.property
            );

            if (
                Array.isArray(this.property.value) &&
                this.propertyValueOptions.nestedProperty
            ) {
                this.property.value = this.property.value.map(x =>
                    getNestedProperty(
                        x,
                        this.propertyValueOptions.nestedProperty
                    )
                );
            }
        }

        if (this.propertyValueOptions.valueFactory) {
            this.property.value = this.propertyValueOptions.valueFactory(
                this.item
            );
        }

        let itemNavigate: ItemNavigate;

        if (this.propertyValueOptions.itemNavigate) {
            itemNavigate = this.propertyValueOptions.itemNavigate;
        } else if (this.propertyValueOptions.itemNavigateFactory?.(this.item)) {
            itemNavigate = this.propertyValueOptions.itemNavigateFactory(
                this.item
            );
        }

        if (itemNavigate) {
            this.property.itemNavigate = {
                link: itemNavigate.link(
                    getNestedProperty(this.item, itemNavigate.targetIdProperty)
                ),
                permission: itemNavigate.permission,
            };
        }
    }
}
