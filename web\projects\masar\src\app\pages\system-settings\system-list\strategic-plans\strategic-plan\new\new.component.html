<app-page
    pageTitle="{{
        (mode === 'new'
            ? 'translate_add_new_strategic_plan'
            : 'translate_update_existing_strategic_plan'
        ) | translate
    }}"
>
    <!-- Tools -->
    <ng-container tools>
        <!-- List -->
        <a
            [routerLink]="['', 'system-list', 'strategic-plans']"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-share me-2"></i>
            <span>{{ 'translate_strategic_plans_list' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <mnm-form
        content
        *ngIf="formState"
        [state]="formState"
        [translateLabels]="true"
    >
        <div class="mt-2 text-center">
            <button
                type="submit"
                class="btn-lg btn btn-primary"
                (click)="submit()"
                [disabled]="isSubmitting"
            >
                <app-loading-ring
                    *ngIf="isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
    </mnm-form>
</app-page>

<!-- Strategic Values -->
<ng-template #strategicValuesFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['values'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="flex flex-col gap-2 md:flex-row">
                <div class="flex flex-1 flex-col justify-center gap-2">
                    <label>{{
                        'translate_value_name_in_arabic' | translate
                    }}</label>
                    <input
                        type="text"
                        [ngModel]="item.nameAr"
                        (ngModelChange)="item.nameAr = $event; emitChange()"
                        placeholder="{{
                            'translate_enter_value_name_in_arabic' | translate
                        }}"
                    />
                </div>
                <div class="flex flex-1 flex-col justify-center gap-2">
                    <label>{{
                        'translate_value_name_in_english' | translate
                    }}</label>
                    <input
                        type="text"
                        [ngModel]="item.nameEn"
                        (ngModelChange)="item.nameEn = $event; emitChange()"
                        placeholder="{{
                            'translate_enter_value_name_in_english' | translate
                        }}"
                    />
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>
