import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';
import { AppSettingFetcherService } from '@masar/core/services';

export const fields: (
    appSettingFetcherService: AppSettingFetcherService
) => MnmFormField[] = appSettingFetcherService => [
    {
        name: 'id',
        hide: true,
    },

    {
        fields: [
            {
                name: 'title',
                type: 'text',
                label: 'translate_innovation_title',
                size: 6,
                validators: [Validators.required],
            },
            {
                name: 'year',
                type: 'number',
                label: 'translate_year',
                size: 6,
            },
        ],
    },
    {
        fields: [
            {
                name: 'details',
                type: 'textarea',
                label: 'translate_innovation_description',
                size: 6,
            },
            {
                name: 'impact',
                type: 'textarea',
                label: 'translate_innovation_result',
                size: 6,
            },
        ],
    },
    {
        fields: [
            {
                name: 'innovator',
                type: 'select',
                label: 'translate_innovators',
                size: 6,
                bindLabel: 'name',
                validators: [Validators.required],
                isDisabled: true,
            },
            {
                name: 'activities',
                type: 'select',
                label: 'translate_activities',
                size: 6,
                bindLabel: 'name',
                multiple: true,
            },
        ],
    },

    {
        fields: [
            {
                name: 'suggestionNumber',
                type: 'number',
                label: 'translate_suggestion_number',
                size: 4,
            },
            {
                name: 'documentationType',
                type: 'text',
                label: 'translate_documentation_type',
                size: 4,
            },
            {
                name: 'file',
                type: 'file',
                label: 'translate_documentation',
                size: 4,
                mime: appSettingFetcherService.getAllowedFileTypes,
            },
        ],
    },
];
