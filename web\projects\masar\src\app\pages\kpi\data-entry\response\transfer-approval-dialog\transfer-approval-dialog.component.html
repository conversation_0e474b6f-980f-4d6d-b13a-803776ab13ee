<app-page [pageTitle]="'translate_transfer_approval' | translate">
    <div content class="grid gap-2 md:gap-4">
        <app-content [contentTitle]="'translate_current_approves' | translate">
            <table content>
                <tbody>
                    <!-- Year -->
                    <tr *ngFor="let user of currentApprovers">
                        <td class="whitespace-nowrap">
                            {{ 'translate_name' | translate }}
                        </td>
                        <td>
                            <ng-container
                                *appWaitUntilLoaded="currentApprovers"
                            >
                                {{ user.name }}
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <app-content [contentTitle]="'translate_action' | translate">
            <!-- Content -->
            <mnm-form
                content
                *ngIf="moveFormState"
                [state]="moveFormState"
                [translateLabels]="true"
            >
                <div class="mt-2 text-center">
                    <button
                        type="submit"
                        class="btn-lg btn btn-success me-2"
                        (confirm)="save()"
                        [swal]="{
                            title:
                                'translate_transfer_approval_question_mark'
                                | translate,
                            confirmButtonText: 'translate_yes' | translate,
                            cancelButtonText: 'translate_cancel' | translate,
                            showCancelButton: true,
                            showCloseButton: true
                        }"
                        [disabled]="isMoving"
                    >
                        <app-loading-ring
                            *ngIf="isMoving"
                            class="me-2"
                        ></app-loading-ring>
                        <i class="fa fa-check me-2"></i>
                        <span>{{ 'translate_transfer' | translate }}</span>
                    </button>

                    <button
                        type="button"
                        class="btn-lg btn btn-danger me-2"
                        (click)="emitTransferMove(false)"
                        [disabled]="isMoving"
                    >
                        <app-loading-ring
                            *ngIf="isMoving"
                            class="me-2"
                        ></app-loading-ring>
                        <i class="fa fa-times me-2"></i>
                        <span>{{ 'translate_cancel' | translate }}</span>
                    </button>
                </div>
            </mnm-form>
        </app-content>
    </div>
</app-page>
