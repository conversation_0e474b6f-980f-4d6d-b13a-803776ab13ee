import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GanttChartComponent } from './gantt-chart.component';
import { SharedModule } from '../../shared.module';
import {
    GanttChartService,
    GanttDateService,
    GanttDataProcessorService,
    GanttPositionCalculatorService,
    GanttTimelineService,
} from './services';
import {
    GanttHeaderComponent,
    GanttTaskRowComponent,
    GanttSubtaskRowComponent,
} from './components';

@NgModule({
    declarations: [
        GanttChartComponent,
        GanttHeaderComponent,
        GanttTaskRowComponent,
        GanttSubtaskRowComponent,
    ],
    imports: [CommonModule, SharedModule, TranslateModule],
    exports: [GanttChartComponent],
    providers: [
        GanttChartService,
        GanttDateService,
        GanttDataProcessorService,
        GanttPositionCalculatorService,
        GanttTimelineService,
    ],
})
export class GanttChartModule {}
