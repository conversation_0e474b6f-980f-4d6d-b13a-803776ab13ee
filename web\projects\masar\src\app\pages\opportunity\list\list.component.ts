import { Component, OnDestroy } from '@angular/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { TableController } from '@masar/common/misc/table';
import { Item, Opportunity } from '@masar/common/models';
import { MiscApiService } from '@masar/core/services';
import { OpportunityService } from '../opportunity.service';
import { ActivatedRoute, Router } from '@angular/router';
import { permissionList } from '@masar/common/constants';
import { TranslateService } from '@ngx-translate/core';

interface FilterData {
    closingProjectName?: string;
    inputSourceIds?: string[];
    inputCategoryIds?: string[];
    priorities?: string[];
    completionRate?: string;
    inputsDate?: Date;
    closingStartDate?: Date;
    plannedClosingDate?: Date;
    owningDepartmentIds?: string[];
    assigneeType?: string | null;
    departmentIds?: string[];
    userIds?: string[];
    teamIds?: string[];
    assigned?: string;
    closingStatus?: string;
    initiativeStatus?: string;
    year?: number;
    flowState?: string;
}

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
})
export class ListComponent implements OnDestroy {
    public tableController: TableController<Opportunity, FilterData>;

    public inputSources: Item[];

    public inputsCategories: Item[];

    public priorities: Item[];

    public assigneeTypes: Item[];

    public departments: Item[];

    public flowState: Item[];

    public teams: Item[];

    public users: Item[];

    public year: number[];

    public currentlyDeleting: string[] = [];

    public permissionList = permissionList;

    public constructor(
        private readonly opportunityService: OpportunityService,
        private readonly notificationService: NotificationService,
        private readonly miscApiService: MiscApiService,
        private readonly translateService: TranslateService,
        router: Router,
        activatedRoute: ActivatedRoute
    ) {
        this.initItems();

        this.tableController = new TableController<Opportunity, FilterData>(
            filter =>
                opportunityService.list(
                    filter.data.closingProjectName,
                    filter.data.inputSourceIds,
                    filter.data.inputCategoryIds,
                    filter.data.priorities,
                    filter.data.completionRate,
                    filter.data.inputsDate,
                    filter.data.closingStartDate,
                    filter.data.plannedClosingDate,
                    filter.data.owningDepartmentIds,
                    filter.data.assigneeType,
                    filter.data.departmentIds,
                    filter.data.teamIds,
                    filter.data.userIds,
                    filter.data.assigned,
                    filter.data.closingStatus,
                    filter.data.initiativeStatus,
                    filter.data.year,
                    filter.data.flowState,
                    filter.pageNumber,
                    filter.pageSize
                ),
            {
                data: {
                    closingProjectName: '',
                    inputSourceIds: [],
                    inputCategoryIds: [],
                    priorities: [],
                    completionRate: '',
                    inputsDate: null,
                    closingStartDate: null,
                    plannedClosingDate: null,
                    owningDepartmentIds: [],
                    assigneeType: null,
                    departmentIds: [],
                    teamIds: [],
                    userIds: [],
                    assigned: '',
                    closingStatus: null,
                    initiativeStatus: null,
                    year: null,
                    flowState: null,
                },
            },
            {
                routingControls: {
                    router: router,
                    activatedRoute: activatedRoute,
                },
            }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public delete(item: Opportunity): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id);
        this.opportunityService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }

    private getStatusItems(): Item[] {
        return (this.flowState = [
            {
                id: 'draft',
                name: this.translateService.instant('translate_draft'),
            },
            {
                id: 'submitted',
                name: this.translateService.instant('translate_submitted'),
            },
            {
                id: 'approved',
                name: this.translateService.instant('translate_approved'),
            },
            {
                id: 'approved:final',
                name: this.translateService.instant('translate_approved:final'),
            },
            {
                id: 'rejected',
                name: this.translateService.instant('translate_rejected'),
            },
            {
                id: 'rejected:final',
                name: this.translateService.instant('translate_rejected:final'),
            },
        ]);
    }

    private initItems(): void {
        this.miscApiService
            .getList('improvement-opportunity-priority')
            .subscribe(items => (this.priorities = items));

        this.miscApiService
            .getList('improvement-opportunity-input-category')
            .subscribe(items => (this.inputsCategories = items));

        this.miscApiService
            .getList('improvement-opportunity-input-source')
            .subscribe(items => (this.inputSources = items));

        this.miscApiService
            .getList('plan-assignee-type')
            .subscribe(items => (this.assigneeTypes = items));

        this.miscApiService
            .departments()
            .subscribe(items => (this.departments = items));

        this.miscApiService
            .getList('team', undefined, true)
            .subscribe(items => (this.teams = items));

        this.miscApiService
            .getList('user', undefined, true)
            .subscribe(items => (this.users = items));

        this.opportunityService.years().subscribe(items => {
            this.year = items;
        });
        this.getStatusItems();
    }
}
