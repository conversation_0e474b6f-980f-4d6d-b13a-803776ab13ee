import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    ElementRef,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { MnmFormState } from '@masar/shared/components';
import { ActivatedRoute } from '@angular/router';
import { NotificationService } from 'mnm-webapp';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, Validators, FormGroup } from '@angular/forms';
import { finalize, first, takeUntil, debounceTime } from 'rxjs/operators';
import { BenchmarkService } from '../benchmark.service';
import {
    Benchmark,
    BenchmarkOtherManagement,
    BenchmarkVisitor,
    Item,
} from '@masar/common/models';
import { fields } from './fields';
import {
    HelperService,
    MiscApiService,
    AppSettingFetcherService,
} from '@masar/core/services';
import { Observable, Subject } from 'rxjs';
import { optionalFieldAndSectionHandler } from '@masar/common/utils';

@Component({
    selector: 'app-new',
    templateUrl: './new.component.html',
})
export class NewComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('departmentFieldRef')
    private departmentFieldRef: ElementRef;

    @ViewChild('operationListFieldRef')
    private operationListFieldRef: ElementRef;

    @ViewChild('otherManagementsFieldRef')
    private otherManagementsFieldRef: ElementRef;

    @ViewChild('visitorsFieldRef')
    private visitorsFieldRef: ElementRef;

    @ViewChild('kpiResultsFieldRef')
    private kpiResultsFieldRef: ElementRef;

    @ViewChild('benefitRateRef')
    private benefitRateRef: ElementRef;

    @ViewChild('comparedWithRef')
    private comparedWithRef: ElementRef;

    public isBenefitRateEnabled = false;

    public isSubmitting = false;
    public mode: 'new' | 'edit';
    public formState: MnmFormState;

    public benchmarkId: string;

    public otherManagementTypes: Item[];

    public departmentParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public departmentChildrenFetcher: (parentId: string) => Observable<Item[]>;

    public operationParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public operationChildrenFetcher: (parentId: string) => Observable<Item[]>;

    private unsubscribeAll = new Subject();
    private visitorEmployeeNumberSubjects = new Map<
        number,
        Subject<{
            employeeNumber: string;
            visitor: BenchmarkVisitor;
            emitChange: () => void;
        }>
    >();

    public constructor(
        private readonly helperService: HelperService,
        private notificationService: NotificationService,
        private benchmarkService: BenchmarkService,
        private translateService: TranslateService,
        private activatedRoute: ActivatedRoute,
        private miscApiService: MiscApiService,
        private appSettingFetcherService: AppSettingFetcherService,
        private changeDetectorRef: ChangeDetectorRef,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);

        this.loadItems();

        this.createFetchers();

        this.monitorForm();

        this.updateOptionalSectionsAndFields();
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[0].path) {
                case 'new':
                    this.mode = 'new';
                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            this.benchmarkId = params.id;
                            this.benchmarkService
                                .get(this.benchmarkId, true)
                                .subscribe(item => {
                                    this.fillForm(item);
                                    this.handleCompletionRateValue(item);
                                });
                        });
                    break;
            }
        });
    }

    public ngAfterViewInit(): void {
        this.formState.get('department').customInputField =
            this.departmentFieldRef;

        this.formState.get('operations').customInputField =
            this.operationListFieldRef;

        this.formState.get('otherManagements').customInputField =
            this.otherManagementsFieldRef;

        this.formState.get('visitors').customInputField = this.visitorsFieldRef;

        this.formState.get('kpiResults').customInputField =
            this.kpiResultsFieldRef;

        this.formState.get('benefitRate').customInputField =
            this.benefitRateRef;

        this.formState.get('comparedWith').customInputField =
            this.comparedWithRef;

        // Update benefitRate visibility after custom field is set up
        this.updateBenefitRateVisibility();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const observable =
            this.mode === 'new'
                ? this.benchmarkService.create(
                      this.formState.group.getRawValue()
                  )
                : this.benchmarkService.update(
                      this.formState.group.getRawValue()
                  );

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(item => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'benchmark'],
                    item.id
                );
            });
    }

    public validOtherManagements(
        otherManagements: BenchmarkOtherManagement[]
    ): BenchmarkOtherManagement[] {
        return otherManagements.filter(x => x.type && x.detail);
    }

    public validVisitors(visitors: BenchmarkVisitor[]): BenchmarkVisitor[] {
        return visitors.filter(
            x =>
                x.employeeNumber &&
                x.rank &&
                x.fullName &&
                x.department &&
                x.description &&
                x.employmentTitle &&
                x.phone &&
                x.email
        );
    }

    public onVisitorEmployeeNumberChange(
        employeeNumber: string,
        visitor: BenchmarkVisitor,
        emitChange: () => void,
        visitorIndex: number
    ): void {
        // Immediately update the employee number
        visitor.employeeNumber = employeeNumber;
        emitChange();

        if (!this.visitorEmployeeNumberSubjects.has(visitorIndex)) {
            const subject = new Subject<{
                employeeNumber: string;
                visitor: BenchmarkVisitor;
                emitChange: () => void;
            }>();

            subject
                .pipe(takeUntil(this.unsubscribeAll), debounceTime(1000))
                .subscribe(({ employeeNumber, visitor, emitChange }) => {
                    if (employeeNumber) {
                        this.handleEmployeeNumberChange(employeeNumber, {
                            target: visitor,
                            fieldMap: {
                                rank: 'rank',
                                name: 'fullName',
                                email: 'email',
                                phoneNumber: 'phone',
                            },
                            additionalFields: {
                                department: user =>
                                    user.departmentLinks?.[0]?.department
                                        ?.name || '',
                                employmentTitle: user => user.rank,
                                description: user => user.rank,
                            },
                            onSuccess: () => emitChange(),
                            onError: () => emitChange(),
                        });
                    }
                });

            this.visitorEmployeeNumberSubjects.set(visitorIndex, subject);
        }

        this.visitorEmployeeNumberSubjects
            .get(visitorIndex)!
            .next({ employeeNumber, visitor, emitChange });
    }

    private handleEmployeeNumberChange(
        employeeNumber: string,
        options: {
            target: any;
            fieldMap: { [key: string]: string };
            additionalFields?: { [key: string]: (user: any) => string };
            onSuccess?: () => void;
            onError?: () => void;
        }
    ): void {
        this.benchmarkService
            .getUserByEmployeeNumber(employeeNumber)
            .subscribe({
                next: user => {
                    if (user) {
                        // Handle mapped fields
                        const updates: { [key: string]: string } = {};
                        Object.entries(options.fieldMap).forEach(
                            ([userField, targetField]) => {
                                if (user[userField]) {
                                    updates[targetField] = user[userField];
                                }
                            }
                        );

                        // Handle additional fields if provided
                        if (options.additionalFields) {
                            Object.entries(options.additionalFields).forEach(
                                ([field, getValue]) => {
                                    updates[field] = getValue(user);
                                }
                            );
                        }

                        // Update target
                        if (options.target instanceof FormGroup) {
                            options.target.patchValue(updates);
                        } else {
                            Object.assign(options.target, updates);
                        }

                        options.onSuccess?.();
                    }
                },
                error: () => {
                    options.onError?.();
                },
            });
    }

    private loadItems(): void {
        this.miscApiService
            .getList('benchmark-other-management-type')
            .subscribe(items => (this.otherManagementTypes = items));

        this.miscApiService.setMiscItems(this.formState, [
            ['benchmark-management-type', 'managementType'],
            ['benchmark-entity-type', 'entityType'],
            ['benchmark-language', 'language'],
            ['benchmark-type', 'type'],
            ['benchmark-method', 'method'],
            ['benchmark-selection-reason', 'selectionReasons', undefined, true],
            ['benchmark-request-reason', 'requestReasons', undefined, true],
            ['partner', 'partner', undefined, true],
            ['strategic-goal', 'goals', undefined, true],
        ]);
    }

    private createFetchers(): void {
        this.departmentParentFetcher = (childId: string) =>
            this.miscApiService.parentDepartment(childId);
        this.departmentChildrenFetcher = (parentId: string) =>
            this.miscApiService.departments({
                parentDepartmentId: parentId,
                respectHierarchy: true,
            });

        this.operationParentFetcher = (childId: string) =>
            this.miscApiService.parentOperation(childId);
        this.operationChildrenFetcher = (parentId: string) =>
            this.miscApiService.operations({
                parentOperationId: parentId,
                respectHierarchy: true,
            });
    }

    private fillForm(item: Benchmark): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }

    private monitorForm(): void {
        // Management type monitoring
        this.formState.group.controls['managementType'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const operations = this.formState.group.controls['operations'];
                const otherManagements =
                    this.formState.group.controls['otherManagements'];

                operations.disable();
                operations.setValue([]);

                otherManagements.disable();
                otherManagements.setValue([]);

                switch (value) {
                    case 'operation':
                        operations.enable();
                        break;
                    case 'other':
                        otherManagements.enable();
                        break;
                }

                operations.updateValueAndValidity();
                otherManagements.updateValueAndValidity();
            });

        // Entity type monitoring
        this.formState.group.controls['entityType'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const entityName = this.formState.group.controls['entityName'];
                const partner = this.formState.group.controls['partner'];

                entityName.disable();
                entityName.setValidators([]);
                entityName.setValue('');

                partner.disable();
                partner.setValidators([]);
                partner.setValue(null);

                switch (value) {
                    case 'partner':
                        partner.setValidators([Validators.required]);
                        partner.enable();
                        break;
                    case 'country':
                    case 'other':
                        entityName.setValidators([Validators.required]);
                        entityName.enable();
                        break;
                }

                partner.updateValueAndValidity();
                entityName.updateValueAndValidity();
            });

        [
            'requestReasons',
            'otherRequestReasons',
            // 'selectionReasons',
            // 'otherSelectionReasons',
        ].forEach(x => {
            const control = this.formState.group.controls[x];
            control.setValidators([Validators.required]);
            control.updateValueAndValidity();
        });

        // Request reason monitoring
        this.formState.group.controls['requestReasons'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const correspondingControl =
                    this.formState.group.controls['otherRequestReasons'];
                if (!value || value.length === 0) {
                    correspondingControl.setValidators([Validators.required]);
                } else {
                    correspondingControl.setValidators([]);
                }
                correspondingControl.updateValueAndValidity({
                    emitEvent: false,
                });
            });

        this.formState.group.controls['otherRequestReasons'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const correspondingControl =
                    this.formState.group.controls['requestReasons'];
                if (!value) {
                    correspondingControl.setValidators([Validators.required]);
                } else {
                    correspondingControl.setValidators([]);
                }
                correspondingControl.updateValueAndValidity({
                    emitEvent: false,
                });
            });

        // Selection reason monitoring
        // this.formState.group.controls['selectionReasons'].valueChanges
        //     .pipe(takeUntil(this.unsubscribeAll))
        //     .subscribe(value => {
        //         const correspondingControl =
        //             this.formState.group.controls['otherSelectionReasons'];
        //         if (!value || value.length === 0) {
        //             correspondingControl.setValidators([Validators.required]);
        //         } else {
        //             correspondingControl.setValidators([]);
        //         }
        //         correspondingControl.updateValueAndValidity({
        //             emitEvent: false,
        //         });
        //     });

        // this.formState.group.controls['otherSelectionReasons'].valueChanges
        //     .pipe(takeUntil(this.unsubscribeAll))
        //     .subscribe(value => {
        //         const correspondingControl =
        //             this.formState.group.controls['selectionReasons'];
        //         if (!value) {
        //             correspondingControl.setValidators([Validators.required]);
        //         } else {
        //             correspondingControl.setValidators([]);
        //         }
        //         correspondingControl.updateValueAndValidity({
        //             emitEvent: false,
        //         });
        //     });

        // Previously compared monitoring
        this.formState.group.controls['isPreviouslyCompared'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const comparedWithControl =
                    this.formState.group.controls['comparedWith'];

                if (value) {
                    // Show field and make it required
                    this.formState.showField('comparedWith');
                    comparedWithControl.setValidators([Validators.required]);
                } else {
                    // Hide field and remove validation
                    this.formState.hideField('comparedWith');
                    comparedWithControl.setValidators([]);
                    comparedWithControl.setValue('');
                }

                comparedWithControl.updateValueAndValidity();
            });

        // Coordinator employee number monitoring
        this.formState.group.controls['coordinatorEmployeeNumber'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll), debounceTime(1000))
            .subscribe(employeeNumber => {
                if (employeeNumber) {
                    this.handleEmployeeNumberChange(employeeNumber, {
                        target: this.formState.group,
                        fieldMap: {
                            rank: 'coordinatorRank',
                            name: 'coordinatorFullName',
                            email: 'coordinatorEmail',
                            phoneNumber: 'coordinatorPhone',
                        },
                    });
                }
            });
    }

    private handleCompletionRateValue(item: Benchmark): void {
        if (!item.benefitRate) return;

        // if it was percentage then replace the % sign with empty string
        if (item.benefitRate.includes('%')) {
            item.benefitRate = item.benefitRate.replace('%', '');
        }

        // if completion rate can not be number then notify the user to enter a valid number
        if (isNaN(parseFloat(item.benefitRate))) {
            const oldValue = item.benefitRate;

            item.benefitRate = null;

            let note = this.translateService.instant(
                'translate_benefit_rate_hint'
            );

            const message = this.translateService.instant(
                'translate_benefit_rate_hint_message',
                { oldValue: oldValue }
            );

            note += `\n${message}`;

            this.formState.get('benefitRate').note = note;
        }
    }

    private updateOptionalSectionsAndFields(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(item => {
                const optionalFields = item.benchmarkSetting?.optionalFields;

                // Handle benefitRate custom field visibility
                const benefitRateField = optionalFields.find(
                    (field: any) => field.name === 'benefit_rate'
                );
                this.isBenefitRateEnabled = benefitRateField?.isEnabled ?? true;

                // Update benefitRate visibility if custom field is already set up
                this.updateBenefitRateVisibility();

                optionalFieldAndSectionHandler(
                    this.formState,
                    this.changeDetectorRef,
                    {
                        optionalFields: optionalFields,
                        fields: [
                            ['benchmark_points', 'benchmarkPoints'],
                            ['is_previously_compared', 'isPreviouslyCompared'],
                            ['reason_for_comparison', 'reasonForComparison'],
                            [
                                'current_performance_summary',
                                'currentPerformanceSummary',
                            ],
                        ],
                    },
                    null
                );
            });
    }

    private updateBenefitRateVisibility(): void {
        // Only update if the custom field is set up
        if (this.formState.get('benefitRate')?.customInputField) {
            if (this.isBenefitRateEnabled) {
                this.formState.showField('benefitRate');
            } else {
                this.formState.hideField('benefitRate');
            }
        }
    }
}
