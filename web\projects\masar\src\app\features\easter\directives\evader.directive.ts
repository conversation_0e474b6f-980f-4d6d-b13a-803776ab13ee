import {
    AfterViewInit,
    Directive,
    ElementRef,
    HostListener,
    Inject,
    <PERSON><PERSON><PERSON><PERSON>,
    Renderer2,
} from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { EasterService } from '../easter.service';

interface PolarPoint {
    r: number;
    theta: number;
}

interface CartesianPoint {
    x: number;
    y: number;
}

interface Rectangle {
    x: number;
    y: number;
    width: number;
    height: number;
}

@Directive({
    selector: '[appEvader]',
})
export class EvaderDirective implements AfterViewInit, OnDestroy {
    private minDistance: number;
    private rect: Rectangle;
    private rectCenter: CartesianPoint;
    private evaderCallback: any;

    public constructor(
        private ref: ElementRef,
        private renderer: Renderer2,
        private easterService: EasterService,
        @Inject(DOCUMENT) private document: Document
    ) {}

    private static cartesianToPolar(point: CartesianPoint): PolarPoint {
        return {
            r: Math.sqrt(point.x * point.x + point.y * point.y),
            theta: Math.atan2(point.y, point.x),
        };
    }

    private static polarToCartesian(point: PolarPoint): CartesianPoint {
        return {
            x: point.r * Math.cos(point.theta),
            y: point.r * Math.sin(point.theta),
        };
    }

    private static calculateDistance(
        point: CartesianPoint,
        rectangle: Rectangle
    ): number {
        // Calculate the closest point within the rectangle to the given point
        const closestX = Math.max(
            rectangle.x,
            Math.min(point.x, rectangle.x + rectangle.width)
        );
        const closestY = Math.max(
            rectangle.y,
            Math.min(point.y, rectangle.y + rectangle.height)
        );

        // Calculate the distance between the closest point and the point
        const distanceX = point.x - closestX;
        const distanceY = point.y - closestY;
        return Math.sqrt(distanceX * distanceX + distanceY * distanceY);
    }

    @HostListener('window:resize')
    private refreshParameters(): void {
        const clientRect = this.ref.nativeElement.getBoundingClientRect();
        this.rect = {
            x: clientRect.x,
            y: clientRect.y,
            width: clientRect.width,
            height: clientRect.height,
        };

        this.rectCenter = {
            x: this.rect.x + this.rect.width / 2,
            y: this.rect.y + this.rect.height / 2,
        };
        this.minDistance = Math.max(this.rect.width, this.rect.height) * 0.6;
    }

    public ngAfterViewInit(): void {
        this.renderer.setStyle(
            this.ref.nativeElement,
            'transition',
            'transform cubic-bezier(.41,.88,.74,1.45) 0.1s'
        );

        this.refreshParameters();

        this.evaderCallback = this.evade.bind(this);
        this.document.addEventListener('mousemove', this.evaderCallback);
    }

    public ngOnDestroy(): void {
        this.document.removeEventListener('mousemove', this.evaderCallback);
    }

    private evade(event: MouseEvent): void {
        // Check if mouse is still far away from the rectangle
        const distance = EvaderDirective.calculateDistance(event, this.rect);
        if (distance > this.minDistance || !this.easterService.isEnabled) {
            this.renderer.setStyle(this.ref.nativeElement, 'transform', '');
            this.renderer.setStyle(
                this.ref.nativeElement,
                'box-shadow',
                '0px 0px 0px rgba(0, 0, 0, 0.5)'
            );
            return;
        }

        // Normalize mouse pointer to center of
        // rectangle
        const mouseNormalized = {
            x: event.x - this.rectCenter.x,
            y: this.rectCenter.y - event.y,
        };

        const mousePolarPoint =
            EvaderDirective.cartesianToPolar(mouseNormalized);

        const rectPolarPoint = {
            r: this.minDistance,
            theta: -(mousePolarPoint.theta + Math.PI), // flip the position to the opposite side
        };

        // Get the translation
        const translation = EvaderDirective.polarToCartesian(rectPolarPoint);

        // Compute the rotation angles
        const maxAngle = 20;
        const yRotation = (-translation.x / this.minDistance) * maxAngle;
        const xRotation = (translation.y / this.minDistance) * maxAngle;

        // Compute shadow
        const maxShadow = 20;
        const xShadow = (translation.x / this.minDistance) * maxShadow;
        const yShadow = (translation.y / this.minDistance) * maxShadow;

        this.renderer.setStyle(
            this.ref.nativeElement,
            'transform',
            `translate(${translation.x}px, ${translation.y}px) perspective(50px) rotateX(${xRotation}deg) rotateY(${yRotation}deg)`
        );

        this.renderer.setStyle(
            this.ref.nativeElement,
            'box-shadow',
            `${xShadow}px ${yShadow}px 10px rgba(0, 0, 0, 0.3)`
        );
    }
}
