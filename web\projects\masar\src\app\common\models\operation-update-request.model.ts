import { FlowItem } from '@masar/features/flow/interfaces';
import { Operation } from '@masar/common/models/operation.model';
import { User } from '@masar/common/models/user.model';
import { OperationUpdateRequestItem } from '@masar/common/models/operation-update-request-item.model';

export interface OperationUpdateRequest
    extends FlowItem<
        'draft' | 'submitted' | 'approved' | 'returned' | 'rejected'
    > {
    id: string;
    oldData: unknown;
    newData: unknown;
    operation: Operation;
    createdBy: User;
    creationTime: Date;
    items: OperationUpdateRequestItem[];
}
