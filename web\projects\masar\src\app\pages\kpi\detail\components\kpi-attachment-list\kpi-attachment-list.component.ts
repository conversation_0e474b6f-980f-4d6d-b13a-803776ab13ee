import { Component, Input, Ng<PERSON><PERSON>, OnDestroy, OnInit } from '@angular/core';
import { TableController } from '@masar/common/misc/table';
import { Department, KpiResultAttachment } from '@masar/common/models';
import { KpiService } from '../../../kpi.service';
import { permissionList } from '@masar/common/constants';
import { functions } from '@masar/common/misc/functions';
import { FileViewerComponent } from '@masar/features/masar/components';
import { ModalService } from 'mnm-webapp';

@Component({
    selector: 'app-kpi-attachment-list',
    templateUrl: './kpi-attachment-list.component.html',
})
export class KpiAttachmentListComponent implements OnInit, OnDestroy {
    @Input() public kpiId: string;

    public tableController: TableController<
        KpiResultAttachment,
        { keyword?: string; years?: number[]; departmentIds?: string[] }
    >;

    public years: number[];
    public departments: Department[];

    public permissionList = permissionList;

    public constructor(
        private kpiService: KpiService,
        private ngZone: NgZone,
        private modalService: ModalService
    ) {}

    public ngOnInit(): void {
        this.kpiService
            .getAggregatedYears(this.kpiId, null)
            .subscribe(items => (this.years = items));

        this.kpiService
            .getAggregatedDepartments(this.kpiId, null)
            .subscribe(items => (this.departments = items));

        this.tableController = new TableController<
            KpiResultAttachment,
            { keyword?: string; years?: number[]; departmentIds?: string[] }
        >(
            filter =>
                this.kpiService.listAttachments(
                    this.kpiId,
                    filter.data.keyword,
                    filter.data.years,
                    filter.data.departmentIds,
                    filter.pageNumber,
                    filter.pageSize
                ),
            { data: { keyword: '', years: [], departmentIds: [] } }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public download(item: KpiResultAttachment): void {
        this.kpiService
            .downloadAggregateAttachment(item.id)
            .subscribe(async file => {
                if (
                    item.contentType === 'application/pdf' ||
                    item.contentType.split('/')[0] === 'image'
                ) {
                    const src = URL.createObjectURL(file);
                    await this.showFileModal(src);
                } else {
                    this.ngZone.runOutsideAngular(() => {
                        functions.downloadBlobIntoFile(file);
                    });
                }
            });
    }

    public async showFileModal(src: string): Promise<void> {
        await this.modalService.show(FileViewerComponent, {
            beforeInit: c => {
                c.src = src;
            },
            size: {
                width: '100%',
                height: '100%',
            },
        });
    }
}
