<div *ngIf="data.alerts?.length" class="mb-2 flex flex-col gap-2">
    <app-alert
        *ngFor="let alert of data.alerts"
        [id]="alert.id"
        [mode]="alert.mode"
        [label]="alert.label"
        [description]="alert.description"
    >
    </app-alert>
</div>

<app-content
    [contentTitle]="data.title | translate"
    class="col-span-1 md:col-span-2"
>
    <!-- Tools -->
    <ng-container tools>
        <ng-container *ngIf="!data.hideNew">
            <button
                *appHasPermissionId="data.permissions.write"
                class="btn btn-sm btn-outline-white"
                (click)="showCreationDialog()"
            >
                <i class="fas fa-plus"></i>
                {{ 'translate_add_new' | translate }}
            </button>
        </ng-container>

        <!-- Section Actions -->
        <ng-container *ngFor="let action of data?.sectionActions">
            <ng-container *ngIf="action.permission; else actionButtonTemplate">
                <ng-container *appHasPermissionId="action.permission">
                    <ng-container
                        [ngTemplateOutlet]="actionButtonTemplate"
                    ></ng-container>
                </ng-container>
            </ng-container>

            <ng-template #actionButtonTemplate>
                <button
                    class="btn btn-sm btn-outline-white"
                    (click)="action.callbackFn()"
                >
                    {{ action.label | translate }}
                    <i [class]="action.icon"></i>
                </button>
            </ng-template>
        </ng-container>
    </ng-container>

    <app-table-list
        content
        [data]="data"
        [tableController]="tableController"
        (edit)="showCreationDialog($event)"
        (delete)="delete.emit($event)"
    ></app-table-list>
</app-content>
