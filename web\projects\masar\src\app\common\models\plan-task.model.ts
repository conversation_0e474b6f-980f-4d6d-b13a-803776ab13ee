import { Department } from './department.model';
import { Kpi } from './kpi.model';
import { Operation } from './operation.model';
import { Plan } from './plan.model';
import { Team } from './team.model';
import { User } from './user.model';
import { PlanSubtask } from './plan-subtask.model';
import { PlanUserAbility } from './plan-user-ability.model';
import { PlanTaskCategory } from '@masar/common/models/plan-task-category.model';
import { Item } from '@masar/common/models/item.model';

export interface PlanTask {
    id: string;
    name: string;
    assignedDepartment: Department;
    assignedTeam: Team;
    assignedUser: User;
    otherKpis: string;
    isApproved: boolean;
    progress: number;
    expectedProgress: number;
    weight: number;
    from: Date;
    to: Date;
    plan: Plan;
    risks: string;
    operations: Operation[];
    kpis: Kpi[];
    subtasks: PlanSubtask[];
    subtasksCount: number;
    isPlanInitiallyApproved: boolean;
    category: PlanTaskCategory;
    description?: string;
    estimatedCost?: number;
    expectedOutputs?: any;
    planTaskStakeholders?: Item[];
    strategicGoals?: Item[];

    planUserAbility: PlanUserAbility;
}
