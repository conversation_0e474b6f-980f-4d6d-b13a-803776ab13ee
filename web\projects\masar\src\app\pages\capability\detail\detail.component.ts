import { Component, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CapabilityService } from '../capability.service';
import { takeUntil } from 'rxjs/operators';
import { Capability, LibraryFile, Kpi } from '@masar/common/models';
import { Observable, Subject } from 'rxjs';
import { TableResult } from '@masar/common/misc/table';
import { SharedCapabilityService } from '@masar/shared/services';

@Component({
    selector: 'app-detail',
    templateUrl: './detail.component.html',
})
export class DetailComponent implements OnDestroy {
    public item: Capability;
    public itemId: string;

    private unsubscribeAll = new Subject();

    public constructor(
        public capabilityService: CapabilityService,
        public sharedCapabilityService: SharedCapabilityService,
        private activatedRoute: ActivatedRoute
    ) {
        this.activatedRoute.params
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(params => {
                {
                    this.itemId = params.id;
                    this.capabilityService.get(this.itemId).subscribe(item => {
                        this.item = item;
                    });
                }
            });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    // Library linking methods
    public listLinkedLibraryFiles(): (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<LibraryFile>> {
        return (keyword: string, pageNumber: number, pageSize: number) =>
            this.sharedCapabilityService.listLinkedLibraryFiles(
                this.itemId,
                keyword,
                pageNumber,
                pageSize
            );
    }

    public linkLibraryFile(): (libraryFileId: string) => Observable<string> {
        return (libraryFileId: string) =>
            this.capabilityService.linkLibraryFile(this.itemId, libraryFileId);
    }

    public unlinkLibraryFile(): (libraryFileId: string) => Observable<string> {
        return (libraryFileId: string) =>
            this.capabilityService.unlinkLibraryFile(
                this.itemId,
                libraryFileId
            );
    }

    // Kpi linking methods
    public listLinkedKpis(): (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Kpi>> {
        return (keyword: string, pageNumber: number, pageSize: number) =>
            this.sharedCapabilityService.listLinkedKpis(
                this.itemId,
                keyword,
                null,
                null,
                pageNumber,
                pageSize
            );
    }

    // Kpi linking methods
    public listUnlinkedKpis(): (
        keyword: string,
        kpiNumber: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Kpi>> {
        return (
            keyword: string,
            kpiNumber: string,
            pageNumber: number,
            pageSize: number
        ) =>
            this.capabilityService.listUnlinkedKpis(
                this.itemId,
                keyword,
                kpiNumber,
                pageNumber,
                pageSize
            );
    }

    public linkKpi(): (kpiId: string) => Observable<string> {
        return (kpiId: string) =>
            this.capabilityService.linkKpi(this.itemId, kpiId);
    }

    public unlinkKpi(): (kpiId: string) => Observable<string> {
        return (kpiId: string) =>
            this.capabilityService.unlinkKpi(this.itemId, kpiId);
    }
}
