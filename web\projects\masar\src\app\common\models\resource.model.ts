export interface Resource {
    id: string;
    type:
        | 'operation_procedure_step'
        | 'kpi_result_capability'
        | 'capability'
        | 'kpi_benchmark'
        | 'operation'
        | 'operation_enhancement'
        | 'plan_subsubtask'
        | 'benchmark'
        | 'tournament'
        | 'standard'
        | 'operation_procedure'
        | 'improvement_opportunity'
        | 'standard_subtask';
    name: string;
    metadata: string;
}
