import { Operation } from './operation.model';
import { Item } from './item.model';
import { LibraryFile } from './library-file.model';
import { OperationProcedure } from './operation-procedure.model';

export interface OperationEnhancement {
    id: string;
    date: Date;
    number: number;
    drawNumber: number;
    description: string;
    tools: string;
    impact: string;
    inputs: string;
    outputs: string;
    outputType: string;
    flowChartFile: LibraryFile;
    businessModelFile: LibraryFile;
    operation: Operation;
    type: Item;
    files: LibraryFile[];
    procedure: OperationProcedure;
}
