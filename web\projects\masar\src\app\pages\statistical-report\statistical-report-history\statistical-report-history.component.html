<app-content contentTitle="{{ 'translate_rejection_notes' | translate }}">
    <ng-container content>
        <div *ngIf="isLoading" class="py-4 text-center">
            <app-loading-ring></app-loading-ring>
        </div>

        <ng-container *ngIf="!isLoading">
            <table class="w-full" *ngIf="rejectionDetails?.length">
                <thead>
                    <tr>
                        <!-- Pre-translate headers to avoid multiple pipe executions -->
                        <th>{{ dateHeader }}</th>
                        <th>{{ rejectedByHeader }}</th>
                        <th>{{ notesHeader }}</th>
                        <th class="text-center">{{ valueRemovedHeader }}</th>
                        <th class="text-center">
                            {{ attachmentRemovedHeader }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        *ngFor="
                            let item of rejectionDetails;
                            trackBy: trackById
                        "
                    >
                        <td>
                            {{ item.createdDate | date : 'yyyy-MM-dd' }}<br />{{
                                item.createdDate | date : 'hh:mm a'
                            }}
                        </td>
                        <td>{{ item.user?.name }}</td>
                        <td
                            class="max-w-[200px] overflow-scroll text-ellipsis whitespace-normal break-words p-2 align-top"
                        >
                            {{ item.notes }}
                        </td>
                        <td class="text-center">
                            {{ item.value !== null ? item.value : '' }}
                        </td>
                        <td class="text-center">
                            {{ item.fileName || '' }}
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- Empty state message -->
            <div
                *ngIf="!rejectionDetails?.length"
                class="py-4 text-center text-gray-500"
            >
                {{ 'translate_no_rejection_notes' | translate }}
            </div>
        </ng-container>
    </ng-container>
</app-content>
