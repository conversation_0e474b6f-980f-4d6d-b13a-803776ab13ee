import {
    Component,
    EventEmitter,
    Input,
    NgModuleRef,
    Output,
    Renderer2,
} from '@angular/core';
import { TableController } from '@masar/common/misc/table';
import { Subject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { ModalService } from 'mnm-webapp';
import { TableListLinkerComponent } from '@masar/features/dynamic-page/components/table-list/table-list-linker/table-list-linker.component';
import { takeUntil } from 'rxjs/operators';
import { TableListService } from '@masar/features/dynamic-page/components/table-list/table-list.service';
import { DetailSectionsComponent } from '@masar/features/dynamic-page/components/detail-sections/detail-sections.component';
import { NewFormComponent } from '@masar/features/dynamic-page/components';
import { MnmFormState } from '@masar/shared/components';
import { FormBuilder } from '@angular/forms';
import { MiscApiService } from '@masar/core/services';
import { TableListData, TableListDataAction } from '../../interfaces';

@Component({
    selector: 'app-table-list',
    templateUrl: './table-list.component.html',
    providers: [TableListService],
})
export class TableListComponent {
    @Input() public data: TableListData<unknown>;

    @Input() public tableController: TableController<unknown, unknown>;

    @Output() public edit = new EventEmitter<string>();

    @Output() public delete = new EventEmitter<string>();

    public isDeleting: Record<string, boolean> = {};

    public itemTabOpen: Record<string, boolean> = {};

    public constructor(
        private readonly formBuilder: FormBuilder,
        private readonly modalService: ModalService,
        private readonly miscApiService: MiscApiService,
        private readonly tableListService: TableListService,
        private readonly moduleReference: NgModuleRef<any>,
        private readonly translateService: TranslateService,
        private readonly renderer: Renderer2
    ) {}

    public refreshTable(): void {
        this.tableController.filter$.next();
    }

    public async showDetailDialog(
        id: string,
        detail: TableListData<unknown>['detail']
    ): Promise<void> {
        const translatedTitle = await this.translateService.instant(
            detail.title
        );

        this.tableListService.init(detail.endPoint);

        const item = await this.tableListService.get(id).toPromise();

        await this.modalService.show(DetailSectionsComponent, {
            size: { width: '70%' },
            moduleRef: this.moduleReference,
            title: translatedTitle,
            beforeInit: c => {
                c.sections = detail.sections;
                c.item = item;
            },
        });
    }

    public showLinker(
        linker: TableListDataAction<unknown>['linker']
    ): () => void {
        return () => {
            this.showLinkerDialog(linker).then();
        };
    }

    public getShowFormDialog(
        form: TableListDataAction<unknown>['form']
    ): () => void {
        return () => {
            this.showFormDialog(form).then();
        };
    }

    public getShowDialog(
        id: string,
        dialog: TableListDataAction<unknown>['dialog']
    ): () => void {
        return () => {
            this.showDialog(id, dialog).then();
        };
    }

    private async showFormDialog(
        form: TableListDataAction<unknown>['form']
    ): Promise<void> {
        const subject = new Subject();

        const translatedTitle = await this.translateService.instant('s');

        const formState = new MnmFormState(form.fields(), this.formBuilder);

        if (form.miscList) {
            this.miscApiService.setMiscItems(formState, form.miscList);
        }

        const componentReference = await this.modalService.show(
            NewFormComponent,
            {
                size: { width: '70%' },
                moduleRef: this.moduleReference,
                title: translatedTitle,
                beforeInit: c => {
                    this.renderer.addClass(document.body, 'modal-no-overlay');
                    c.formState = formState;
                    c.id = form.id;
                    c.createCb = form.createCb;
                    c.updateCb = form.updateCb;
                    c.getForEditCb = form.getForEditCb;
                },
                onDismiss: () => {
                    subject.next();
                    subject.complete();
                    this.renderer.removeClass(
                        document.body,
                        'modal-no-overlay'
                    );
                },
            }
        );

        componentReference.submitted.pipe(takeUntil(subject)).subscribe(() => {
            this.refreshTable();
            this.modalService.dismiss(componentReference);
        });
    }

    private async showLinkerDialog(
        linker: TableListDataAction<unknown>['linker']
    ): Promise<void> {
        const subject = new Subject();

        const translatedTitle = await this.translateService.instant(
            linker.title
        );

        const componentReference = await this.modalService.show(
            TableListLinkerComponent,
            {
                size: { width: '70%' },
                moduleRef: this.moduleReference,
                title: translatedTitle,
                beforeInit: c => {
                    c.bodyParameterName = linker.bodyParameterName;
                    c.endPoint = linker.endPoint;
                    c.data = linker.data;
                },
                onDismiss: () => {
                    subject.next();
                    subject.complete();
                },
            }
        );

        componentReference.changed.pipe(takeUntil(subject)).subscribe(() => {
            this.refreshTable();
        });
    }

    private async showDialog(
        id: string,
        dialog: TableListDataAction<unknown>['dialog']
    ): Promise<void> {
        const subject = new Subject();

        const translatedTitle = await this.translateService.instant(
            dialog.title
        );

        const componentReference = await this.modalService.show(
            dialog.component,
            {
                size: { width: '70%' },
                moduleRef: this.moduleReference,
                title: translatedTitle,
                beforeInit: c => {
                    c['id'] = id;
                    c['data'] = dialog.data;
                },
                onDismiss: () => {
                    subject.next();
                    subject.complete();
                },
            }
        );

        componentReference['submitted']
            ?.pipe(takeUntil(subject))
            .subscribe(() => {
                this.refreshTable();
                this.modalService.dismiss(componentReference);
            });
    }
}
