import { NgModule } from '@angular/core';
import { AiAnalyzeButtonComponent } from './components/analyze-button/ai-analyze-button.component';
import { TranslateModule } from '@ngx-translate/core';
import { AiAnalysisResultComponent } from './components/analyze-button/components/ai-analysis-result/ai-analysis-result.component';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@masar/shared/shared.module';

@NgModule({
    declarations: [AiAnalyzeButtonComponent, AiAnalysisResultComponent],
    imports: [CommonModule, TranslateModule, SharedModule],
    exports: [AiAnalyzeButtonComponent],
})
export class AiModule {}
