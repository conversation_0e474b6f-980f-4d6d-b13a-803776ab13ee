import { Pipe, PipeTransform } from '@angular/core';
import { TableListData } from '@masar/features/dynamic-page/interfaces';

@Pipe({ name: 'getTableListActions' })
export class GetTableListActionsPipe implements PipeTransform {
    public transform<T>(
        data: TableListData<T>,
        item: T
    ): TableListData<T>['actions'] {
        if (data.actions) return data.actions;

        if (data.actionsFactory) return data.actionsFactory(item);

        return [];
    }
}
