import { Injectable } from '@angular/core';
import { GanttDateService } from './gantt-date.service';
import { MonthHeader } from '../interfaces/gantt.interfaces';

export interface TimelineData {
    dates: Date[];
    monthHeaders: MonthHeader[];
    normalizedStartDate: Date;
    normalizedEndDate: Date;
    totalMonths: number;
}

@Injectable({
    providedIn: 'root',
})
export class GanttTimelineService {
    private readonly monthNames = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
    ];

    public constructor(private dateService: GanttDateService) {}

    /**
     * Generates timeline data including dates and month headers
     */
    public generateTimeline(startDate: Date, endDate: Date): TimelineData {
        const normalizedStartDate =
            this.dateService.getFirstDayOfMonth(startDate);
        const normalizedEndDate = this.dateService.getFirstDayOfMonth(endDate);

        const totalMonths = this.dateService.getMonthsDifference(
            normalizedStartDate,
            normalizedEndDate
        );

        const dates = this.generateTimelineDates(
            normalizedStartDate,
            totalMonths
        );
        const monthHeaders = this.generateMonthHeaders(dates);

        return {
            dates,
            monthHeaders,
            normalizedStartDate,
            normalizedEndDate,
            totalMonths,
        };
    }

    private generateTimelineDates(startDate: Date, monthCount: number): Date[] {
        const dates = new Array<Date>(monthCount);
        const current = new Date(startDate.getTime());

        for (let i = 0; i < monthCount; i++) {
            dates[i] = new Date(current.getTime());
            current.setUTCMonth(current.getUTCMonth() + 1);
        }

        return dates;
    }

    private generateMonthHeaders(dates: Date[]): MonthHeader[] {
        return dates.map(date => {
            const monthYear = `${
                this.monthNames[date.getUTCMonth()]
            } ${date.getUTCFullYear()}`;
            return {
                month: monthYear,
                colspan: 1, // Each month gets one column // cspell:disable-line
            };
        });
    }
}
