<app-content>
    <!-- Content -->
    <ng-container content>
        <mnm-form
            *ngIf="formState"
            [state]="formState"
            [translateLabels]="true"
        >
            <div class="mt-2 text-center">
                <button
                    type="submit"
                    class="btn-lg btn btn-primary"
                    (click)="submit()"
                    [disabled]="isSubmitting"
                >
                    <app-loading-ring
                        *ngIf="isSubmitting"
                        class="me-2"
                    ></app-loading-ring>
                    <i class="fa-light fa-save me-2"></i>
                    <span>{{ 'translate_save' | translate }}</span>
                </button>
            </div>
        </mnm-form>
    </ng-container>
</app-content>

<!-- Library files field -->
<ng-template #libraryFilesFieldRef>
    <app-fieldset legend="{{ 'translate_attachments' | translate }}">
        <app-list-field
            [itemTemplate]="template"
            [formControl]="formState.group.controls['libraryFiles']"
            [validItems]="validLibraryFiles"
        >
            <ng-template #template let-item="item" let-emitChange="emitChange">
                <div class="flex flex-col gap-4 md:flex-row">
                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label>{{ 'translate_file' | translate }}</label>
                        <app-library-file-field
                            [ngModel]="item.libraryFile"
                            (ngModelChange)="
                                item.libraryFile = $event; emitChange()
                            "
                        ></app-library-file-field>
                    </div>
                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label>{{ 'translate_description' | translate }}</label>
                        <input
                            type="text"
                            [ngModel]="item.description"
                            (ngModelChange)="
                                item.description = $event; emitChange()
                            "
                        />
                    </div>
                </div>
            </ng-template>
        </app-list-field>
    </app-fieldset>
</ng-template>
