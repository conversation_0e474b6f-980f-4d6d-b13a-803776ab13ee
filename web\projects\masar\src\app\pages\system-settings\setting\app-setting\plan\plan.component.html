<app-fieldset legend="{{ 'translate_general' | translate }}">
    <div
        *appWaitUntilLoaded="planSetting"
        class="mb-5 grid grid-cols-3 items-center gap-5"
    >
        <!-- Max approving department level -->
        <label class="flex flex-col items-stretch gap-2">
            <span>
                {{ 'translate_max_approving_department_level' | translate }}
            </span>
            <input
                type="number"
                min="0"
                [(ngModel)]="planSetting.maxApprovingDepartmentLevel"
            />
        </label>

        <!-- Min intermediate initial approval count -->
        <label class="flex flex-col items-stretch gap-2">
            <span>
                {{
                    'translate_min_intermediate_initial_approval_count'
                        | translate
                }}
            </span>
            <input
                type="number"
                min="0"
                [(ngModel)]="planSetting.minIntermediateInitialApprovalCount"
            />
        </label>

        <!-- Can approval cycle be reset by rejection -->
        <label class="flex flex-row items-center gap-2">
            <input
                type="checkbox"
                [(ngModel)]="planSetting.doesRejectionResetApprovalCycle"
            />
            <span>
                {{ 'translate_rejection_resets_approval_cycle' | translate }}
            </span>
        </label>

        <!-- Enable the action button without conditions -->
        <label
            class="flex cursor-pointer select-none flex-row items-center gap-3"
        >
            <div class="relative h-6 w-11 flex-shrink-0">
                <input
                    type="checkbox"
                    [(ngModel)]="planSetting.shouldRuleCheckBeforeSubmission"
                    class="peer sr-only"
                />
                <div
                    class="absolute inset-0 rounded-full transition-colors duration-200 ease-in-out"
                    [ngClass]="{
                        'bg-blue-500':
                            planSetting.shouldRuleCheckBeforeSubmission,
                        'bg-gray-300':
                            !planSetting.shouldRuleCheckBeforeSubmission
                    }"
                ></div>
                <div
                    class="absolute left-1 top-1 h-4 w-4 rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out"
                    [ngClass]="{
                        'translate-x-5 transform':
                            !planSetting.shouldRuleCheckBeforeSubmission
                    }"
                ></div>
            </div>
            <div class="flex items-center text-sm md:text-base">
                <span>{{
                    'translate_is_flow_action_enabled' | translate
                }}</span>
                <span
                    class="fa-light fa-info-circle ml-2 cursor-pointer text-gray-500 hover:text-gray-700"
                    data-toggle="tooltip"
                    [appTooltip]="
                        'translate_action_not_available_message' | translate
                    "
                    data-placement="top"
                ></span>
            </div>
        </label>
    </div>
</app-fieldset>

<ng-container *ngIf="safeModeService.isEnabled">
    <app-fieldset legend="{{ 'translate_optional_fields' | translate }}">
        <div
            *appWaitUntilLoaded="planSetting"
            class="mb-5 grid grid-cols-3 items-center gap-5"
        >
            <ng-container *ngFor="let item of planSetting.optionalFields">
                <!-- Name -->
                <span
                    class="border-s-4 border-green-500 bg-gray-200 p-2 text-gray-600"
                >
                    {{ 'translate_' + item.name | translate }}
                </span>

                <!-- Is enabled -->
                <label>
                    <input
                        type="checkbox"
                        [(ngModel)]="item.isEnabled"
                        class="me-2"
                    />
                    <span>{{ 'translate_is_enabled_qm' | translate }}</span>
                </label>

                <!-- Is required -->
                <label>
                    <input
                        type="checkbox"
                        [(ngModel)]="item.isRequired"
                        class="me-2"
                    />
                    <span>{{ 'translate_is_required_qm' | translate }}</span>
                </label>
            </ng-container>
        </div>
    </app-fieldset>

    <app-fieldset [legend]="'translate_optional_sections' | translate">
        <div
            *appWaitUntilLoaded="planSetting"
            class="mb-5 grid grid-cols-3 items-center gap-5"
        >
            <ng-container *ngFor="let item of planSetting.optionalSections">
                <!-- Name -->
                <span
                    class="border-s-4 border-green-500 bg-gray-200 p-2 text-gray-600"
                >
                    {{ 'translate_' + item.name | translate }}
                </span>

                <!-- Is enabled -->
                <label>
                    <input
                        type="checkbox"
                        [(ngModel)]="item.isEnabled"
                        class="me-2"
                    />
                    <span>{{ 'translate_is_enabled_qm' | translate }}</span>
                </label>

                <!-- Is required -->
                <label>
                    <input
                        type="checkbox"
                        [(ngModel)]="item.isRequired"
                        class="me-2"
                    />
                    <span>{{ 'translate_is_required_qm' | translate }}</span>
                </label>
            </ng-container>
        </div>
    </app-fieldset>
</ng-container>

<div class="text-center">
    <button
        type="submit"
        class="btn btn-primary"
        (click)="save()"
        [disabled]="isSubmitting"
    >
        <app-loading-ring *ngIf="isSubmitting" class="me-2"></app-loading-ring>
        <i class="fa-light fa-save me-2"></i>
        <span>{{ 'translate_save' | translate }}</span>
    </button>
</div>
