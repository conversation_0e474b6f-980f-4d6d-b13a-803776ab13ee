import { Component, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { permissionList } from '@masar/common/constants';
import { Benchmark } from '@masar/common/models';
import { BenchmarkService } from '../benchmark.service';
import { AppSettingFetcherService } from '@masar/core/services';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-detail',
    templateUrl: './detail.component.html',
})
export class DetailComponent implements OnDestroy {
    public benchmarkId: string;
    public benchmark: Benchmark;

    public permissionList = permissionList;

    // Optional field visibility flags
    public optionalFields = {
        benchmarkPoints: true,
        benefitRate: true,
        isPreviouslyCompared: true,
        reasonForComparison: true,
        currentPerformanceSummary: true,
    };

    private unsubscribeAll = new Subject();

    public constructor(
        benchmarkService: BenchmarkService,
        activatedRoute: ActivatedRoute,
        private appSettingFetcherService: AppSettingFetcherService
    ) {
        this.benchmarkId = activatedRoute.snapshot.paramMap.get('id');
        benchmarkService.get(this.benchmarkId).subscribe(item => {
            this.benchmark = item;
        });

        this.updateOptionalFieldsVisibility();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    private updateOptionalFieldsVisibility(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(item => {
                const optionalFields = item.benchmarkSetting?.optionalFields;

                const isFieldEnabled: (name: string) => boolean = (
                    name: string
                ) =>
                    optionalFields.find(x => x.name === name)?.isEnabled ??
                    true;

                this.optionalFields = {
                    benchmarkPoints: isFieldEnabled('benchmark_points'),
                    benefitRate: isFieldEnabled('benefit_rate'),
                    isPreviouslyCompared: isFieldEnabled(
                        'is_previously_compared'
                    ),
                    reasonForComparison: isFieldEnabled(
                        'reason_for_comparison'
                    ),
                    currentPerformanceSummary: isFieldEnabled(
                        'current_performance_summary'
                    ),
                };
            });
    }
}
