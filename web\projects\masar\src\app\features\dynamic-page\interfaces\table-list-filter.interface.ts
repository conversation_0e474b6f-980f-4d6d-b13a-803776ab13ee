import { MiscApiOptions } from '@masar/common/interfaces';

export interface TableListBaseFilter {
    label: string;
    key: string;
}

export interface TableListFilterSelect extends TableListBaseFilter {
    type: 'select';
    miscApiOptions?: MiscApiOptions;
    bindValue?: string;
    bindLabel?: string;
    items?: unknown[];
    isMultiple?: boolean;
    isClearable?: boolean;
    isSearchable?: boolean;
}

export interface TableListFilterInput extends TableListBaseFilter {
    type: 'text';
}

export interface TableListFilterDate extends TableListBaseFilter {
    type: 'date';
}
