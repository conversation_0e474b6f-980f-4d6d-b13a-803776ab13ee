<div class="flex flex-wrap items-center gap-2">
    <span
        *ngFor="let arrayItem of value"
        class="inline-block whitespace-nowrap rounded-full bg-gray-200 px-2 py-1 text-xs font-semibold leading-none text-gray-800"
    >
        {{
            translateItemNamePipe
                ? (arrayItem | translateItem : translateItemNamePipe | async)
                : (arrayItem | translate)
        }}
    </span>
</div>
