<div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
    <div class="mb-4 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
            {{
                strategicPlan?.name ||
                    ('translate_strategic_plan_details' | translate)
            }}
        </h3>
    </div>

    <div class="overflow-hidden">
        <table class="min-w-full">
            <tbody class="divide-y divide-gray-200">
                <!-- Start Year -->
                <tr>
                    <td class="py-3 pr-6 text-sm font-medium text-gray-700">
                        {{ 'translate_start_year' | translate }}
                    </td>
                    <td class="py-3 text-sm text-gray-900">
                        <span *ngIf="strategicPlan; else loading">
                            {{ strategicPlan.startYear }}
                        </span>
                        <ng-template #loading>
                            <div
                                class="h-4 w-16 animate-pulse rounded bg-gray-200"
                            ></div>
                        </ng-template>
                    </td>
                </tr>

                <!-- End Year -->
                <tr>
                    <td class="py-3 pr-6 text-sm font-medium text-gray-700">
                        {{ 'translate_end_year' | translate }}
                    </td>
                    <td class="py-3 text-sm text-gray-900">
                        <span *ngIf="strategicPlan; else loading">
                            {{ strategicPlan.endYear }}
                        </span>
                        <ng-template #loading>
                            <div
                                class="h-4 w-16 animate-pulse rounded bg-gray-200"
                            ></div>
                        </ng-template>
                    </td>
                </tr>

                <!-- Vision -->
                <tr *ngIf="strategicPlan?.vision">
                    <td
                        class="py-3 pr-6 align-top text-sm font-medium text-gray-700"
                    >
                        {{ 'translate_vision' | translate }}
                    </td>
                    <td class="py-3 text-sm text-gray-900">
                        <p class="whitespace-pre-wrap">
                            {{ strategicPlan.vision }}
                        </p>
                    </td>
                </tr>

                <!-- Mission -->
                <tr *ngIf="strategicPlan?.mission">
                    <td
                        class="py-3 pr-6 align-top text-sm font-medium text-gray-700"
                    >
                        {{ 'translate_mission' | translate }}
                    </td>
                    <td class="py-3 text-sm text-gray-900">
                        <p class="whitespace-pre-wrap">
                            {{ strategicPlan.mission }}
                        </p>
                    </td>
                </tr>

                <!-- Strategic Goals Count -->
                <tr>
                    <td class="py-3 pr-6 text-sm font-medium text-gray-700">
                        {{ 'translate_strategic_goals_count' | translate }}
                    </td>
                    <td class="py-3 text-sm text-gray-900">
                        <span *ngIf="strategicPlan; else loading">
                            {{ strategicPlan.pillars?.length || 0 }}
                        </span>
                        <ng-template #loading>
                            <div
                                class="h-4 w-8 animate-pulse rounded bg-gray-200"
                            ></div>
                        </ng-template>
                    </td>
                </tr>

                <!-- Strategic Values Count -->
                <tr>
                    <td class="py-3 pr-6 text-sm font-medium text-gray-700">
                        {{ 'translate_strategic_values_count' | translate }}
                    </td>
                    <td class="py-3 text-sm text-gray-900">
                        <span *ngIf="strategicPlan; else loading">
                            {{ strategicPlan.values?.length || 0 }}
                        </span>
                        <ng-template #loading>
                            <div
                                class="h-4 w-8 animate-pulse rounded bg-gray-200"
                            ></div>
                        </ng-template>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
