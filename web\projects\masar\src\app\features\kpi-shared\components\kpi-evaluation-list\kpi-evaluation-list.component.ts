import { Component, Input } from '@angular/core';
import { Kpi, KpiEvaluation } from '@masar/common/models';
import { EvaluationInstanceDetailComponent } from '@masar/features/evaluate/components';
import { TranslateService } from '@ngx-translate/core';
import { ModalService } from 'mnm-webapp';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { EvaluationInstance } from '@masar/features/evaluate/types';

@Component({
    selector: 'app-kpi-evaluation-list',
    templateUrl: './kpi-evaluation-list.component.html',
})
export class KpiEvaluationListComponent {
    @Input() public records: KpiEvaluation[];
    @Input() public kpi: Kpi;

    public constructor(
        private readonly modalService: ModalService,
        private readonly translateService: TranslateService
    ) {}

    public async showDetailDialog(record: KpiEvaluation): Promise<void> {
        const evaluateString =
            this.translateService.instant('translate_evaluate');

        const periodString = this.translateService.instant(
            'translate_period_period',
            { period: record.period + 1 }
        );

        const title = `${evaluateString} ${this.kpi.name} - ${record.year} - ${periodString}`;
        const subject = new Subject();
        const component = await this.modalService.show(
            EvaluationInstanceDetailComponent,
            {
                title: title,
                beforeInit: c => {
                    c.itemId = record.id;
                    c.mode = 'view';
                    c.type = 'kpi-result-period';
                    c.item = record as unknown as Partial<EvaluationInstance>;
                },
                onDismiss: () => {
                    subject.next();
                    subject.complete();
                },
                size: { width: '80%' },
            }
        );

        component.update.pipe(takeUntil(subject)).subscribe(() => {
            this.modalService.dismiss(component);
        });
    }
}
