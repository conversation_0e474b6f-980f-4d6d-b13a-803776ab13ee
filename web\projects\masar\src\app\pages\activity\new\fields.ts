import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },

    {
        fields: [
            {
                name: 'nameAr',
                label: 'translate_name_in_arabic',
                type: 'text',
                size: 6,
                validators: [
                    Validators.required,
                    Validators.maxLength(256),
                    Validators.min(3),
                ],
            },
            {
                name: 'nameEn',
                label: 'translate_name_in_english',
                type: 'text',
                size: 6,
                validators: [Validators.maxLength(256), Validators.min(3)],
            },
        ],
    },

    {
        name: 'year',
        label: 'translate_year',
        type: 'number',
    },
];
