import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { FormBuilder, Validators } from '@angular/forms';
import { ExcelSheetService } from '../../excel-sheet.service';

export interface Department {
    id: number;
    name: string;
}

@Component({
    selector: 'app-departments',
    templateUrl: './departments.component.html',
})
export class DepartmentsComponent implements OnInit {
    public departments: Department[] = [];
    public isLoadingDepartments = false;
    public isSubmittingDepartments = false;

    public departmentForm = this.fb.group({
        name: [null, [Validators.required, Validators.minLength(3)]],
    });

    public constructor(
        private readonly fb: FormBuilder,
        private readonly api: ExcelSheetService
    ) {}

    public ngOnInit(): void {
        this.getDepartments();
    }

    public createDepartment(): void {
        if (this.departmentForm.invalid) return;
        this.isSubmittingDepartments = true;

        const name = this.departmentForm.controls['name']
            .value as unknown as string;

        this.api
            .createDepartment(name)
            .pipe(finalize(() => (this.isSubmittingDepartments = false)))
            .subscribe(() => {
                this.departmentForm.reset();
                this.getDepartments();
            });
    }

    public getDepartments(): void {
        if (this.isLoadingDepartments) return;

        this.isLoadingDepartments = true;

        this.api
            .getDepartments()
            .pipe(finalize(() => (this.isLoadingDepartments = false)))
            .subscribe(departments => (this.departments = departments));
    }

    public deleteDepartment(id: number): void {
        this.api.deleteDepartment(id).subscribe(() => this.getDepartments());
    }
}
