import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    Output,
    TemplateRef,
} from '@angular/core';
import {
    GanttItem,
    GanttConfig,
    GanttTemplateContext,
    TaskPosition,
} from '../services';

@Component({
    selector: 'app-gantt-subtask-row',
    // cspell:disable
    template: `
        <div
            class="flex w-full border-b transition-all duration-300 hover:bg-gray-50"
            role="row"
            [attr.aria-label]="getSubtaskAriaLabel()"
        >
            <div
                class="flex w-full min-w-[200px] items-center justify-between border-l border-r p-1.5 sm:w-1/3 sm:min-w-[300px]"
                [style.min-height.px]="config.subtaskHeight"
            >
                <div class="flex w-full items-center justify-between gap-2">
                    <div class="flex min-w-0 flex-1 items-center gap-2">
                        <!-- Indentation spacer -->
                        <div class="ms-5 flex-shrink-0 sm:ms-7"></div>

                        <!-- Custom subtask template -->
                        <ng-container
                            *ngIf="subtaskTemplate; else defaultSubtaskTemplate"
                        >
                            <ng-container
                                *ngTemplateOutlet="
                                    subtaskTemplate;
                                    context: { $implicit: item }
                                "
                            ></ng-container>
                        </ng-container>

                        <!-- Default subtask template -->
                        <ng-template #defaultSubtaskTemplate>
                            <button
                                class="min-w-0 flex-1 rounded p-1 text-start text-xs transition-colors hover:text-blue-600 focus:text-blue-600 focus:outline-none sm:text-sm"
                                [appTooltip]="formatTooltip()"
                                style="
                                    white-space: normal;
                                    word-wrap: break-word;
                                    overflow-wrap: break-word;
                                    line-height: 1.2;
                                "
                                (click)="onSubtaskClick()"
                                [attr.aria-label]="'Subtask: ' + item.name"
                            >
                                {{ item.name }}
                            </button>
                        </ng-template>
                    </div>

                    <!-- Progress badge - hide on mobile, show as tooltip -->
                    <span
                        *ngIf="
                            config.showProgress && item.progress !== undefined
                        "
                        class="mt-1 hidden flex-shrink-0 self-start rounded px-1.5 py-0.5 text-xs sm:flex"
                        [ngClass]="config.subtaskColors.progressBadge"
                        [attr.aria-label]="getProgressAriaLabel()"
                    >
                        {{ getProgressDisplay() }}%
                    </span>
                </div>
            </div>

            <!-- Subtask timeline visualization -->
            <div
                class="relative flex-1"
                [style.min-height.px]="config.subtaskHeight"
            >
                <div
                    class="grid h-full w-full"
                    [style.grid-template-columns]="
                        'repeat(' + displayDatesLength + ', 1fr)'
                    "
                ></div>
                <div
                    class="absolute left-0 top-0 h-full px-1"
                    style="width: 100%"
                >
                    <div
                        class="absolute top-1/2 -mt-2 h-3 rounded-md transition-all duration-200 hover:shadow-md sm:h-4"
                        [ngClass]="config.subtaskColors.background"
                        [ngStyle]="taskPosition"
                        [appTooltip]="formatTooltip()"
                        [attr.aria-label]="getSubtaskTimelineAriaLabel()"
                        role="progressbar"
                        [attr.aria-valuenow]="getProgressValue()"
                        [attr.aria-valuemin]="0"
                        [attr.aria-valuemax]="100"
                    >
                        <div
                            *ngIf="
                                config.showProgress &&
                                item.progress !== undefined
                            "
                            class="h-full rounded-md transition-all duration-300"
                            [ngClass]="config.subtaskColors.progress"
                            [style.width]="item.progress * 100 + '%'"
                        ></div>
                    </div>
                </div>
            </div>
        </div>
    `,
    // cspell:enable
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GanttSubtaskRowComponent<T = unknown> {
    @Input() public item!: GanttItem<T>;
    @Input() public config!: GanttConfig;
    @Input() public taskPosition!: TaskPosition;
    @Input() public displayDatesLength = 0;
    @Input() public subtaskTemplate?: TemplateRef<GanttTemplateContext<T>>;

    @Output() public subtaskClick = new EventEmitter<GanttItem<T>>();

    public onSubtaskClick(): void {
        this.subtaskClick.emit(this.item);
    }

    public getSubtaskAriaLabel(): string {
        const progressText =
            this.item.progress !== undefined
                ? `, ${Math.round(this.item.progress * 100)}% complete`
                : '';
        return `Subtask: ${this.item.name}${progressText}`;
    }

    public getProgressAriaLabel(): string {
        if (this.item.progress === undefined) return '';
        return `${Math.round(this.item.progress * 100)} percent complete`;
    }

    public getProgressDisplay(): string {
        if (this.item.progress === undefined) return '0';
        return (this.item.progress * 100).toFixed(0);
    }

    public getProgressValue(): number {
        return this.item.progress ? Math.round(this.item.progress * 100) : 0;
    }

    public getSubtaskTimelineAriaLabel(): string {
        const startDate = this.item.from
            ? this.formatDate(this.item.from)
            : 'Unknown start';
        const endDate = this.item.to
            ? this.formatDate(this.item.to)
            : 'Unknown end';
        return `${this.item.name} subtask timeline: ${startDate} to ${endDate}`;
    }

    public formatTooltip(): string {
        const fromDate = this.item.from ? this.formatDate(this.item.from) : '';
        const toDate = this.item.to ? this.formatDate(this.item.to) : '';

        if (fromDate && toDate) {
            return `${fromDate} - ${toDate}`;
        } else if (fromDate) {
            return `Start: ${fromDate}`;
        } else if (toDate) {
            return `End: ${toDate}`;
        }

        return this.item.name;
    }

    private formatDate(date: Date | string): string {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return dateObj.toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
        });
    }
}
