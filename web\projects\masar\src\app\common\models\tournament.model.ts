import { LibraryFile } from './library-file.model';
import { Pillar } from './pillar.model';
import { Standard } from './standard.model';

export interface Tournament {
    id: string;
    name: string;
    nameAr: string;
    nameEn: string;
    order: number;
    weight: number;
    years: number[];
    version: string;
    year: number;
    isHidden: boolean;
    libraryFile: LibraryFile;
    kpiCount: number;
    capabilityCount: number;
    pillarCount: number;
    standardCount: number;
    achieved: number;
    pillars: Pillar[];
    standards: Standard[];
    isDefault: boolean;
}
