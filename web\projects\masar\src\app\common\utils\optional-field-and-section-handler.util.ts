import {
    ResourceOptionalField,
    ResourceOptionalSection,
} from '@masar/common/models';
import { ChangeDetectorRef, ElementRef } from '@angular/core';
import { Validators } from '@angular/forms';
import { MnmFormState } from '@masar/shared/components';

const updateOptionalFields = (
    formState: MnmFormState,
    optionalFields: ResourceOptionalField[],
    fields: [string, string][]
): void => {
    fields.forEach(([optionalFieldName, controlName]) => {
        const { isEnabled, isRequired } = optionalFields.find(
            x => x.name === optionalFieldName
        );

        updateFieldVisibility(formState, isEnabled, isRequired, controlName);
    });
};

const updateOptionalSections = (
    formState: MnmFormState,
    changeDetectorRef: ChangeDetectorRef,
    optionalSections: ResourceOptionalSection[],
    sections: ([string, string, string, ElementRef] | [string, string])[]
): void => {
    sections.forEach(
        ([optionalSectionName, sectionName, controlName, elementRef]) => {
            updateSectionVisibility(
                formState,
                changeDetectorRef,
                optionalSections.find(x => x.name === optionalSectionName)
                    .isEnabled,
                sectionName,
                controlName,
                elementRef
            );
        }
    );
};

const updateFieldVisibility = (
    formState: MnmFormState,
    isEnabled: boolean,
    isRequired: boolean,
    fieldName: string
): void => {
    formState.setValidators(
        fieldName,
        isEnabled && isRequired ? [Validators.required] : []
    );

    if (isEnabled) formState.showField(fieldName);
    else formState.hideField(fieldName);
};

const updateSectionVisibility = (
    formState: MnmFormState,
    changeDetectorRef: ChangeDetectorRef,
    isEnabled: boolean,
    sectionName: string,
    controlName?: string,
    elementRef?: ElementRef
): void => {
    if (!isEnabled) {
        formState.hideField(sectionName);
        return;
    }

    formState.showField(sectionName);

    if (!controlName || !elementRef) return;

    setTimeout(() => {
        formState.get(controlName).customInputField = elementRef;
        changeDetectorRef.detectChanges();
    }, 10);
};

export const optionalFieldAndSectionHandler = (
    formState: MnmFormState,
    changeDetectorRef: ChangeDetectorRef,
    fields?: {
        optionalFields: ResourceOptionalField[];
        fields: [string, string][];
    },
    sections?: {
        optionalSections: ResourceOptionalSection[];
        sections: ([string, string, string, ElementRef] | [string, string])[];
    }
): void => {
    if (fields)
        updateOptionalFields(formState, fields.optionalFields, fields.fields);

    if (sections)
        updateOptionalSections(
            formState,
            changeDetectorRef,
            sections.optionalSections,
            sections.sections
        );
};
