import {
    Component,
    Input,
    NgM<PERSON>uleRef,
    <PERSON><PERSON><PERSON><PERSON>,
    OnInit,
} from '@angular/core';
import { PlanSubtask, Item } from '@masar/common/models';
import { finalize } from 'rxjs/operators';
import { TableController } from '@masar/common/misc/table/table-controller';
import { ModalService, NotificationService } from 'mnm-webapp';
import { MiscApiService } from '@masar/core/services';
import { PlanSubtaskListService } from './plan-subtask-list.service';
import { permissionList } from '@masar/common/constants';
import { getOrderByAsString } from '@masar/common/utils';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { DetailComponent } from '@masar/pages/plan-subtask/detail/detail.component';

interface Filter {
    keyword?: string;
    assigneeType?: string;
    departmentIds?: string[];
    includeChildDepartments?: boolean;
    teamIds?: string[];
    userIds?: string[];
    planIds?: string[];
    taskIds?: string[];
    from?: Date;
    to?: Date;
    status?: string;
}

interface HiddenFilters {
    keyword?: boolean;
    assigneeType?: boolean;
    departmentIds?: boolean;
    teamIds?: boolean;
    userIds?: boolean;
    planIds?: boolean;
    taskIds?: boolean;
    from?: boolean;
    to?: boolean;
}

interface HiddenColumns {
    name?: boolean;
    plan?: boolean;
    task?: boolean;
    assigned?: boolean;
    from?: boolean;
    to?: boolean;
    weight?: boolean;
    isApproved?: boolean;
}

@Component({
    selector: 'app-plan-subtask-list',
    templateUrl: './plan-subtask-list.component.html',
    providers: [PlanSubtaskListService],
})
export class PlanSubtaskListComponent implements OnInit, OnDestroy {
    @Input() public defaultFilter: Filter = {};
    @Input() public hiddenFilter: HiddenFilters = {};
    @Input() public hiddenColumns: HiddenColumns = {};
    @Input() public considerPlanFilterAsBase: boolean = false;
    @Input() public isFilterInUrl = false;

    public tableController: TableController<PlanSubtask, Filter>;

    public years: Item[];
    public assigneeTypes: Item[];
    public departments: Item[];
    public teams: Item[];
    public users: Item[];
    public plans: Item[];
    public tasks: Item[];

    public currentlyDeleting: string[] = [];

    public permissionList = permissionList;

    public constructor(
        private readonly router: Router,
        private readonly activatedRoute: ActivatedRoute,
        private readonly notificationService: NotificationService,
        private readonly planSubtaskListService: PlanSubtaskListService,
        private readonly translateService: TranslateService,
        private readonly modalService: ModalService,
        private readonly moduleRef: NgModuleRef<any>,
        miscApiService: MiscApiService
    ) {
        miscApiService
            .getList('plan-assignee-type')
            .subscribe(items => (this.assigneeTypes = items));

        miscApiService
            .departments()
            .subscribe(items => (this.departments = items));

        miscApiService
            .getList('team', { linkedWith: 'plan_subtask' }, true)
            .subscribe(items => (this.teams = items));

        miscApiService
            .getList('user', { linkedWith: 'plan_subtask' }, true)
            .subscribe(items => (this.users = items));

        miscApiService
            .getList('plan', undefined, true)
            .subscribe(items => (this.plans = items));

        miscApiService
            .getList('plan-task', undefined, true)
            .subscribe(items => (this.tasks = items));
    }

    public ngOnInit(): void {
        this.tableController = new TableController<PlanSubtask, Filter>(
            filter =>
                this.planSubtaskListService.list(
                    filter.data.keyword,
                    filter.data.assigneeType,
                    filter.data.departmentIds,
                    filter.data.includeChildDepartments,
                    filter.data.teamIds,
                    filter.data.userIds,
                    filter.data.planIds,
                    filter.data.taskIds,
                    filter.data.from ? new Date(filter.data.from) : null,
                    filter.data.to ? new Date(filter.data.to) : null,
                    filter.data.status,
                    getOrderByAsString(filter.orderBy),
                    filter.pageNumber,
                    filter.pageSize,
                    this.considerPlanFilterAsBase
                ),
            {
                data: {
                    keyword: this.defaultFilter.keyword || '',
                    departmentIds: this.defaultFilter.departmentIds || [],
                    teamIds: this.defaultFilter.teamIds || [],
                    userIds: this.defaultFilter.userIds || [],
                    planIds: this.defaultFilter.planIds || [],
                    taskIds: this.defaultFilter.taskIds || [],
                    from: this.defaultFilter.from || null,
                    to: this.defaultFilter.to || null,
                },
            },
            this.isFilterInUrl
                ? {
                      routingControls: {
                          router: this.router,
                          activatedRoute: this.activatedRoute,
                      },
                  }
                : {}
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public delete(item: PlanSubtask): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id);
        this.planSubtaskListService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }

    public showPlanSubTaskDialog(id: string): void {
        this.translateService
            .get('translate_procedure_details')
            .subscribe(async str => {
                const subject = new Subject();
                await this.modalService.show(DetailComponent, {
                    title: str,
                    moduleRef: this.moduleRef,
                    size: {
                        width: '100%',
                    },
                    onDismiss: () => {
                        subject.next();
                        subject.complete();
                    },
                    beforeInit: c => {
                        c.isDialog = true;
                        c.planSubtaskId = id;
                    },
                });
            });
    }
}
