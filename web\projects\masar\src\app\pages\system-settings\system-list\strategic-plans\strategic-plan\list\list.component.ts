import { Component, OnD<PERSON>roy } from '@angular/core';
import { StrategicPlanService } from '../services/strategic-plan.service';
import { StrategicPlan } from '@masar/common/models';
import { NotificationService } from 'mnm-webapp';
import { TableController } from '@masar/common/misc/table/table-controller';
import { finalize } from 'rxjs/operators';

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
})
export class ListComponent implements OnDestroy {
    public tableController: TableController<
        StrategicPlan,
        {
            keyword?: string;
        }
    >;

    public currentlyDeleting: string[] = [];

    public constructor(
        private strategicPlanService: StrategicPlanService,
        private notificationService: NotificationService
    ) {
        this.tableController = new TableController<
            StrategicPlan,
            {
                keyword?: string;
            }
        >(
            filter =>
                strategicPlanService.list(
                    filter.data.keyword,
                    filter.pageNumber,
                    filter.pageSize
                ),
            {
                data: {
                    keyword: '',
                },
            }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public deleteStrategicPlan(item: StrategicPlan): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id!);
        this.strategicPlanService
            .delete(item.id!)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }
}
