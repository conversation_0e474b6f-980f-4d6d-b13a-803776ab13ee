import { PermissionValue } from '@masar/common/types';
import { FlowItemType } from '@masar/features/flow/types';
import { Alert } from '@masar/common/interfaces/alert.interface';
import { TableListDataBasic } from './table-list-data-basic.interface';
import { DetailSectionData } from './detail-section-data.interface';
import { ProgressRingSection } from './progress-ring-section.interface';

/**
 * DetailPageData Interface
 * Describes the data structure required for rendering the detail page.
 */
export interface DetailPageData<T> {
    /**
     * The route specifies the URL path for navigation.
     * This will be used for constructing the New and Edit buttons.
     */
    route: string;

    /**
     * The title to display on the detail page.
     * The title is usually localized and translated dynamically.
     */
    title: string;

    /**
     * Permissions object, detailing the access level of the current user.
     * For example, 'write' permission controls the visibility of Edit and New buttons.
     */
    permissions: {
        write: PermissionValue;
    };

    /**
     * An array of sections that make up the body of the detail page.
     * Each section usually corresponds to a logical grouping of the item's properties.
     */
    sections?: (
        | DetailSectionData<T>
        | TableListDataBasic<T>
        | ProgressRingSection<T>
    )[];

    sectionsFactory?: (
        item: T | undefined
    ) => (
        | DetailSectionData<T>
        | TableListDataBasic<T>
        | ProgressRingSection<T>
    )[];

    dynamicFlow?: {
        show?: (item: T) => boolean;
        type: FlowItemType;
    };

    alerts?: (item: T) => Alert[];
}
