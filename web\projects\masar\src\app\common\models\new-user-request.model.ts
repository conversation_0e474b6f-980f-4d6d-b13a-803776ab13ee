import { FlowItem } from '@masar/features/flow/interfaces';
import { Department } from '@masar/common/models/department.model';
import { User } from '@masar/common/models/user.model';

export interface NewUserRequest
    extends FlowItem<'submitted' | 'approved' | 'rejected'> {
    id: string;
    nameAr: string;
    nameEn: string;
    email: string;
    creationTime: Date;
    gender: string;
    employeeNumber: string;
    rank: string;
    requestedPermissions: string;
    department: Department;
    user: User;
    flowState: 'submitted' | 'approved' | 'rejected';
    password: string;
    passwordConfirmation: string;
}
