import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { catchError, filter } from 'rxjs/operators';
import { flippingCard } from '@masar/common/animations';
import { Pillar } from '@masar/common/models';
import { ImageApiService } from '@masar/core/services';
import { ExcellenceService } from '../excellence.service';

@Component({
    selector: 'app-pillar-detail',
    templateUrl: './pillar-detail.component.html',
    animations: [...flippingCard],
})
export class PillarDetailComponent {
    public pillar: Pillar;

    public constructor(
        public imageApiService: ImageApiService,
        activatedRoute: ActivatedRoute,
        router: Router,
        excellenceService: ExcellenceService
    ) {
        const pillarId = activatedRoute.snapshot.paramMap.get('pillarId');
        if (!pillarId) {
            router.navigate(['']);
            return;
        }

        excellenceService
            .getPillar(pillarId)
            .pipe(
                catchError(() => {
                    router.navigate(['']);
                    return of(null);
                }),
                filter(x => x !== null)
            )
            .subscribe(item => (this.pillar = item));
    }
}
