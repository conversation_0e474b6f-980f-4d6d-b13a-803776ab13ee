import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MnmFormState } from '@masar/shared/components';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { AfterSubmitNavigateTo } from '@masar/common/types';

@Component({
    selector: 'app-new-page',
    templateUrl: './new-page.component.html',
})
export class NewPageComponent implements OnInit {
    @Input() public label: string;

    @Input() public labelBack: string;

    @Input() public link: string[];

    @Input() public formState: MnmFormState;

    @Input() public hidePreviewButton: boolean;

    @Input() public afterSubmitNavigateTo?: AfterSubmitNavigateTo;

    @Input() public hideNavigateCases?: AfterSubmitNavigateTo[];

    @Input() public getForEditCb: <T>(id: string) => Observable<T>;

    @Input() public createCb: <T>(item: T) => Observable<T>;

    @Input() public updateCb: <T>(item: T) => Observable<T>;

    @Output() public mode = new EventEmitter<'new' | 'edit'>();

    public id?: string;

    public constructor(private readonly activatedRoute: ActivatedRoute) {}

    public ngOnInit(): void {
        const mode = this.activatedRoute.snapshot.data['mode'] as
            | 'new'
            | 'edit';

        if (!mode) throw new Error('Mode is not defined');

        this.mode.emit(mode);

        if (mode !== 'edit') return;

        this.id = this.activatedRoute.snapshot.params['id'];

        if (!this.id) throw new Error('Id is not defined');
    }
}
