<ng-container>
    <app-fieldset legend="{{ 'translate_optional_fields' | translate }}">
        <div
            *appWaitUntilLoaded="benchmarkSetting"
            class="mb-5 grid grid-cols-3 items-center gap-5"
        >
            <ng-container *ngFor="let item of benchmarkSetting.optionalFields">
                <!-- Name -->
                <span
                    class="border-s-4 border-green-500 bg-gray-200 p-2 text-gray-600"
                >
                    {{ 'translate_' + item.name | translate }}
                </span>

                <!-- Is enabled -->
                <label>
                    <input
                        type="checkbox"
                        [(ngModel)]="item.isEnabled"
                        class="me-2"
                    />
                    <span>{{ 'translate_is_enabled_qm' | translate }}</span>
                </label>

                <!-- Is required -->
                <label>
                    <input
                        type="checkbox"
                        [(ngModel)]="item.isRequired"
                        class="me-2"
                    />
                    <span>{{ 'translate_is_required_qm' | translate }}</span>
                </label>
            </ng-container>
        </div>
    </app-fieldset>

    <div class="text-center">
        <button
            type="submit"
            class="btn btn-primary"
            (click)="save()"
            [disabled]="isSubmitting"
        >
            <app-loading-ring
                *ngIf="isSubmitting"
                class="me-2"
            ></app-loading-ring>
            <i class="fa-light fa-save me-2"></i>
            <span>{{ 'translate_save' | translate }}</span>
        </button>
    </div>
</ng-container>
