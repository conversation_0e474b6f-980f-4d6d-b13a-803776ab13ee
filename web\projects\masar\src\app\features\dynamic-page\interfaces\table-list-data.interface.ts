import { PermissionValue } from '@masar/common/types';
import { FlowItemType } from '@masar/features/flow/types';
import { TableListDataAction } from './table-list-data-actions.interface';
import { DetailPageData } from './detail-page-data.interface';
import { TableListDataTab } from './table-list-data-tabs.interface';
import { TableListColumn, TableListFilter } from '../types';

/**
 * Interface for TableListData
 *
 * This interface defines the data structure expected for a list page,
 * which can be used in the rendering of list-based UI components.
 *
 * @template T The type parameter indicating the type of the items that will
 *             be displayed in the list.
 */
export interface TableListData<T, NewItem = unknown> {
    /**
     * Title of the list page.
     *
     * This string value will be used as the title of the list page,
     * typically displayed at the top.
     */
    title: string;

    /**
     * Routing path for the list page.
     *
     * This string value specifies the route to the list page. This
     * could be used for navigation purposes.
     */
    route?: string[];

    /**
     * Mode for editing items in the list.
     *
     * The `editMode` property specifies how editing actions should be performed in the list.
     * This can be useful for determining the UI/UX behavior for item modification.
     *
     * - 'both': Allows both inline editing and editing via separate events or pages.
     * - 'event-emitter': Editing will be performed by emitting an event, typically caught by a parent component for further action.
     */
    editMode?: 'both' | 'event-emitter' | 'none';

    /**
     * Columns definition for the list.
     *
     * An array of PropertyValueOptions<T> that define the columns to
     * be displayed on the list page. Each PropertyValueOptions object
     * provides the configuration for individual columns.
     *
     * @see PropertyValueOptions
     */
    columns: TableListColumn<T>[];

    /**
     * Permissions for write and delete actions.
     *
     * specifies the permissions required for
     * write and delete actions on the list. If present, the `write`
     * and `delete` fields should be of type PermissionValue.
     */
    permissions: { write: PermissionValue; delete: PermissionValue };

    actions?: TableListDataAction<T>[];

    tabs?: TableListDataTab<T>[];

    actionsFactory?: (item: T) => TableListDataAction<T>[];

    tabsFactory?: (item: T) => TableListDataTab<T>[];

    detail?: Omit<DetailPageData<T>, 'route' | 'permissions'> & {
        endPoint: string;
    };

    dynamicFlow?: {
        show?: (item: T) => boolean;
        type: FlowItemType;
    };

    filters?: TableListFilter[];

    canEdit?: (item: T) => boolean;

    canDelete?: (item: T) => boolean;

    groupByKey?: string;

    mapperFn?: (item: T) => NewItem;
}
