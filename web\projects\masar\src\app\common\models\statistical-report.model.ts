import { StatisticalReportCategory } from './statistical-report-category.model';
import { StatisticalReportCategoryResult } from './statistical-report-category-result.model';

export interface StatisticalReport {
    id: string;
    name: string;
    nameAr: string;
    nameEn: string;
    cycle: string;
    initialYear: number;
    isPublished: boolean;
    isLocked: boolean;
    isDecreaseBest: boolean;
    categories: StatisticalReportCategory[];
    results: StatisticalReportCategoryResult[];
    canFullEdit: boolean;
}
