import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
    Filter,
    OrderCallback,
    RoutingControls,
    TableController,
    TableResult,
} from '@masar/common/misc/table';
import { NotificationService, Result } from 'mnm-webapp';
import { environment } from '@masar/env/environment';
import { finalize, map, tap } from 'rxjs/operators';
import { HttpQueryParameters } from './http-query-parameters.interface';
import { getBodyAsUrlParams, getHttpParameters } from './utils';

export abstract class HttpCrud<T = unknown, F = unknown, NewItem = unknown> {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    private _tableController?: TableController<T, F>;

    // eslint-disable-next-line @typescript-eslint/naming-convention
    private _isDeleting: Record<string, boolean> = {};

    protected constructor(
        private readonly httpClient: HttpClient,
        private readonly notificationService: NotificationService,
        public endPoint?: string,
        public bodyParameterName?: string
    ) {}

    public get tableController(): TableController<T, F> | undefined {
        return this._tableController;
    }

    public get isDeleting(): Record<string, boolean> {
        return this._isDeleting;
    }

    public get url(): string {
        return `${environment.apiUrl}/${this.endPoint}`;
    }

    public list(
        httpQueryParameters?: HttpQueryParameters,
        mapperFn?: (item: T) => NewItem
    ): Observable<TableResult<T>> {
        const params = getHttpParameters(httpQueryParameters);
        return this.httpClient
            .get<Result<TableResult<T>>>(this.url, { params })
            .pipe(
                map(result => result.extra),
                tap(extra => {
                    if (mapperFn) extra.items.map(item => mapperFn(item));
                })
            );
    }

    public create(item: T): Observable<T> {
        return this.httpClient
            .post<Result<T>>(this.url, this.getBodyData(item))
            .pipe(map(result => result.extra));
    }

    public update(item: T): Observable<T> {
        return this.httpClient
            .put<Result<T>>(this.url, this.getBodyData(item))
            .pipe(map(result => result.extra));
    }

    public get(id: string, forEdit: boolean = false): Observable<T> {
        const params = getHttpParameters({ forEdit });
        return this.httpClient
            .get<Result<T>>(`${this.url}/${id}`, { params })
            .pipe(map(result => result.extra));
    }

    public delete(id: string, onSuccessCallBack?: () => void): void {
        this._isDeleting = { ...this._isDeleting, [id]: true };

        this.httpClient
            .delete<Result>(`${this.url}/${id}`)
            .pipe(
                finalize(
                    () =>
                        (this._isDeleting = {
                            ...this._isDeleting,
                            [id]: false,
                        })
                ),
                map(result => result.messages[0]),
                tap(message => {
                    if (message)
                        this.notificationService.notifySuccess(message);
                    this.tableController.filter$.next(false);
                })
            )
            .subscribe(() => onSuccessCallBack?.());
    }

    public stop(): void {
        this.tableController?.stop();
    }

    public getCb(): (id: string) => Observable<T> {
        return id => this.get(id, false);
    }

    public getForEditCb(): (id: string) => Observable<T> {
        return id => this.get(id, true);
    }

    public createCb(): (item: T) => Observable<T> {
        return item => this.create(item);
    }

    public updateCb(): (item: T) => Observable<T> {
        return item => this.update(item);
    }

    protected startTableController(
        callback: (filter: Filter<F>) => Observable<TableResult<T>>,
        filter?: { pageNumber?: number; pageSize?: number; data?: F },
        options?: {
            routingControls: RoutingControls;
            orderCallback?: OrderCallback<T, F>;
        }
    ): void {
        this._tableController = new TableController<T, F>(
            callback,
            filter,
            options
        );
        this._tableController.start();
    }

    private getBodyData(item: T): string {
        return getBodyAsUrlParams(item, this.bodyParameterName);
    }
}
