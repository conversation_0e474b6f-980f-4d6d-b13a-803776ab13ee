import { RouterModule, Routes } from '@angular/router';
import { LoadTranslationsResolver } from '@ng-omar/translation';
import { systemList } from '@masar/pages/system-settings/system-list/constants/system-list.constants';
import { SystemListComponent } from '@masar/pages/system-settings/system-list/system-list.component';
import { ListComponent } from './list/list.component';
import { NewComponent } from './new/new.component';
import { NgModule } from '@angular/core';
import { permissionList } from '@masar/common/constants';

const routes: Routes = [
    {
        path: '',
        component: SystemListComponent,
        resolve: { translations: LoadTranslationsResolver },
        children: [
            ...systemList.map(item => ({
                path: item.path,
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: item.permissionId },
                children: [
                    {
                        path: '',
                        component: ListComponent,
                        data: {
                            title: `translate_${item.plural}`,
                            breadcrumb: [`translate_${item.plural}`],
                            item,
                        },
                    },
                    {
                        path: 'new',
                        component: NewComponent,
                        data: {
                            mode: 'new',
                            title: `translate_new_${item.label}`,
                            breadcrumb: [
                                `translate_${item.plural}`,
                                'translate_add_new',
                            ],
                            item,
                        },
                    },
                    {
                        path: 'edit/:id',
                        component: NewComponent,
                        data: {
                            mode: 'edit',
                            title: `translate_edit_${item.label}`,
                            breadcrumb: [
                                `translate_${item.plural}`,
                                'translate_edit',
                            ],
                            item,
                        },
                    },
                ],
            })),
            {
                path: 'strategic-goal',
                loadChildren: () =>
                    import('./strategic-goal/strategic-goal.module').then(
                        x => x.StrategicGoalModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.strategicGoal },
            },
            {
                path: 'strategic-plans',
                loadChildren: () =>
                    import(
                        '@masar/pages/system-settings/system-list/strategic-plans/strategic-plan.module'
                    ).then(x => x.StrategicPlanModule),
                resolve: { translations: LoadTranslationsResolver },
                data: {
                    permissionId: permissionList.fullAccess,
                    label: 'translate_strategic_plans',
                    icon: 'fa-light fa-bullseye-pointer',
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class SystemListRoutingModule {}
