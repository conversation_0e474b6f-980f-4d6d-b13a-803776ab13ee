<app-content
    contentTitle="{{ 'translate_kpi_benchmark' | translate }}"
    *ngIf="!filteredItems || filteredItems.length > 0"
>
    <!-- Tools for filtering -->
    <ng-container tools *ngIf="years && years.length > 0">
        <ng-select
            [items]="years"
            placeholder="{{ 'translate_year' | translate }}"
            [(ngModel)]="selectedYear"
            (change)="yearChanged()"
            [clearable]="false"
        >
        </ng-select>
    </ng-container>

    <!-- Content -->
    <app-list-loading [items]="filteredItems" content>
        <div class="table-responsive">
            <table class="table-bordered table-hover table">
                <thead class="thead-dark">
                    <tr>
                        <th>
                            {{ 'translate_entity_name' | translate }}
                        </th>
                        <th>
                            {{ 'translate_result' | translate }}
                        </th>
                        <th>
                            {{ 'translate_attachment' | translate }}
                        </th>
                        <th>
                            {{ 'translate_competitive_position' | translate }}
                        </th>
                        <th>
                            {{ 'translate_competitive_effect' | translate }}
                        </th>
                        <ng-container *ngIf="mode === 'default'">
                            <th *appHasPermissionId="permissionList.kpi"></th>
                        </ng-container>
                    </tr>
                </thead>

                <tbody>
                    <tr *ngFor="let item of filteredItems; let idx = index">
                        <!-- Entity name -->
                        <td>
                            {{ item.entityName }}
                        </td>

                        <!-- External result -->
                        <td class="text-center">
                            {{ item.result }}
                        </td>

                        <!-- Attachment -->
                        <td class="text-center">
                            <app-library-file-name
                                *ngIf="item.libraryFile"
                                [libraryFile]="item.libraryFile"
                            ></app-library-file-name>
                        </td>

                        <!-- Competitive position -->
                        <td class="text-center">
                            {{ item.competitivePosition }}
                        </td>

                        <!-- Competitive effect -->
                        <td class="text-center">
                            {{ item.competitiveEffect }}
                        </td>

                        <!-- Control -->
                        <ng-container *ngIf="mode === 'default'">
                            <td *appHasPermissionId="permissionList.kpi">
                                <app-dropdown>
                                    <!-- Edit -->
                                    <button
                                        (click)="
                                            showCreateBenchmarkDialog(item)
                                        "
                                        class="btn btn-sm btn-info me-2"
                                    >
                                        <i class="fa-light fa-edit fa-fw"></i>
                                        <!--                                        {{ 'translate_edit' | translate }}-->
                                    </button>

                                    <!-- Delete -->
                                    <button
                                        class="btn btn-sm btn-danger"
                                        [disabled]="
                                            currentlyDeletingBenchmarks.includes(
                                                item.id
                                            )
                                        "
                                        (confirm)="deleteBenchmark(item)"
                                        [swal]="{
                                            title:
                                                'translate_delete_this_item_question_mark'
                                                | translate,
                                            confirmButtonText:
                                                'translate_yes' | translate,
                                            cancelButtonText:
                                                'translate_cancel' | translate,
                                            showCancelButton: true,
                                            showCloseButton: true
                                        }"
                                    >
                                        <i class="fas fa-trash fa-fw"></i>
                                        <!--                                        {{ 'translate_delete' | translate }}-->
                                    </button>
                                </app-dropdown>
                            </td>
                        </ng-container>
                    </tr>
                </tbody>
            </table>
        </div>
    </app-list-loading>
</app-content>
