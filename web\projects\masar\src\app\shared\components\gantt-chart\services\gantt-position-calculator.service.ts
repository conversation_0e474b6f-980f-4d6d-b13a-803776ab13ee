import { Injectable } from '@angular/core';
import { TranslationService } from '@ng-omar/translation';
import { GanttDateService } from './gantt-date.service';
import { GanttItem } from '../interfaces/gantt.interfaces';

export interface TaskPosition {
    left: string;
    width: string;
}

export interface TimelineBounds {
    startDate: Date;
    endDate: Date;
    totalDays: number;
}

@Injectable()
export class GanttPositionCalculatorService {
    private positionCache = new Map<string, TaskPosition>();

    public constructor(
        private dateService: GanttDateService,
        private translationService: TranslationService
    ) {}

    /**
     * Calculates the position of a task within the timeline
     */
    public calculateTaskPosition<T>(
        item: GanttItem<T>,
        timelineBounds: TimelineBounds
    ): TaskPosition {
        if (!this.isValidItem(item) || !timelineBounds) {
            return { left: '0%', width: '0%' };
        }

        const cacheKey = this.generateCacheKey(item, timelineBounds);

        if (this.positionCache.has(cacheKey)) {
            return this.positionCache.get(cacheKey)!;
        }

        const position = this.computePosition(item, timelineBounds);
        this.positionCache.set(cacheKey, position);

        return position;
    }

    /**
     * Clears the position cache
     */
    public clearCache(): void {
        this.positionCache.clear();
    }

    /**
     * Creates timeline bounds from start and end dates
     */
    public createTimelineBounds(
        startDate: Date,
        endDate: Date
    ): TimelineBounds {
        const normalizedStart = this.dateService.getFirstDayOfMonth(startDate);
        const normalizedEnd = this.dateService.getLastDayOfMonth(endDate);
        const totalDays =
            this.dateService.getDaysDifference(normalizedStart, normalizedEnd) +
            1;

        return {
            startDate: normalizedStart,
            endDate: normalizedEnd,
            totalDays,
        };
    }

    private isValidItem<T>(item: GanttItem<T>): boolean {
        return !!(item?.from && item?.to);
    }

    private generateCacheKey<T>(
        item: GanttItem<T>,
        bounds: TimelineBounds
    ): string {
        // Include language direction in cache key to prevent incorrect cached positions
        const isRtl = this.translationService.currentLanguage?.isRtl || false;
        return `${item.id}_${item.from}_${
            item.to
        }_${bounds.startDate.getTime()}_${bounds.endDate.getTime()}_${isRtl}`;
    }

    private computePosition<T>(
        item: GanttItem<T>,
        bounds: TimelineBounds
    ): TaskPosition {
        const taskStart = this.dateService.parseUtcDate(item.from)!;
        const taskEnd = this.dateService.parseUtcDate(item.to)!;

        // Bound task dates within timeline
        const boundedTaskStart = new Date(
            Math.max(bounds.startDate.getTime(), taskStart.getTime())
        );
        const boundedTaskEnd = new Date(
            Math.min(bounds.endDate.getTime(), taskEnd.getTime())
        );

        // Calculate positions
        const taskStartDays = this.dateService.getDaysDifference(
            bounds.startDate,
            boundedTaskStart
        );
        const taskDurationDays = Math.max(
            1,
            this.dateService.getDaysDifference(
                boundedTaskStart,
                boundedTaskEnd
            ) + 1
        );

        // Get current language direction
        const isRtl = this.translationService.currentLanguage?.isRtl || false;

        let leftPercentage: number;
        const widthPercentage = (taskDurationDays / bounds.totalDays) * 100;

        if (isRtl) {
            // For RTL layout - calculate from right
            const rightOffset =
                bounds.totalDays - taskStartDays - taskDurationDays;
            leftPercentage = (rightOffset / bounds.totalDays) * 100;
        } else {
            // For LTR layout - calculate from left
            leftPercentage = (taskStartDays / bounds.totalDays) * 100;
        }

        // Ensure bounds
        const finalLeftPercentage = Math.max(0, Math.min(100, leftPercentage));
        const finalWidthPercentage = Math.max(
            0.5,
            Math.min(100 - finalLeftPercentage, widthPercentage)
        );

        return {
            left: `${finalLeftPercentage}%`,
            width: `${finalWidthPercentage}%`,
        };
    }
}
