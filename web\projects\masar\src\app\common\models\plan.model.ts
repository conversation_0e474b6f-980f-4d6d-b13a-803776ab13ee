import { Department } from './department.model';
import { GovernmentStrategicGoal } from './government-strategic-goal.model';
import { Operation } from './operation.model';
import { PlanInput } from './plan-input.model';
import { PlanResource } from './plan-resource.model';
import { Partner } from './partner.model';
import { Team } from './team.model';
import { User } from './user.model';
import { PlanTask } from './plan-task.model';
import { StrategicGoal } from './strategic-goal.model';
import { PlanCategory } from './plan-category.model';
import { PlanUserAbility } from './plan-user-ability.model';
import { Kpi } from './kpi.model';
import { FlowItem } from '@masar/features/flow/interfaces';
import { DefaultFlowState } from '@masar/features/flow/types';
import { Item, MinistryStrategicGoal, Policy } from '@masar/common/models';
import { PlanFuturePlan } from './plan-future-plan.model';
import { PlanOtherResource } from '@masar/common/models/plan-other-resource.interface';

export interface Plan extends FlowItem<DefaultFlowState> {
    id: string;
    code: string;
    year: number;
    name: string;
    assignedDepartment: Department;
    assignedTeam: Team;
    assignedUser: User;

    // Based on the approval of the achievement
    // of the procedures of the plan.
    isApproved: boolean;

    progress: number;
    expectedProgress: number;
    category: PlanCategory;
    from: Date;
    to: Date;
    governmentStrategicGoal: GovernmentStrategicGoal;
    governmentDirections: string;
    otherPartners: string;
    initiatives: string;
    resources: PlanResource[];
    otherResources: PlanOtherResource[];
    strategicGoals: StrategicGoal[];
    ministryStrategicGoals: MinistryStrategicGoal[];
    inputs: {
        planInput: PlanInput;
        text: string;
    }[];
    partners: Partner[];
    operations: Operation[];
    kpis: Kpi[];
    tasks: PlanTask[];
    currentApprovingDepartment: Department;

    userAbility: PlanUserAbility;

    description: string;
    workScope: string;
    implementationRequirement: string;
    expectedOutputs: { type: string; output: string }[];
    lessonsLearned: {
        description: string;
        learningProcedure: string;
        recommendations: string;
    }[];
    challenges: {
        description: string;
        responsible: string;
        actions: string;
    }[];
    communicationProcesses: {
        activity: string;
        from: string;
        to: string;
        communicationMethod: string;
        communicationMethodRepetition: string;
        communicationGoal: string;
        importanceLevel: string;
    }[];
    risks: {
        description: string;
        occurringProbability: string;
        impact: string;
        responsible: string;
        actions: string;
    }[];
    dependencies: {
        plan: Plan;
        reason: string;
        relation: string;
        recommendations: string;
    }[];
    policies: {
        policy: Policy;
        hasPlan: boolean;
    }[];

    // Financial Requirements Details
    isBudgetAllocated: boolean;
    financialRequirements: {
        label: string;
        value: number;
    }[];
    financialStages: { value: number }[];

    expectedBenefits: {
        description: string;
        achievementTarget: string;
        isAudited: boolean;
    }[];
    futurePlans: PlanFuturePlan[];
    totalWeight: number;
    partneringDepartments: Item[];
    isRuleCheckPassed: boolean;
    pinned: boolean;
}
