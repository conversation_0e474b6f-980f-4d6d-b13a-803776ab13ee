import { Activity } from './activity.model';
import { Award } from './award.model';
import { Innovation } from './innovation.model';
import { TrainingProgram } from './training-programs.model';

export interface Innovator {
    id: string;
    name: string;
    employeeNumber: string;
    rank: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    hasALogo: boolean;
    innovations: Innovation[];
    awards: Award[];
    activities: Activity[];
    trainingPrograms: TrainingProgram[];
    innovationCount: number;
    awardCount: number;
    activityCount: number;
    trainingHours: number;
    trainingProgramCount: number;
}
