<h1 class="border-b py-3 text-center text-3xl font-bold">ادخال البيانات</h1>

<form
    [formGroup]="dataEntryFilterForm"
    class="grid w-full grid-cols-1 gap-2 px-2 py-4 text-center md:grid-cols-4"
    autocomplete="off"
>
    <!-- Department Field -->
    <select
        name="center"
        id="department"
        formControlName="departmentId"
        class="bg-blur w-full rounded-full border-none text-center"
    >
        <option [value]="null" disabled selected>الادارة</option>
        <option *ngFor="let department of departments" [value]="department.id">
            {{ department.name }}
        </option>
    </select>

    <!-- Center Field -->
    <select
        name="center"
        id="center"
        formControlName="centerId"
        class="bg-blur w-full rounded-full border-none text-center"
    >
        <option [value]="null" disabled selected>قناة تقديم الخدمة</option>
        <option *ngFor="let center of centers" [value]="center.id">
            {{ center.name }}
        </option>
    </select>

    <!-- Year Field -->
    <select
        name="center"
        id="year"
        formControlName="year"
        class="bg-blur w-full rounded-full border-none text-center"
    >
        <option [value]="null" disabled selected>السنة</option>
        <option *ngFor="let year of years" [value]="year">{{ year }}</option>
    </select>

    <!-- Month Field -->
    <select
        name="month"
        id="month"
        formControlName="month"
        class="bg-blur w-full rounded-full border-none text-center"
    >
        <option [value]="null" disabled selected>الشهر</option>
        <option *ngFor="let month of months" [value]="month.value">
            {{ month.label }}
        </option>
    </select>
</form>

<!-- Create -->
<div class="h-[500px] max-h-[70vh] px-4 pb-4">
    <p
        class="flex h-full items-center justify-center text-gray-800"
        *ngIf="dataEntryFilterForm.invalid"
    >
        الرجاء تحديد الخيارات لإظهار النتيجة
    </p>

    <form
        class="h-full overflow-auto"
        (ngSubmit)="onSaveForm()"
        *ngIf="dataEntryForm.controls.length"
    >
        <table
            class="mx-auto mt-2 w-full table-auto"
            aria-label="عدد المتعاملين"
        >
            <thead>
                <tr class="border-b">
                    <th class="p-2 text-start">الخدمات</th>
                    <th class="w-32 whitespace-nowrap p-2 text-center">
                        عدد المتعاملين
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr
                    *ngFor="
                        let control of dataEntryForm.controls;
                        let i = index
                    "
                    class="border-b"
                >
                    <td>
                        <span
                            [title]="control.value.serviceLabel"
                            class="line-clamp-1"
                            >{{ control.value.serviceLabel }}</span
                        >
                    </td>
                    <td class="px-1 text-center">
                        <input
                            [formControl]="control.controls.value"
                            type="number"
                            name="value"
                            id="value"
                            class="bg-blur my-2 w-full rounded-full border-none px-0.5 py-2 text-center font-bold"
                        />
                    </td>
                </tr>
            </tbody>
        </table>

        <button
            type="submit"
            class="bg-blur mt-2 w-full rounded-full p-3"
            [disabled]="isSubmittingDataEntries"
        >
            حفظ
        </button>
    </form>
</div>
