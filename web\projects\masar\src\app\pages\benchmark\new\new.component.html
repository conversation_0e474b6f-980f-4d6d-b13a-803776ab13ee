<app-page
    pageTitle="{{
        (mode === 'new'
            ? 'translate_add_new_benchmark'
            : 'translate_edit_benchmark'
        ) | translate
    }}"
>
    <!-- Tools -->
    <ng-container tools>
        <!-- List -->
        <a
            [routerLink]="['', 'benchmark']"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-share me-2"></i>
            <span>{{ 'translate_benchmarks_list' | translate }}</span>
        </a>

        <!-- Detail -->
        <a
            *ngIf="mode === 'edit'"
            [routerLink]="[
                '',
                'benchmark',
                'detail',
                formState.group.controls['id'].value
            ]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-eye me-2"></i>
            <span>{{ 'translate_preview' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <ng-container content>
        <mnm-form
            *ngIf="formState"
            [state]="formState"
            [translateLabels]="true"
        >
            <div class="mt-2 text-center">
                <button
                    type="submit"
                    class="btn-lg btn btn-primary"
                    (click)="submit()"
                    [disabled]="isSubmitting"
                >
                    <app-loading-ring
                        *ngIf="isSubmitting"
                        class="me-2"
                    ></app-loading-ring>
                    <i class="fa-light fa-save me-2"></i>
                    <span>{{ 'translate_save' | translate }}</span>
                </button>
            </div>
        </mnm-form>
    </ng-container>
</app-page>

<!-- Department field -->
<ng-template #departmentFieldRef>
    <app-item-list-field
        [isMulti]="false"
        [formControl]="formState.group.controls['department']"
        [parentFetcher]="departmentParentFetcher"
        [childrenFetcher]="departmentChildrenFetcher"
    ></app-item-list-field>
</ng-template>

<!-- Operation field -->
<ng-template #operationListFieldRef>
    <app-fieldset legend="{{ 'translate_managing_operations' | translate }}">
        <app-item-list-field
            [formControl]="formState.group.controls['operations']"
            [parentFetcher]="operationParentFetcher"
            [childrenFetcher]="operationChildrenFetcher"
        ></app-item-list-field>
    </app-fieldset>
</ng-template>

<!-- Other management field -->
<ng-template #otherManagementsFieldRef>
    <app-fieldset legend="{{ 'translate_other_management' | translate }}">
        <app-list-field
            [itemTemplate]="template"
            [formControl]="formState.group.controls['otherManagements']"
            [validItems]="validOtherManagements"
        >
            <ng-template
                #template
                let-item="item"
                let-emitChange="emitChange"
                let-isDisabled="isDisabled"
            >
                <div class="flex flex-col gap-2 md:flex-row">
                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label>{{ 'translate_type' | translate }}</label>
                        <ng-select
                            [items]="otherManagementTypes"
                            bindLabel="name"
                            bindValue="id"
                            [ngModel]="item.type"
                            (ngModelChange)="item.type = $event; emitChange()"
                            [disabled]="isDisabled"
                        >
                        </ng-select>
                    </div>
                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label>{{ 'translate_detail' | translate }}</label>
                        <input
                            type="text"
                            [ngModel]="item.detail"
                            (ngModelChange)="item.detail = $event; emitChange()"
                            [disabled]="isDisabled"
                        />
                    </div>
                </div>
            </ng-template>
        </app-list-field>
    </app-fieldset>
</ng-template>

<!-- Visitors field -->
<ng-template #visitorsFieldRef>
    <app-fieldset legend="{{ 'translate_visitor_details' | translate }}">
        <app-list-field
            [itemTemplate]="template"
            [formControl]="formState.group.controls['visitors']"
            [validItems]="validVisitors"
        >
            <ng-template
                #template
                let-item="item"
                let-emitChange="emitChange"
                let-i="index"
            >
                <div
                    class="mb-5 grid grid-cols-2 grid-rows-5 gap-2 rounded bg-black bg-opacity-5 p-4 md:grid-cols-4 md:grid-rows-3"
                >
                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label
                            >{{ 'translate_employee_number' | translate }}
                            <span class="text-red-500"> * </span>
                        </label>
                        <input
                            type="text"
                            [ngModel]="item.employeeNumber"
                            (ngModelChange)="
                                onVisitorEmployeeNumberChange(
                                    $event,
                                    item,
                                    emitChange,
                                    i
                                )
                            "
                        />
                    </div>

                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label
                            >{{ 'translate_rank' | translate }}
                            <span class="text-red-500"> * </span>
                        </label>
                        <input
                            type="text"
                            [ngModel]="item.rank"
                            (ngModelChange)="item.rank = $event; emitChange()"
                        />
                    </div>

                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label
                            >{{ 'translate_full_name' | translate }}
                            <span class="text-red-500"> * </span>
                        </label>
                        <input
                            type="text"
                            [ngModel]="item.fullName"
                            (ngModelChange)="
                                item.fullName = $event; emitChange()
                            "
                        />
                    </div>

                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label
                            >{{ 'translate_department' | translate }}
                            <span class="text-red-500"> * </span>
                        </label>
                        <input
                            type="text"
                            [ngModel]="item.department"
                            (ngModelChange)="
                                item.department = $event; emitChange()
                            "
                        />
                    </div>

                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label
                            >{{ 'translate_employment_title' | translate }}
                            <span class="text-red-500"> * </span>
                        </label>
                        <input
                            type="text"
                            [ngModel]="item.employmentTitle"
                            (ngModelChange)="
                                item.employmentTitle = $event; emitChange()
                            "
                        />
                    </div>

                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label
                            >{{ 'translate_description' | translate }}
                            <span class="text-red-500"> * </span>
                        </label>
                        <input
                            type="text"
                            [ngModel]="item.description"
                            (ngModelChange)="
                                item.description = $event; emitChange()
                            "
                        />
                    </div>

                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label
                            >{{ 'translate_phone' | translate }}
                            <span class="text-red-500"> * </span>
                        </label>
                        <input
                            type="text"
                            [ngModel]="item.phone"
                            (ngModelChange)="item.phone = $event; emitChange()"
                        />
                    </div>

                    <div class="flex flex-1 flex-col justify-center gap-2">
                        <label
                            >{{ 'translate_email' | translate }}
                            <span class="text-red-500"> * </span>
                        </label>
                        <input
                            type="text"
                            [ngModel]="item.email"
                            (ngModelChange)="item.email = $event; emitChange()"
                        />
                    </div>
                </div>
            </ng-template>
        </app-list-field>
    </app-fieldset>
</ng-template>

<!-- Kpi results field-->
<ng-template #kpiResultsFieldRef>
    <app-benchmark-kpi-result-field
        [formControl]="formState.group.controls['kpiResults']"
    ></app-benchmark-kpi-result-field>
</ng-template>

<ng-template #benefitRateRef>
    <div class="w-full" *ngIf="isBenefitRateEnabled">
        <app-progress-bar-input
            [formControl]="$any(formState.group.controls['benefitRate'])"
            [percentStep]="50"
        ></app-progress-bar-input>
    </div>
</ng-template>

<ng-template #comparedWithRef>
    <input
        class="form-control w-full max-w-4xl"
        [formControl]="$any(formState.group.controls['comparedWith'])"
        [placeholder]="'translate_compared_with' | translate"
        type="text"
    />
</ng-template>
