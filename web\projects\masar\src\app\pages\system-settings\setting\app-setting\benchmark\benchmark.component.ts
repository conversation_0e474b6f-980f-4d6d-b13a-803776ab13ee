import { Component } from '@angular/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { SafeModeService } from '@masar/core/services';
import { TranslateService } from '@ngx-translate/core';
import { BenchmarkSetting } from '@masar/common/models/benchmark-Setting.model';
import { BenchmarkSettingService } from '../benchmark-setting.service';

@Component({
    selector: 'app-benchmark',
    templateUrl: './benchmark.component.html',
    styles: [],
})
export class BenchmarkComponent {
    public benchmarkSetting: BenchmarkSetting;
    public isSubmitting = false;

    public constructor(
        public safeModeService: SafeModeService,
        private benchmarkSettingService: BenchmarkSettingService,
        private translateService: TranslateService,
        private notificationService: NotificationService
    ) {
        benchmarkSettingService
            .get()
            .subscribe(item => (this.benchmarkSetting = item));
    }

    public save(): void {
        if (this.isSubmitting) return;

        this.isSubmitting = true;

        this.benchmarkSettingService
            .update(this.benchmarkSetting)
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(() => {
                this.notificationService.notifySuccess(
                    this.translateService.instant(
                        'translate_item_updated_successfully'
                    )
                );
            });
    }
}
