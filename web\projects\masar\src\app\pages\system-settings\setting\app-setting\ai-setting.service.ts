import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { AiSetting } from '@masar/common/models';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';
import { miscFunctions, Result } from 'mnm-webapp';
import { environment } from '@masar/env/environment';

@Injectable()
export class AiSettingService {
    public constructor(private httpClient: HttpClient) {}

    public get(): Observable<AiSetting> {
        return this.httpClient
            .get<Result<AiSetting>>(`${environment.apiUrl}/setting/ai`)
            .pipe(map(res => res.extra));
    }

    public update(aiSetting: AiSetting): Observable<AiSetting> {
        return this.httpClient
            .put<Result<AiSetting>>(
                `${environment.apiUrl}/setting/ai`,
                miscFunctions.objectToURLParams({
                    aiSetting: JSON.stringify(aiSetting),
                })
            )
            .pipe(map(res => res.extra));
    }
}
