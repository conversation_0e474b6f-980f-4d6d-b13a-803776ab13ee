<app-page
    pageTitle="{{
        (mode === 'new'
            ? 'translate_add_new_strategic_pillar'
            : 'translate_update_existing_strategic_pillar'
        ) | translate
    }}"
>
    <!-- Tools -->
    <ng-container tools>
        <!-- List -->
        <a
            [routerLink]="[
                '',
                'system-list',
                'strategic-plans',
                'strategic-pillar'
            ]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-share me-2"></i>
            <span>{{ 'translate_strategic_pillars_list' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <mnm-form
        content
        *ngIf="formState"
        [state]="formState"
        [translateLabels]="true"
    >
        <div class="mt-2 text-center">
            <button
                type="submit"
                class="btn-lg btn btn-primary"
                (click)="submit()"
                [disabled]="isSubmitting"
            >
                <app-loading-ring
                    *ngIf="isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
    </mnm-form>
</app-page>
