import { CommonModule, DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { MasarModule } from '@masar/features/masar/masar.module';
import { SharedModule } from '@masar/shared/shared.module';
import {
    PlanListComponent,
    PlanSubtaskListComponent,
    PlanTaskListComponent,
} from './components';
import { TranslationModule } from '@ng-omar/translation';
import { FlowModule } from '../flow/flow.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        TranslationModule,
        RouterModule,
        NgSelectModule,
        MasarModule,
        SharedModule,
        SweetAlert2Module,
        FlowModule,
    ],
    declarations: [
        PlanTaskListComponent,
        PlanSubtaskListComponent,
        PlanListComponent,
    ],
    exports: [
        PlanTaskListComponent,
        PlanSubtaskListComponent,
        PlanListComponent,
    ],
    providers: [DatePipe],
})
export class PlanSharedModule {}
