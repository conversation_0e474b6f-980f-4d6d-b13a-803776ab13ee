import { KpiResultDataEntryResponsePeriod } from './kpi-result-data-entry-response-period.model';
import { KpiResultDataEntryRequest } from './kpi-result-data-entry-request.model';
import { KpiResultDataEntryResponseTransfer } from './kpi-result-data-entry-response-transfer.model';
import { KpiResult } from './kpi-result.model';

export interface KpiResultDataEntryResponse {
    id: string;
    result: KpiResult;
    request: KpiResultDataEntryRequest;
    transfers: KpiResultDataEntryResponseTransfer[];
    periods: KpiResultDataEntryResponsePeriod[];
    currentTransfer: KpiResultDataEntryResponseTransfer;
    startTime: Date;
    endTime: Date;
    canApprove: boolean;
}
