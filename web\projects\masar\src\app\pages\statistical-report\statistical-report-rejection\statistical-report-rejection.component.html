<app-content contentTitle="{{ 'translate_reject' | translate }}">
    <ng-container content>
        <!-- Form -->
        <mnm-form
            [state]="formState"
            [translateLabels]="true"
            [validationMessages]="validationMessages"
        ></mnm-form>

        <!-- Form-level validation message -->
        <div
            *ngIf="
                formState.group.errors?.atLeastOne && formState.triedToSubmit
            "
            class="mb-4 text-center text-red-500"
        >
            {{ 'translate_at_least_one_checkbox_required' | translate }}
        </div>

        <!-- Buttons -->
        <div class="mt-6 flex justify-center">
            <button
                class="btn btn-danger"
                (click)="rejectResult()"
                [disabled]="isSubmitting"
            >
                <app-loading-ring
                    *ngIf="isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i *ngIf="!isSubmitting" class="fa fa-times fa-fw me-2"></i>
                <span>{{ 'translate_reject' | translate }}</span>
            </button>
        </div>
    </ng-container>
</app-content>
