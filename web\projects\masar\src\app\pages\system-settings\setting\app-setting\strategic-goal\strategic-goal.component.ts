import { Component } from '@angular/core';
import { StrategicGoalSetting } from '@masar/common/models/strategic-goal-setting';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { StrategicGoalService } from '../strategic-goal.service';

@Component({
    selector: 'app-strategic-goal',
    templateUrl: './strategic-goal.component.html',
})
export class StrategicGoalComponent {
    public strategicGoalSetting: StrategicGoalSetting = {
        isOverallPerformanceCalculationEnabled: false,
    };
    public isSubmitting = false;

    public constructor(
        private strategicGoalService: StrategicGoalService,
        private translateService: TranslateService,
        private notificationService: NotificationService
    ) {
        strategicGoalService.get().subscribe(item => {
            console.log(item);
            this.strategicGoalSetting = item;
        });
    }

    public save(): void {
        if (this.isSubmitting) return;

        this.isSubmitting = true;

        this.strategicGoalService
            .update(this.strategicGoalSetting)
            .subscribe(() => {
                this.translateService
                    .get('translate_item_updated_successfully')
                    .pipe(finalize(() => (this.isSubmitting = false)))
                    .subscribe(str => {
                        this.notificationService.notifySuccess(str);
                    });
            });
    }
}
