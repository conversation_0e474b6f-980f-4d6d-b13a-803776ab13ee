import { Injectable } from '@angular/core';
import { GanttItem, GanttDataMapper } from '../interfaces/gantt.interfaces';
import { GanttDateService, DateRange } from './gantt-date.service';

export interface DataProcessingResult<T> {
    processedData: GanttItem<T>[];
    dateRange: DateRange | null;
}

@Injectable({
    providedIn: 'root',
})
export class GanttDataProcessorService {
    private hasChildrenCache = new Map<string, boolean>();

    public constructor(private dateService: GanttDateService) {}

    /**
     * Processes raw data using the provided mapper
     */
    public processRawData<T>(
        rawData: T[],
        dataMapper: GanttDataMapper<T>,
        adjustDatesBy?: number
    ): DataProcessingResult<T> {
        if (!rawData?.length || !dataMapper) {
            return { processedData: [], dateRange: null };
        }

        const processedData = this.mapRawDataToGanttItems(rawData, dataMapper);

        if (adjustDatesBy && adjustDatesBy !== 0) {
            this.adjustItemDates(processedData, adjustDatesBy);
        }

        const dateRange = this.calculateDateRange(processedData);

        return { processedData, dateRange };
    }

    /**
     * Processes pre-structured Gantt data
     */
    public processGanttData<T>(
        data: GanttItem<T>[],
        adjustDatesBy?: number
    ): DataProcessingResult<T> {
        if (!data?.length) {
            return { processedData: [], dateRange: null };
        }

        const processedData = [...data];

        if (adjustDatesBy && adjustDatesBy !== 0) {
            this.adjustItemDates(processedData, adjustDatesBy);
        }

        const dateRange = this.calculateDateRange(processedData);

        return { processedData, dateRange };
    }

    /**
     * Checks if an item has children (cached)
     */
    public hasChildren<T>(item: GanttItem<T>): boolean {
        if (this.hasChildrenCache.has(item.id)) {
            return this.hasChildrenCache.get(item.id)!;
        }

        const hasResult = !!item.children?.length;
        this.hasChildrenCache.set(item.id, hasResult);
        return hasResult;
    }

    /**
     * Clears all caches
     */
    public clearCaches(): void {
        this.hasChildrenCache.clear();
    }

    /**
     * Calculates date range from processed data
     */
    public calculateDateRange<T>(items: GanttItem<T>[]): DateRange | null {
        let minDate: Date | null = null;
        let maxDate: Date | null = null;

        const processItems = (itemList: GanttItem<T>[]): void => {
            itemList.forEach(item => {
                if (item.from) {
                    const fromDate = this.dateService.parseUtcDate(item.from);
                    if (fromDate && (!minDate || fromDate < minDate)) {
                        minDate = fromDate;
                    }
                }
                if (item.to) {
                    const toDate = this.dateService.parseUtcDate(item.to);
                    if (toDate && (!maxDate || toDate > maxDate)) {
                        maxDate = toDate;
                    }
                }

                if (item.children?.length) {
                    processItems(item.children);
                }
            });
        };

        processItems(items);

        if (!minDate || !maxDate) {
            return null;
        }

        return this.dateService.normalizeToMonthBoundaries(minDate, maxDate);
    }

    private mapRawDataToGanttItems<T>(
        rawItems: T[],
        dataMapper: GanttDataMapper<T>
    ): GanttItem<T>[] {
        return rawItems.map(item => {
            const ganttItem: GanttItem<T> = {
                id: dataMapper.getId(item),
                name: dataMapper.getName(item),
                from: dataMapper.getFromDate(item),
                to: dataMapper.getToDate(item),
                progress: dataMapper.getProgress?.(item) ?? 0,
                data: item,
            };

            // Map children if mapper provides them
            if (dataMapper.getChildren) {
                const children = dataMapper.getChildren(item);
                if (children?.length) {
                    ganttItem.children = this.mapRawDataToGanttItems(
                        children,
                        dataMapper
                    );
                }
            }

            return ganttItem;
        });
    }

    private adjustItemDates<T>(
        items: GanttItem<T>[],
        adjustDatesBy: number
    ): void {
        items.forEach(item => {
            if (item.from) {
                item.from = this.dateService.addDays(item.from, adjustDatesBy);
            }
            if (item.to) {
                item.to = this.dateService.addDays(item.to, adjustDatesBy);
            }

            if (item.children?.length) {
                this.adjustItemDates(item.children, adjustDatesBy);
            }
        });
    }
}
