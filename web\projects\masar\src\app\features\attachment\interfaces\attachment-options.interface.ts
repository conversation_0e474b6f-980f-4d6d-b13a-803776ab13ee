import { PermissionValue } from '@masar/common/types';
import { HttpQueryParameters } from '@masar/features/http-crud/http-query-parameters.interface';

export interface AttachmentOptions {
    endPoint: string;
    id: string;
    httpQueryParams?: HttpQueryParameters;
    createPermission?: PermissionValue;
    editPermission?: PermissionValue;
    deletePermission?: PermissionValue;
    canCreate?: boolean;
    canEdit?: boolean;
    canDelete?: boolean;
}
