import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {
    StrategicGoal,
    StrategicPillar,
} from '../interfaces/strategic-plan.interface';
import { YearService } from '../../../core/services/year.service';
import { StrategicPlanService } from '../services/strategic-plan.service';
import { StrategicSectionData } from '../components/strategic-section-dialog/strategic-section-dialog.component';
import { StrategicPlan } from '@masar/common/models';

// Enhanced interfaces for pre-computed data
export interface EnhancedStrategicGoal extends StrategicGoal {
    displayName: string;
}

export interface EnhancedStrategicPillar extends StrategicPillar {
    displayName: string;
    iconClass: string;
    goals: EnhancedStrategicGoal[];
}

@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
})
export class DashboardComponent implements OnInit, OnDestroy {
    public strategicPlan?: StrategicPlan;
    public pillars: EnhancedStrategicPillar[] = [];
    public standaloneGoals: EnhancedStrategicGoal[] = [];

    // Pre-computed section data
    public visionData: StrategicSectionData | null = null;
    public missionData: StrategicSectionData | null = null;

    private destroy$ = new Subject<void>();

    public constructor(
        private strategicPlanService: StrategicPlanService,
        private yearService: YearService
    ) {}

    public ngOnInit(): void {
        this.yearService.changes$
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                this.loadStrategicPlan();
            });
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    // TrackBy functions for performance optimization
    public trackByPillarId(
        _index: number,
        pillar: EnhancedStrategicPillar
    ): string {
        return pillar.id;
    }

    public trackByGoalId(_index: number, goal: EnhancedStrategicGoal): string {
        return goal.id;
    }

    public trackByValue(_index: number, value: string): string {
        return value;
    }

    // Helper method to check if there are standalone goals to display
    public hasStandaloneGoals(): boolean {
        return this.standaloneGoals && this.standaloneGoals.length > 0;
    }

    // Method to enhance pillars with pre-computed data
    private enhancePillars(
        pillars: StrategicPillar[]
    ): EnhancedStrategicPillar[] {
        return pillars.map(pillar => ({
            ...pillar,
            displayName: this.computePillarDisplayName(pillar),
            iconClass: this.computePillarIcon(pillar),
            goals: pillar.goals.map(goal => ({
                ...goal,
                displayName: this.computeGoalDisplayName(goal),
            })),
        }));
    }

    // Method to enhance standalone goals with pre-computed data
    private enhanceStandaloneGoals(
        goals: StrategicGoal[]
    ): EnhancedStrategicGoal[] {
        return goals.map(goal => ({
            ...goal,
            displayName: this.computeGoalDisplayName(goal),
        }));
    }

    // Method to pre-compute section data
    private computeSectionData(): void {
        if (this.strategicPlan) {
            this.visionData = {
                title: 'translate_vision',
                content: this.strategicPlan.vision,
                type: 'vision',
            };

            this.missionData = {
                title: 'translate_mission',
                content: this.strategicPlan.mission,
                type: 'mission',
            };
        } else {
            this.visionData = null;
            this.missionData = null;
        }
    }

    // Computation methods (private as they're only used internally)
    private computePillarDisplayName(pillar: StrategicPillar): string {
        // You can implement language switching logic here
        return pillar.nameEn || pillar.name; // or pillar.nameAr for Arabic
    }

    private computeGoalDisplayName(goal: StrategicGoal): string {
        // You can implement language switching logic here
        return `${goal.code} ${goal.name}`; // or goal.nameAr for Arabic
    }

    private computePillarIcon(pillar: StrategicPillar): string {
        // Use iconClass from API if available, fallback to default
        return pillar.iconClass || 'fas fa-bullseye';
    }

    private loadStrategicPlan(): void {
        const year = this.yearService.get();

        this.strategicPlanService.getDashboardData(year).subscribe({
            next: plan => {
                this.strategicPlan = plan;
                this.pillars = this.enhancePillars(
                    (plan.pillars || []).map(pillar => ({
                        ...pillar,
                        goals: pillar.goals ?? [],
                    }))
                ).reverse();
                this.standaloneGoals = this.enhanceStandaloneGoals(
                    plan.goals || []
                );
                this.computeSectionData();
            },
            error: error => {
                console.error('Error loading strategic plan:', error);
                // Reset data on error
                this.strategicPlan = undefined;
                this.pillars = [];
                this.standaloneGoals = [];
                this.visionData = null;
                this.missionData = null;
            },
        });
    }
}
