import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'generateColorByIndex',
})
export class GenerateColorByIndexPipe implements PipeTransform {
    public transform(index: number): string {
        const hexColor = getComputedStyle(document.documentElement)
            .getPropertyValue('--primary-500')
            .trim();

        // Return original color if steps is 0 or invalid
        if (!index || index === 0) {
            return hexColor;
        }

        // Remove # if present
        const hex = hexColor.replace('#', '');

        // Convert hex to RGB
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);

        // Calculate brightness factor (each step increases brightness by 10%)
        const stepFactor = 0.1;
        const brightenAmount = Math.min(index * stepFactor, 1);

        // Adjust brightness by moving towards 255 instead of 0
        const newR = Math.min(255, Math.floor(r + (255 - r) * brightenAmount));
        const newG = Math.min(255, Math.floor(g + (255 - g) * brightenAmount));
        const newB = Math.min(255, Math.floor(b + (255 - b) * brightenAmount));

        // Convert back to hex
        return (
            '#' +
            newR.toString(16).padStart(2, '0') +
            newG.toString(16).padStart(2, '0') +
            newB.toString(16).padStart(2, '0')
        );
    }
}
