import { Kpi } from './kpi.model';
import { Item } from './item.model';
import { Operation } from './operation.model';
import { KpiResult } from './kpi-result.model';
import { KpiResultEntryPerYear } from './kpi-result-entry-per-year.model';

export interface Department {
    id: string;
    name: string;
    nameAr: string;
    nameEn: string;
    parentDepartment: Department;
    type: Item;
    managerName: string;
    managerEmail: string;
    managerStaffNumber: number;
    order: number;
    level: number;
    levelName: string;
    children: Department[];
    parents: Department[];
    childCount: number;
    kpiCount: number;
    achieved: number;
    userCount: number;
    specializations: string;
    kpiResultYears: KpiResultEntryPerYear[];
    relatedDepartmentCount: number;
    ownedKpis: Kpi[];
    linkedKpis: Kpi[];
    ownedOperations: Operation[];
    kpiResults: KpiResult[];
    recursiveChildCount: number;
    recursiveKpiCount: number;
    recursivePlanCount: number;
    recursiveBenchmarkCount: number;
    recursiveOperationCount: number;
}
