import { StrategicGoal } from './strategic-goal.model';
import { StrategicPillar } from './strategic-pillar.model';
import { StrategicValue } from './strategic-value.model';

export interface StrategicPlan {
    id?: string;
    name: string;
    nameAr: string;
    nameEn: string;
    startYear: number;
    endYear: number;
    isActive: boolean;
    vision: string;
    mission: string;
    visionAr: string;
    visionEn: string;
    missionAr: string;
    missionEn: string;
    pillars: StrategicPillar[];
    goals: StrategicGoal[];
    values: StrategicValue[];
    period?: string;
}
