import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, combineLatest, Observable, Subject } from 'rxjs';
import { NavigationEnd, NavigationError, Router } from '@angular/router';
import { AppSettingFetcherService } from '@masar/core/services';
import { takeUntil } from 'rxjs/operators';

@Injectable()
export class DefaultLayoutService implements OnDestroy {
    private readonly shouldHideSidebar = new BehaviorSubject<boolean>(false);
    private readonly unsubscribeAll = new Subject();

    public constructor(
        private readonly appSettingFetcherService: AppSettingFetcherService,
        private readonly router: Router
    ) {
        this.initSidebarVisibilityMonitor();
    }

    public get isSidebarHidden$(): Observable<boolean> {
        return this.shouldHideSidebar.asObservable();
    }

    public get isSidebarHidden(): boolean {
        return this.shouldHideSidebar.value;
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    private initSidebarVisibilityMonitor(): void {
        combineLatest([this.appSettingFetcherService.get$, this.router.events])
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(([settings, evt]) => {
                if (
                    !(evt instanceof NavigationEnd) &&
                    !(evt instanceof NavigationError)
                )
                    return;

                this.shouldHideSidebar.next(
                    settings.dashboardSetting.selectedDashboard === 'style_1' &&
                        (evt.url === '/dashboard' || evt.url === '/')
                );
            });
    }
}
