import { Pipe, PipeTransform } from '@angular/core';
import { StatisticsGroup } from '@masar/features/dynamic-page/components/statistics-section/interfaces';

@Pipe({ name: 'statisticalGroupHasValue' })
export class StatisticalGroupHasValuePipe implements PipeTransform {
    public transform(value?: StatisticsGroup[]): StatisticsGroup[] {
        return value?.filter(group => group.items.length > 0) ?? [];
    }
}
