import { NgModule } from '@angular/core';
import { MnmWebappModule } from 'mnm-webapp';
import { CommonModule } from '@angular/common';
import { KpiBenchmarkNewComponent } from './components/kpi-benchmark-new/kpi-benchmark-new.component';
import { KpiBenchmarkListComponent } from './components/kpi-benchmark-list/kpi-benchmark-list.component';
import { KpiBenchmarkService } from './kpi-benchmark.service';
import { SharedModule } from '@masar/shared/shared.module';
import { MasarModule } from '@masar/features/masar/masar.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { KpiListComponent } from './components/kpi-list/kpi-list.component';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { KpiListFullComponent } from './components/kpi-list-full/kpi-list-full.component';
import { KpiLinkerComponent } from './components/kpi-linker/kpi-linker.component';
import { KpiListWithResultComponent } from './components/kpi-list-with-result/kpi-list-with-result.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { TranslationModule } from '@ng-omar/translation';
import { KpiEvaluationListComponent } from './components/kpi-evaluation-list/kpi-evaluation-list.component';

@NgModule({
    imports: [
        CommonModule,
        TranslationModule,
        MnmWebappModule,
        SharedModule,
        MasarModule,
        FormsModule,
        ReactiveFormsModule,
        SweetAlert2Module,
        RouterModule,
        NgSelectModule,
        DragDropModule,
    ],
    declarations: [
        KpiBenchmarkNewComponent,
        KpiBenchmarkListComponent,
        KpiListComponent,
        KpiListFullComponent,
        KpiLinkerComponent,
        KpiListWithResultComponent,
        KpiEvaluationListComponent,
    ],
    exports: [
        KpiBenchmarkNewComponent,
        KpiBenchmarkListComponent,
        KpiListComponent,
        KpiListFullComponent,
        KpiLinkerComponent,
        KpiListWithResultComponent,
        KpiEvaluationListComponent,
    ],
    providers: [KpiBenchmarkService],
})
export class KpiSharedModule {}
