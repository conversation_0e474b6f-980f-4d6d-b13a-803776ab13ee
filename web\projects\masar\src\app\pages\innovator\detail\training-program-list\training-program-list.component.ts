import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { TrainingProgram } from '@masar/common/models';
import { TrainingProgramService } from '../../training-program.service';

@Component({
    selector: 'app-training-program-list',
    templateUrl: './training-program-list.component.html',
})
export class TrainingProgramListComponent {
    @Input()
    public trainingPrograms: TrainingProgram[];

    @Output()
    public editTrainingProgram: EventEmitter<TrainingProgram> =
        new EventEmitter<TrainingProgram>();

    @Output()
    public deleteTrainingProgram: EventEmitter<TrainingProgram> =
        new EventEmitter<TrainingProgram>();

    public currentlyDeleting: string[] = [];

    public constructor(
        private trainingProgramService: TrainingProgramService,
        private notificationService: NotificationService
    ) {}

    public edit(item: TrainingProgram): void {
        this.editTrainingProgram.emit(item);
    }

    public delete(item: TrainingProgram): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id);
        this.trainingProgramService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.deleteTrainingProgram.emit(item);
            });
    }
}
