import { Pipe, PipeTransform } from '@angular/core';
import { TableListData } from '@masar/features/dynamic-page/interfaces';

@Pipe({ name: 'getTableListTabs' })
export class GetTableListTabsPipe implements PipeTransform {
    public transform<T>(
        data: TableListData<T>,
        item: T
    ): TableListData<T>['tabs'] {
        if (data.tabs) return data.tabs;

        if (data.tabsFactory) return data.tabsFactory(item);

        return [];
    }
}
