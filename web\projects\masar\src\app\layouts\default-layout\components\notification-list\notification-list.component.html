<div class="relative" #containerElement>
    <button
        class="relative rounded-full px-3 py-2 text-gray-600 no-underline transition-all hover:bg-gray-200 hover:text-secondary-600"
        [appTooltip]="'translate_my_notifications' | translate"
        (click)="showNotifications()"
    >
        <p
            *ngIf="notificationBadgeCount > 0"
            dir="ltr"
            class="absolute start-0 top-[-10px] flex h-[25px] w-[25px] items-center justify-center rounded-full border-2 border-white bg-red-500 text-[12px] text-white shadow"
        >
            {{ notificationBadgeCount > 99 ? '99+' : notificationBadgeCount }}
        </p>
        <em class="fa-light fa-bell fa-lg"></em>
    </button>

    <div
        class="absolute left-1/2 top-12 z-50 -translate-x-1/2"
        *ngIf="showNotificationList"
        [appClickOutside]="containerElement"
        (clickOutside)="showNotificationList = false"
    >
        <div
            class="relative flex h-[400px] w-72 flex-col rounded-md bg-white shadow-xl"
        >
            <!-- Notification list top chevron -->
            <div
                class="absolute -top-[7px] left-1/2 -z-10 h-4 w-4 -translate-x-1/2 rotate-45 rounded bg-primary shadow"
            ></div>

            <!-- Title and read all button -->
            <div
                class="flex items-center justify-between rounded-t-md bg-primary p-2 text-white"
            >
                <p class="text-lg font-bold">
                    {{ 'translate_notifications' | translate }}
                </p>
                <button
                    class="text-xs font-semibold underline"
                    (click)="markAllAsRead()"
                >
                    {{ 'translate_read_all' | translate }}
                </button>
            </div>

            <!-- Notification list -->
            <div class="min-h-0 flex-grow">
                <!-- Loading ring -->
                <div
                    class="flex h-full w-full items-center justify-center"
                    *ngIf="
                        isLoading &&
                            (!notifications || notifications.length === 0);
                        else itemListTemplate
                    "
                >
                    <app-loading-ring></app-loading-ring>
                </div>

                <!-- List -->
                <ng-template #itemListTemplate>
                    <ul
                        [(appScrollPosition)]="listScrollPosition"
                        class="h-full overflow-auto"
                    >
                        <li
                            *ngFor="let notification of notifications"
                            class="border-b border-b-gray-200 p-1 hover:bg-gray-200"
                        >
                            <button
                                (click)="navigateTo(notification)"
                                class="flex w-full flex-col gap-0.5 border-s-4 p-2 text-start"
                                [ngClass]="
                                    notification.isRead
                                        ? 'border-transparent'
                                        : 'border-primary'
                                "
                            >
                                <p
                                    class="text-sm"
                                    [ngClass]="{
                                        'text-slate-500': notification.isRead,
                                        'font-semibold text-slate-800':
                                            !notification.isRead
                                    }"
                                >
                                    {{ notification.title }}
                                </p>
                                <p
                                    class="text-xs"
                                    [ngClass]="{
                                        'text-gray-400': notification.isRead,
                                        'text-gray-600': !notification.isRead
                                    }"
                                >
                                    {{ notification.description }}
                                </p>
                                <p
                                    class="w-full text-end text-xs text-gray-400"
                                >
                                    {{
                                        notification.creationTime
                                            | toDate
                                            | dfnsFormatDistance
                                                : now
                                                : { addSuffix: true }
                                    }}
                                </p>
                            </button>
                        </li>
                    </ul>
                </ng-template>
            </div>

            <!-- Show all button -->
            <a
                [routerLink]="['', 'my-notification']"
                class="rounded-b-md bg-gray-100 p-1.5 text-center text-xs font-semibold text-slate-900"
                (click)="showNotificationList = false"
            >
                {{ 'translate_show_all' | translate }}
            </a>
        </div>
    </div>
</div>
