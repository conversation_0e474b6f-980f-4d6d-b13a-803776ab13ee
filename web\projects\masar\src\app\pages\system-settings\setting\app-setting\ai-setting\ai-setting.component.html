<app-fieldset legend="{{ 'translate_general' | translate }}">
    <div
        *appWaitUntilLoaded="aiSetting"
        class="mb-5 grid grid-cols-3 items-center gap-5"
    >
        <!-- Is enabled -->
        <div class="flex flex-col items-stretch gap-2">
            <label>
                <input
                    type="checkbox"
                    [(ngModel)]="aiSetting.isEnabled"
                    class="me-2"
                />
                <span>{{ 'translate_is_enabled_qm' | translate }}</span>
            </label>
        </div>
    </div>
</app-fieldset>

<div class="text-center">
    <button
        type="submit"
        class="btn btn-primary"
        (click)="save()"
        [disabled]="isSubmitting"
    >
        <app-loading-ring *ngIf="isSubmitting" class="me-2"></app-loading-ring>
        <i class="fa-light fa-save me-2"></i>
        <span>{{ 'translate_save' | translate }}</span>
    </button>
</div>
