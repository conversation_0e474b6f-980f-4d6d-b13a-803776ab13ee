<div class="table-responsive">
    <table>
        <thead>
            <tr>
                <th *ngIf="shownFields.includes('name')">
                    {{ 'translate_name' | translate }}
                </th>
                <th *ngIf="shownFields.includes('type')">
                    {{ 'translate_type' | translate }}
                </th>
                <th *ngIf="shownFields.includes('year')">
                    {{ 'translate_year' | translate }}
                </th>
                <th *ngIf="shownFields.includes('kpi_count')">
                    {{ 'translate_kpis_count' | translate }}
                </th>
                <th *ngIf="shownFields.includes('file_count')">
                    {{ 'translate_files_count' | translate }}
                </th>
                <th *ngFor="let field of customFields" style="width: 0%">
                    {{ field.name | translate }}
                </th>
            </tr>
        </thead>

        <tbody
            cdkDropList
            [cdkDropListDisabled]="!enableDrag"
            (cdkDropListDropped)="order.emit($event)"
        >
            <tr *ngFor="let item of items; let idx = index" cdkDrag>
                <!-- Name -->
                <td>
                    <a
                        *appHasPermissionId="
                            permissionList.capability;
                            else justNameTemplateRef
                        "
                        [routerLink]="['', 'capability', 'detail', item.id]"
                    >
                        {{ item.name }}
                    </a>
                    <ng-template #justNameTemplateRef>
                        {{ item.name }}
                    </ng-template>
                    <p
                        *ngIf="item.description"
                        class="line-clamp-3 text-gray-500"
                        [title]="item.description"
                    >
                        {{ item.description }}
                    </p>
                </td>

                <!-- Type -->
                <td *ngIf="shownFields.includes('name')">
                    {{ item.type.name }}
                </td>

                <!-- Year -->
                <td *ngIf="shownFields.includes('year')">
                    {{ item.year }}
                </td>

                <!-- Kpis count -->
                <td
                    *ngIf="shownFields.includes('kpi_count')"
                    class="text-center"
                >
                    <a (click)="showKpiListModal(item)" class="cursor-pointer">
                        {{ item.kpiCount }}
                    </a>
                </td>

                <!-- Files count -->
                <td
                    *ngIf="shownFields.includes('file_count')"
                    class="text-center"
                >
                    <a
                        (click)="showLibraryFileListModal(item)"
                        class="cursor-pointer"
                    >
                        {{ item.libraryFileCount }}
                    </a>
                </td>

                <!-- Custom fields -->
                <td *ngFor="let field of customFields">
                    <app-dropdown>
                        <ng-container
                            [ngTemplateOutlet]="field.template"
                            [ngTemplateOutletContext]="{item}"
                        ></ng-container>
                    </app-dropdown>
                </td>
            </tr>
        </tbody>
    </table>
</div>
