import { ResourceOptionalField } from './resource-optional-field.model';
import { ResourceOptionalSection } from './resource-optional-section.model';

export interface PlanSetting {
    maxApprovingDepartmentLevel: number;
    minIntermediateInitialApprovalCount: number;
    doesRejectionResetApprovalCycle: boolean;
    optionalFields: ResourceOptionalField[];
    optionalSections: ResourceOptionalSection[];
    shouldRuleCheckBeforeSubmission: boolean;
}
