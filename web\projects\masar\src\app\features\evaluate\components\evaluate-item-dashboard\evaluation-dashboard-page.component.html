<app-page pageTitle="{{ title | translate }}">
    <!-- Tools Section with Dropdowns -->
    <ng-container tools *ngIf="showDepartmentStatistics">
        <div class="flex items-center gap-4">
            <!-- Evaluation Type Dropdown -->
            <div class="flex flex-col gap-1">
                <ng-select
                    [items]="evaluationTypes || []"
                    [ngModel]="selectedEvaluationType"
                    [clearable]="false"
                    [searchable]="false"
                    (ngModelChange)="onEvaluationTypeChange($event)"
                    bindLabel="name"
                    placeholder="{{
                        'translate_select_evaluation_type' | translate
                    }}"
                    class="min-w-[200px]"
                ></ng-select>
            </div>

            <!-- Measurement Cycle Dropdown -->
            <div class="flex flex-col gap-1">
                <ng-select
                    [items]="measurementCycles || []"
                    [ngModel]="selectedMeasurementCycle"
                    [clearable]="false"
                    [searchable]="false"
                    (ngModelChange)="onMeasurementCycleChange($event)"
                    bindLabel="name"
                    bindValue="id"
                    placeholder="{{
                        'translate_select_measurement_cycle' | translate
                    }}"
                    class="min-w-[100px]"
                ></ng-select>
            </div>
        </div>
    </ng-container>

    <div content class="flex flex-col gap-5">
        <!-- Existing evaluation statistics (shown when not in department mode) -->
        <app-evaluation-statistics
            *ngIf="!showDepartmentStatistics"
            #statistics
            (evaluationChange)="evaluationChange.emit($event); reload()"
            type="{{ type }}"
        ></app-evaluation-statistics>

        <!-- Department Statistics Section -->
        <div *ngIf="showDepartmentStatistics" class="flex flex-col gap-5">
            <!-- Department Statistics Grid -->
            <app-content
                *ngIf="selectedEvaluationType && selectedMeasurementCycle"
                contentTitle="{{
                    'translate_departments_statistics' | translate
                }}"
            >
                <div content>
                    <!-- Back Navigation Button -->
                    <div
                        *ngIf="currentParentDepartment"
                        class="mb-4 flex justify-end"
                    >
                        <button
                            dir="ltr"
                            (click)="goToPreviousDepartmentStatistics()"
                            class="btn btn-sm btn-info flex items-center gap-1"
                        >
                            <i class="fas fa-arrow-left"></i>
                            <span class="hidden md:inline">
                                {{ 'translate_back' | translate }}
                            </span>
                        </button>
                    </div>

                    <!-- Loading State -->
                    <div
                        *ngIf="isLoadingStatistics"
                        class="flex items-center justify-center p-8"
                    >
                        <app-loading-ring
                            [spinnerBorderWidth]="4"
                            [spinnerDim]="100"
                        ></app-loading-ring>
                    </div>

                    <!-- Department Cards Grid -->
                    <div
                        *ngIf="
                            !isLoadingStatistics && departmentStatistics?.length
                        "
                        class="grid grid-cols-1 justify-items-center gap-6 transition-all sm:grid-cols-2 md:grid-cols-3"
                    >
                        <ng-container
                            *ngFor="let item of departmentStatistics"
                            [ngTemplateOutlet]="departmentTemplateRef"
                            [ngTemplateOutletContext]="{ item }"
                        ></ng-container>
                    </div>

                    <!-- No Data State -->
                    <div
                        *ngIf="
                            !isLoadingStatistics &&
                            !departmentStatistics?.length
                        "
                        class="flex items-center justify-center p-8 text-gray-500"
                    >
                        {{ 'translate_no_data_available' | translate }}
                    </div>
                </div>
            </app-content>
        </div>

        <!-- Existing content for evaluations list -->
        <app-content
            *ngIf="hasListContent && !showDepartmentStatistics"
            contentTitle="{{ 'translate_evaluations' | translate }}"
        >
            <ng-container content>
                <ng-content select="[list]"></ng-content>
            </ng-container>
        </app-content>
    </div>
</app-page>

<!-- Department Template -->
<ng-template #departmentTemplateRef let-item="item">
    <div
        class="w-full transform overflow-hidden rounded-lg border border-gray-300 bg-white p-4 text-primary shadow"
    >
        <p
            class="mb-4 overflow-hidden text-ellipsis whitespace-nowrap text-center text-lg font-semibold"
            [title]="item.department.name"
        >
            {{ item.department.name }}
        </p>

        <div class="mb-4 flex flex-wrap justify-center gap-4">
            <!-- Total Evaluations -->
            <a
                [routerLink]="['', 'kpi-evaluation']"
                [queryParams]="{
                    filter:
                        {
                            data: {
                                departmentIds: [item.department.id],
                                cycles: selectedMeasurementCycle
                                    ? [selectedMeasurementCycle]
                                    : []
                            }
                        } | json
                }"
                class="flex min-w-[150px] cursor-pointer flex-col items-center rounded bg-primary px-4 py-2 text-white no-underline shadow-md hover:no-underline"
            >
                <p>
                    {{ 'translate_total_evaluations' | translate }}
                </p>
                <p class="text-2xl font-bold">
                    {{ item.totalEvaluation }}
                </p>
            </a>

            <!-- Average Score -->
            <a
                [routerLink]="['', 'kpi-evaluation']"
                [queryParams]="{
                    filter:
                        {
                            data: {
                                departmentIds: [item.department.id],
                                cycles: selectedMeasurementCycle
                                    ? [selectedMeasurementCycle]
                                    : []
                            }
                        } | json
                }"
                class="flex min-w-[150px] cursor-pointer flex-col items-center rounded bg-secondary px-4 py-2 text-white no-underline shadow-md hover:no-underline"
            >
                <p>
                    {{ 'translate_average_score' | translate }}
                </p>
                <p class="text-2xl font-bold">
                    {{
                        item.averageScore !== null
                            ? (item.averageScore * 100 | round : 0) + '%'
                            : 'N/A'
                    }}
                </p>
            </a>
        </div>

        <!-- Navigation to Child Departments -->
        <div
            *ngIf="item.childrenCount > 0"
            class="flex items-center justify-center text-sm text-primary-400 hover:text-primary"
        >
            <button
                class="underline"
                (click)="goToNextDepartmentStatistics(item.department)"
            >
                <span class="me-0.5">
                    {{ 'translate_show_departments_details' | translate }}
                </span>
                <strong> ({{ item.childrenCount }}) </strong>
            </button>
        </div>
    </div>
</ng-template>

<ng-template #controlsTemplate let-item="item">
    <div class="flex flex-row items-center gap-2">
        <!-- Show evaluations -->
        <button
            (click)="onClickEvaluate(item)"
            *ngIf="item.evaluateActionAbility.canRead"
            class="btn btn-sm btn-warning"
        >
            {{ 'translate_evaluations_list' | translate }}
        </button>
    </div>
</ng-template>
