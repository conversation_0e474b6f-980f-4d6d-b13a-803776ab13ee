import { Pipe, PipeTransform } from '@angular/core';
import { Kpi } from '@masar/common/models';

@Pipe({
    name: 'extractKpiResult',
})
export class ExtractKpiResultPipe implements PipeTransform {
    public transform(value: Kpi): {
        year: number;
        target?: number;
        result?: number;
    }[] {
        return new Array(value.resultsToYear - value.resultsFromYear)
            .fill(0)
            .map((_, i) => {
                const year = value.resultsFromYear + i;
                const result = value.results.find(x => x.year === year);
                return {
                    year,
                    target: result?.target,
                    result: result?.result,
                };
            });
    }
}
