import { Injectable } from '@angular/core';
import { Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient, HttpParams } from '@angular/common/http';
import { TableResult } from '@masar/common/misc/table';
import { environment } from '@masar/env/environment';
import { Operation } from '@masar/common/models';

@Injectable()
export class OperationSharedService {
    public constructor(private httpClient: HttpClient) {}

    public list(
        keyword: string,
        code: string,
        level: string | null,
        departmentIds: string[],
        includeChildDepartments: boolean,
        version: string,
        strategicGoalIds: string[],
        pageNumber: number,
        pageSize: number = 20
    ): Observable<TableResult<Operation>> {
        let params = new HttpParams();
        params = params.append('keyword', keyword);
        params = params.append('code', code);
        params = params.append('level', level || '');
        params = params.append('version', version);
        params = params.append('pageNumber', `${pageNumber}`);
        params = params.append('pageSize', `${pageSize}`);

        departmentIds?.forEach(
            item => (params = params.append('departmentIds', item))
        );

        strategicGoalIds?.forEach(
            item => (params = params.append('strategicGoalIds', item))
        );

        params = params.append(
            'includeChildDepartments',
            includeChildDepartments
        );

        return this.httpClient
            .get<Result<TableResult<Operation>>>(
                environment.apiUrl + '/operation',
                {
                    params,
                }
            )
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(environment.apiUrl + '/operation/' + id)
            .pipe(map(result => result.messages[0]));
    }
}
