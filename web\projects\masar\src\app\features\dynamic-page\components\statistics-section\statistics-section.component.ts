import { Component, Input } from '@angular/core';
import {
    StatisticsGroup,
    StatisticsGroupCallbackMap,
    StatisticsGroupItem,
    StatisticsGroupLinkMap,
} from './interfaces';

@Component({
    selector: 'app-statistics-section',
    templateUrl: './statistics-section.component.html',
})
export class StatisticsSectionComponent {
    @Input() public groups?: StatisticsGroup[];

    @Input() public statisticsGroupLinkMap?: StatisticsGroupLinkMap;

    @Input() public statisticsGroupCallbackMap?: StatisticsGroupCallbackMap;

    public getUrlAndQueryParams(
        item: StatisticsGroupItem
    ): { url: string[]; queryParams?: Record<string, string> } | null {
        if (
            this.statisticsGroupLinkMap &&
            this.statisticsGroupLinkMap[item.type]
        ) {
            return this.statisticsGroupLinkMap[item.type](item);
        }
        return null;
    }

    public hasCallback(item: StatisticsGroupItem): boolean {
        return !!(
            this.statisticsGroupCallbackMap &&
            this.statisticsGroupCallbackMap[item.type]
        );
    }

    public hasLink(item: StatisticsGroupItem): boolean {
        return !!this.getUrlAndQueryParams(item);
    }

    public executeCallback(item: StatisticsGroupItem): void {
        if (this.hasCallback(item)) {
            this.statisticsGroupCallbackMap[item.type](item);
        }
    }
}
