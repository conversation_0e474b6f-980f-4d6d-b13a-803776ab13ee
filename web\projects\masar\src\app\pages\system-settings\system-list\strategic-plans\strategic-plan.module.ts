import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { StrategicPlanRoutingModule } from './strategic-plan-routing.module';
import { StrategicPlanComponent } from './strategic-plan.component';
import { ListComponent } from './strategic-plan/list/list.component';
import { DetailComponent } from './strategic-plan/detail/detail.component';
import { DetailsSectionComponent } from './strategic-plan/components/details-section/details-section.component';
import { StrategicGoalsSectionComponent } from './strategic-plan/components/strategic-goals-section/strategic-goals-section.component';
import { StrategicValuesSectionComponent } from './strategic-plan/components/strategic-values-section/strategic-values-section.component';
import { StrategicSectionDialogComponent } from './strategic-plan/components/strategic-section-dialog/strategic-section-dialog.component';
import { SharedModule } from '@masar/shared/shared.module';
import { TranslationModule } from '@ng-omar/translation';
import { MasarModule } from '@masar/features/masar/masar.module';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { DynamicPageModule } from '@masar/features/dynamic-page/dynamic-page.module';
import { StrategicPillarDetailComponent } from './strategic-pillars/strategic-pillar-detail/strategic-pillar-detail.component';
import { StrategicPillarListComponent } from './strategic-pillars/strategic-pillar-list/strategic-pillar-list.component';
import { StrategicPillarNewComponent } from './strategic-pillars/strategic-pillar-new/strategic-pillar-new.component';
import { NewComponent } from './strategic-plan/new/new.component';

@NgModule({
    declarations: [
        StrategicPlanComponent,
        ListComponent,
        NewComponent,
        DetailComponent,
        DetailsSectionComponent,
        StrategicGoalsSectionComponent,
        StrategicValuesSectionComponent,
        StrategicSectionDialogComponent,
        StrategicPillarDetailComponent,
        StrategicPillarListComponent,
        StrategicPillarNewComponent,
    ],
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        RouterModule,
        StrategicPlanRoutingModule,
        SharedModule,
        TranslationModule,
        MasarModule,
        SweetAlert2Module,
        DynamicPageModule,
    ],
})
export class StrategicPlanModule {}
