import { Injectable, NgZone } from '@angular/core';
import { OauthService } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { first, switchMap } from 'rxjs/operators';

@Injectable()
export class EventSourceService {
    public constructor(
        private readonly oauthService: OauthService,
        private readonly ngZone: NgZone
    ) {}

    public stream<T>(url: string): Observable<T> {
        return this.oauthService.auth$.pipe(
            first(),
            switchMap(accessToken => {
                return new Observable<T>(observer => {
                    const separator = url.includes('?') ? '&' : '?';
                    const eventSource = new EventSource(
                        `${url}${separator}access_token=${accessToken.value}`
                    );

                    eventSource.onmessage = event => {
                        this.ngZone.run(() => {
                            const data = JSON.parse(event.data) as T;
                            observer.next(data);
                        });
                    };

                    eventSource.onerror = () => {
                        this.ngZone.run(() => {
                            observer.complete();
                        });
                    };

                    return () => {
                        eventSource.close();
                    };
                });
            })
        );
    }
}
