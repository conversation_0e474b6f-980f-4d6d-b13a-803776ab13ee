import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    ElementRef,
    <PERSON><PERSON><PERSON><PERSON>,
    OnInit,
    ViewChild,
} from '@angular/core';
import { MnmFormState } from '@masar/shared/components';
import { ActivatedRoute } from '@angular/router';
import { NotificationService } from 'mnm-webapp';
import { FormBuilder } from '@angular/forms';
import { fields } from './fields';
import { finalize, first } from 'rxjs/operators';
import { StrategicPlanService } from '../services/strategic-plan.service';
import { StrategicPlan } from '@masar/common/models';
import { TranslateService } from '@ngx-translate/core';
import { HelperService } from '@masar/core/services';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-new',
    templateUrl: './new.component.html',
})
export class NewComponent implements OnInit, AfterViewInit, OnD<PERSON>roy {
    @ViewChild('strategicValuesFieldRef')
    private strategicValuesFieldRef: ElementRef;

    public isSubmitting = false;
    public mode: 'new' | 'edit';
    public formState: MnmFormState;

    private unsubscribeAll = new Subject();
    private planId: string;

    public constructor(
        private notificationService: NotificationService,
        private strategicPlanService: StrategicPlanService,
        private translateService: TranslateService,
        private activatedRoute: ActivatedRoute,
        private changeDetectorRef: ChangeDetectorRef,
        private readonly helperService: HelperService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[0].path) {
                case 'new':
                    this.mode = 'new';
                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            this.planId = params['id'];
                            this.strategicPlanService
                                .get(this.planId, true)
                                .subscribe(item => {
                                    this.fillForm(item);
                                });
                        });
                    break;
            }
        });
    }

    public ngAfterViewInit(): void {
        this.formState.get('values').customInputField =
            this.strategicValuesFieldRef;

        this.changeDetectorRef.detectChanges();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const value = this.formState.group.getRawValue();

        // Convert string values to numbers
        value.startYear = parseInt(value.startYear, 10);
        value.endYear = parseInt(value.endYear, 10);

        // Validate that end year is greater than start year
        if (value.endYear <= value.startYear) {
            this.notificationService.notifyError(
                this.translateService.instant(
                    'translate_end_year_must_be_greater_than_start_year'
                )
            );
            this.isSubmitting = false;
            return;
        }

        const observable =
            this.mode === 'new'
                ? this.strategicPlanService.create(value)
                : this.strategicPlanService.update(value);

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(strategicPlan => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'system-list', 'strategic-plans'],
                    strategicPlan.id
                );
            });
    }

    private fillForm(item: StrategicPlan): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }
}
