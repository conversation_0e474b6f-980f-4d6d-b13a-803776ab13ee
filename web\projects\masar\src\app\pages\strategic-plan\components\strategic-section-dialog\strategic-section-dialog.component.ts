import { Component } from '@angular/core';

export interface StrategicSectionData {
    title: string;
    content: string;
    type: 'vision' | 'mission';
}

@Component({
    selector: 'app-strategic-section-dialog',
    templateUrl: './strategic-section-dialog.component.html',
})
export class StrategicSectionDialogComponent {
    public selection?: StrategicSectionData;

    public set(data: StrategicSectionData): void {
        this.selection = data;
    }

    public clear(): void {
        this.selection = undefined;
    }
}
