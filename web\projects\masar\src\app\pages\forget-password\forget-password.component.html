<!-- The form -->
<form
    class="flex flex-col gap-6 self-stretch"
    novalidate
    autocomplete="off"
    (ngSubmit)="submit()"
>
    <!-- Email field -->
    <div class="flex flex-col gap-2">
        <label for="email">
            {{ 'translate_email_address' | translate }}
        </label>
        <input
            dir="ltr"
            type="email"
            id="email"
            name="email"
            [(ngModel)]="email"
            placeholder="<EMAIL>"
        />
    </div>

    <!-- Button -->
    <div class="mb-4 text-center">
        <button
            appEvader
            class="btn btn-primary rounded px-8"
            type="submit"
            [disabled]="isSubmitting"
        >
            <span>
                {{ 'translate_forget_password_confirmation' | translate }}
            </span>

            <app-loading-ring
                class="ms-3"
                *ngIf="isSubmitting"
            ></app-loading-ring>
        </button>
    </div>
</form>

<div class="mb-4 flex w-full items-center justify-center">
    <a
        class="cursor-pointer text-gray-400"
        [routerLink]="['', 'auth', 'login']"
    >
        {{ 'translate_return_to_login' | translate }}
    </a>
</div>
