import { Component, Input, OnChanges } from '@angular/core';
import { DataEntry } from '../../data-entry/data-entry.component';
import { ChartDataSets, ChartOptions, ChartType } from 'chart.js';
import { Service } from '../../services/services.component';
import { Center } from '../../centers/centers.component';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { getYears } from '../../../utils';
import { colors } from '../../../constants';
import {
    Label,
    PluginServiceGlobalRegistrationAndOptions,
} from 'ng2-charts/lib/base-chart.directive';

@Component({
    selector: 'app-services-chart',
    templateUrl: './services-chart.component.html',
})
export class ServicesChartComponent implements OnChanges {
    @Input() public dataEntries: DataEntry[] = [];

    @Input() public services: Service[] = [];

    @Input() public centers: Center[] = [];

    public selectedCenterId!: string;

    public years!: number[];

    public selectedYears: number[] = [];

    public chartLabels: Label[] = [];
    public chartType: ChartType = 'bar';
    public chartLegend = true;
    public chartPlugins: PluginServiceGlobalRegistrationAndOptions[] = [
        ChartDataLabels,
    ];
    public chartData: ChartDataSets[] = [];

    public chartOptions: ChartOptions = {
        responsive: true,
        plugins: {
            datalabels: {
                anchor: 'center',
                align: 'center',
                textAlign: 'center',
            },
        },
    };

    public constructor() {
        this.years = getYears();
        this.selectedYears.push(new Date().getFullYear());
    }

    public ngOnChanges(): void {
        this.setBarCharData();
    }

    public setBarCharData(): void {
        if (!this.centers.length) return;

        if (!this.selectedCenterId)
            this.selectedCenterId = `${this.centers[0].id}`;

        const center = this.centers.find(c => c.id === +this.selectedCenterId);

        if (!center) return;

        let centerServices: {
            service: Service;
            value: number;
        }[] = [];

        this.dataEntries
            .filter(e => {
                if (e.centerId !== center.id) return false;

                return this.selectedYears.length
                    ? this.selectedYears.includes(e.year)
                    : true;
            })
            .forEach(e => {
                let serviceIdx = centerServices.findIndex(
                    cs => cs.service.id === e.serviceId
                );

                if (serviceIdx === -1) {
                    const service = this.services.find(
                        s => s.id === e.serviceId
                    );
                    if (!service) return;
                    serviceIdx = centerServices.push({ service, value: 0 }) - 1;
                }

                centerServices[serviceIdx].value += e.value;
            });

        centerServices = centerServices.filter(c => c.value > 0);

        this.chartLabels = centerServices.map(s => s.service.name);
        this.chartData = [
            {
                data: centerServices.map(s => s.value),
                label: 'عدد المتعاملين',
                backgroundColor: colors[0],
            },
        ];
    }
}
