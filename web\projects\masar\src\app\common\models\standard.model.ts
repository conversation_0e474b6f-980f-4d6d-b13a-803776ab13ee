import { Tournament } from './tournament.model';
import { Pillar } from './pillar.model';
import { Principle } from './principle.model';
import { LibraryFile } from './library-file.model';
import { StandardMember } from './standard-member.model';
import { StandardTask } from './standard-task.model';

export interface Standard {
    id: string;
    name: string;
    nameAr: string;
    nameEn: string;
    order: number;
    weight: number;
    progress: number;
    teamAchievementSummary: string;
    tournament: Tournament;
    teamFormationLibraryFile: LibraryFile;
    presentationLibraryFile: LibraryFile;
    pillar: Pillar;
    kpiCount: number;
    capabilityCount: number;
    libraryFileCount: number;
    achieved: number;
    principles: Principle[];
    members: StandardMember[];
    tasks: StandardTask[];
}
