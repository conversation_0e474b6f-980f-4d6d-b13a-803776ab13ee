import { Injectable } from '@angular/core';

@Injectable()
export class PrivacyPolicyAlertService {
    private static localStorageKey: string = 'privacy_policy_alert_is_shown';

    public setShown(): void {
        localStorage.setItem(PrivacyPolicyAlertService.localStorageKey, 'true');
    }

    public setHidden(): void {
        localStorage.setItem(
            PrivacyPolicyAlertService.localStorageKey,
            'false'
        );
    }

    public isShown(): boolean {
        let privacyAlert = localStorage.getItem(
            PrivacyPolicyAlertService.localStorageKey
        );
        if (privacyAlert === null) {
            this.setShown();
            privacyAlert = 'true';
        }
        return privacyAlert === 'true';
    }

    public reset(): void {
        this.setShown();
    }
}
