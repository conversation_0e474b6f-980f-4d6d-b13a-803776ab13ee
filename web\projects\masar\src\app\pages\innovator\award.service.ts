import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { miscFunctions, Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';
import { TableResult } from '@masar/common/misc/table';
import { Award } from '@masar/common/models';

@Injectable()
export class AwardService {
    public constructor(private httpClient: HttpClient) {}

    public list(): Observable<TableResult<Award>> {
        return this.httpClient
            .get<Result<TableResult<Award>>>(
                environment.apiUrl + '/innovator/award'
            )
            .pipe(map(result => result.extra));
    }

    public create(innovatorId: string, award: Award): Observable<Award> {
        return this.httpClient
            .post<Result<Award>>(
                environment.apiUrl + '/innovator/award/' + innovatorId,
                miscFunctions.objectToURLParams({
                    award: JSON.stringify(award),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(award: Award): Observable<Award> {
        return this.httpClient
            .put<Result<Award>>(
                environment.apiUrl + '/innovator/award',
                miscFunctions.objectToURLParams({
                    award: JSON.stringify(award),
                })
            )
            .pipe(map(result => result.extra));
    }

    public get(id: string, forEdit: boolean = false): Observable<Award> {
        return this.httpClient
            .get<Result<Award>>(environment.apiUrl + '/innovator/award/' + id, {
                params: new HttpParams().append('forEdit', `${forEdit}`),
            })
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(environment.apiUrl + '/innovator/award/' + id)
            .pipe(map(result => result.messages[0]));
    }
}
