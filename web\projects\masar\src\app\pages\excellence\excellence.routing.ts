import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ExcellenceComponent } from './excellence.component';
import { PillarDetailComponent } from './pillar-detail/pillar-detail.component';
import { StandardDetailComponent } from './standard-detail/standard-detail.component';
import { TournamentDetailComponent } from './tournament-detail/tournament-detail.component';
import { TournamentListComponent } from './tournament-list/tournament-list.component';

const routes: Routes = [
    {
        path: '',
        component: ExcellenceComponent,
        children: [
            {
                path: '',
                component: TournamentListComponent,
                data: {
                    title: 'translate_excellence_dashboard',
                },
            },
            {
                path: 'tournament/:tournamentId',
                component: TournamentDetailComponent,
                data: {
                    title: 'translate_tournament_details',
                },
            },
            {
                path: 'pillar/:pillarId',
                component: PillarDetailComponent,
                data: {
                    title: 'translate_pillar_details',
                },
            },
            {
                path: 'standard/:standardId',
                component: StandardDetailComponent,
                data: {
                    title: 'translate_standard_details',
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class ExcellenceRoutingModule {}
