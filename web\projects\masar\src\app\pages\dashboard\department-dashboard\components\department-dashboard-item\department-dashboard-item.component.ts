import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { Department } from '@masar/common/models';

@Component({
    selector: 'app-department-dashboard-item',
    templateUrl: './department-dashboard-item.component.html',
    styleUrls: ['./department-dashboard-item.component.scss'],
})
export class DepartmentDashboardItemComponent implements OnChanges {
    @Input() public department: Department;

    public cards?: {
        label: string;
        count: number;
        link: string[];
        queryParams?: { [key: string]: string };
    }[];

    public ngOnChanges(changes: SimpleChanges): void {
        if (changes['department']) this.resetCards();
    }

    private resetCards(): void {
        if (!this.department) {
            this.cards = [];
            return;
        }

        this.cards = [
            {
                label: 'translate_kpis_count',
                count: this.department.recursiveKpiCount,
                link: ['/kpi'],
                queryParams: {
                    filter: JSON.stringify({
                        data: { departmentIds: [this.department.id] },
                    }),
                },
            },
            {
                label: 'translate_plans_count',
                count: this.department.recursivePlanCount,
                link: ['/plan'],
                queryParams: {
                    filter: JSON.stringify({
                        data: {
                            assigneeType: 'department',
                            departmentIds: [this.department.id],
                            includeChildDepartments: true,
                        },
                    }),
                },
            },
            {
                label: 'translate_benchmarks_count',
                count: this.department.recursiveBenchmarkCount,
                link: ['/benchmark'],
                queryParams: {
                    filter: JSON.stringify({
                        data: {
                            departmentIds: [this.department.id],
                            includeChildDepartments: true,
                        },
                    }),
                },
            },
            {
                label: 'translate_operations_count',
                count: this.department.recursiveOperationCount,
                link: ['/operation'],
                queryParams: {
                    filter: JSON.stringify({
                        data: {
                            departmentIds: [this.department.id],
                            includeChildDepartments: true,
                        },
                    }),
                },
            },
        ];
    }
}
