<ng-template #itemTemplate let-item>
    <div class="text-xl text-white">{{ item.label }}</div>
    <div class="flex gap-4" [class.flex-col]="item.value > 10000">
        <div class="text-5xl font-extrabold text-white">
            {{ item.value ?? 0 | number : '1.0-2' }}
        </div>
        <div
            class="flex items-center justify-center"
            *ngIf="item.arrowDirection || item.arrowValue !== null"
            [ngStyle]="{
                'color':
                    item.arrowColor === 'success'
                        ? '#00FF00'
                        : item.arrowColor === 'danger'
                        ? '#FF0000'
                        : '#FFFFFF'
            }"
        >
            <div *ngIf="item.arrowValue !== null" class="text-2xl">
                {{ item.arrowValue }}
            </div>
            <div *ngIf="item.arrowDirection" class="text-2xl">
                <span *ngIf="item.arrowDirection === 'up'">↑</span>
                <span *ngIf="item.arrowDirection === 'down'">↓</span>
                <span *ngIf="item.arrowDirection === 'both'">↕</span>
            </div>
        </div>
    </div>
</ng-template>

<div
    *ngIf="groups | statisticalGroupHasValue as validGroups"
    class="grid gap-8 p-4"
>
    <div
        *ngFor="let group of validGroups"
        class="animate__animated animate__fadeIn"
    >
        <div
            *ngIf="group.title"
            class="relative mb-6 text-center text-3xl font-bold text-gray-900"
        >
            <span class="relative z-10 bg-white px-2">{{ group.title }}</span>
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t-2 border-gray-300"></div>
            </div>
        </div>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <ng-container *ngFor="let item of group.items">
                <ng-container *ngIf="hasCallback(item); else linkOrDiv">
                    <button
                        class="flex transform flex-col items-center gap-4 rounded-lg bg-gradient-to-t from-primary-800 to-primary-600 p-8 text-center text-white no-underline shadow-md transition-all duration-300 ease-in-out hover:scale-105 hover:from-primary-700 hover:to-primary-500 hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-primary-500 focus:ring-offset-2"
                        (click)="executeCallback(item)"
                    >
                        <ng-container
                            *ngTemplateOutlet="
                                itemTemplate;
                                context: { $implicit: item }
                            "
                        ></ng-container>
                    </button>
                </ng-container>

                <ng-template #linkOrDiv>
                    <a
                        *ngIf="hasLink(item); else plainDiv"
                        class="flex transform flex-col items-center gap-4 rounded-lg bg-gradient-to-t from-primary-800 to-primary-600 p-8 text-center text-white no-underline shadow-md transition-all duration-300 ease-in-out hover:scale-105 hover:from-primary-700 hover:to-primary-500 hover:no-underline hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-primary-500 focus:ring-offset-2"
                        [routerLink]="getUrlAndQueryParams(item)?.url"
                        [queryParams]="getUrlAndQueryParams(item)?.queryParams"
                    >
                        <ng-container
                            *ngTemplateOutlet="
                                itemTemplate;
                                context: { $implicit: item }
                            "
                        ></ng-container>
                    </a>

                    <ng-template #plainDiv>
                        <div
                            class="flex transform flex-col items-center gap-4 rounded-lg bg-gradient-to-t from-primary-800 to-primary-600 p-8 text-center text-white shadow-md"
                        >
                            <ng-container
                                *ngTemplateOutlet="
                                    itemTemplate;
                                    context: { $implicit: item }
                                "
                            ></ng-container>
                        </div>
                    </ng-template>
                </ng-template>
            </ng-container>
        </div>
    </div>
</div>
