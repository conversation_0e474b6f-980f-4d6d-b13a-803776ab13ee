<div class="gantt-chart-wrapper">
    <!-- Custom Gantt Chart -->
    <div
        #ganttContainer
        class="gantt-container overflow-x-auto rounded border"
        *ngIf="processedData && processedData.length > 0 && !isLoading"
        role="grid"
        [attr.aria-label]="title ? 'Gantt chart for ' + title : 'Gantt chart'"
        [attr.aria-rowcount]="processedData.length + 1"
        [attr.aria-colcount]="displayMonths.length + 1"
    >
        <div class="flex w-full flex-col">
            <!-- Header row -->
            <app-gantt-header
                [title]="title"
                [progress]="progress"
                [startDate]="adjustedStartDate"
                [endDate]="adjustedEndDate"
                [displayMonths]="displayMonths"
            ></app-gantt-header>

            <!-- Main Items -->
            <ng-container
                *ngFor="let item of processedData; trackBy: trackByItemId"
            >
                <app-gantt-task-row
                    [item]="item"
                    [config]="mergedConfig"
                    [taskPosition]="getTaskPosition(item)"
                    [isExpanded]="isTaskExpanded(item.id)"
                    [hasChildren]="hasChildren(item)"
                    [displayDatesLength]="displayDates.length"
                    [taskTemplate]="taskTemplate"
                    (taskClick)="onTaskClick($event)"
                    (toggleTask)="toggleTask($event)"
                ></app-gantt-task-row>

                <!-- Children -->
                <ng-container
                    *ngIf="
                        isTaskExpanded(item.id) &&
                        hasChildren(item) &&
                        mergedConfig.allowExpansion
                    "
                >
                    <app-gantt-subtask-row
                        *ngFor="
                            let child of item.children;
                            trackBy: trackByChildId
                        "
                        [item]="child"
                        [config]="mergedConfig"
                        [taskPosition]="getTaskPosition(child)"
                        [displayDatesLength]="displayDates.length"
                        [subtaskTemplate]="subtaskTemplate"
                        (subtaskClick)="onTaskClick($event)"
                    ></app-gantt-subtask-row>
                </ng-container>
            </ng-container>
        </div>
    </div>

    <!-- Loading state -->
    <div
        *ngIf="isLoading"
        class="flex items-center justify-center p-8"
        role="status"
        aria-live="polite"
        aria-label="Loading chart data"
    >
        <div class="flex items-center space-x-2">
            <div
                class="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-600"
            ></div>
            <span class="text-gray-600">Loading chart data...</span>
        </div>
    </div>

    <!-- Empty state -->
    <div
        *ngIf="!isLoading && (!processedData || processedData.length === 0)"
        class="mt-4 text-center text-gray-500"
        role="status"
        aria-live="polite"
    >
        <div class="flex flex-col items-center space-y-2">
            <em class="fa fa-calendar-times text-4xl text-gray-300"></em>
            <p>{{ 'translate_could_not_find_any_items' | translate }}</p>
        </div>
    </div>
</div>
