<!-- Update general -->
<app-fieldset legend="{{ 'translate_basic_info' | translate }}">
    <mnm-form
        content
        *ngIf="updateGeneralProps.formState"
        [state]="updateGeneralProps.formState"
        [translateLabels]="true"
    >
        <div class="form-group form-row justify-content-center mt-2">
            <button
                type="submit"
                class="btn btn-primary"
                (click)="updateGeneral()"
                [disabled]="updateGeneralProps.isSubmitting"
            >
                <app-loading-ring
                    *ngIf="updateGeneralProps.isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
    </mnm-form>
</app-fieldset>

<!-- Update attachment -->
<app-fieldset legend="{{ 'translate_attachment' | translate }}">
    <mnm-form
        content
        *ngIf="updateAttachmentProps.formState"
        [state]="updateAttachmentProps.formState"
        [translateLabels]="true"
    >
        <div class="grid gap-2">
            <label>
                {{ 'translate_allowed_file_types' | translate }}
            </label>
            <ng-select
                [formControl]="
                    $any(
                        updateAttachmentProps.formState.group.controls['types']
                    )
                "
                [items]="contentTypes"
                [multiple]="true"
                bindValue="id"
                bindLabel="name"
                placeholder="{{ 'translate_allowed_file_types' | translate }}"
            >
                <ng-template ng-option-tmp let-item="item">
                    <div class="flex items-center gap-4">
                        <app-file-icon
                            [icon]="item.name | lowercase"
                        ></app-file-icon>
                        <span>{{ item.name }}</span>
                    </div>
                </ng-template>
            </ng-select>

            <!-- Hint for file types -->
            <div class="text-xs text-gray-500">
                {{ 'translate_hint_all_file_types' | translate }}
            </div>
        </div>

        <div class="form-group form-row justify-content-center mt-2">
            <button
                type="submit"
                class="btn btn-primary"
                (click)="updateAttachment()"
                [disabled]="updateAttachmentProps.isSubmitting"
            >
                <app-loading-ring
                    *ngIf="updateAttachmentProps.isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
    </mnm-form>
</app-fieldset>

<!-- Update logo section -->
<app-fieldset legend="{{ 'translate_app_logo' | translate }}">
    <mnm-form
        content
        *ngIf="updateLogoProps.formState"
        [state]="updateLogoProps.formState"
        [translateLabels]="true"
    >
        <div class="form-group form-row justify-content-center mt-2">
            <button
                type="submit"
                class="btn btn-primary"
                (click)="updateLogo()"
                [disabled]="updateLogoProps.isSubmitting"
            >
                <app-loading-ring
                    *ngIf="updateLogoProps.isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
    </mnm-form>
</app-fieldset>

<!-- Update theme section -->
<app-fieldset legend="{{ 'translate_app_theme' | translate }}">
    <!-- Update logo -->
    <div class="flex flex-col gap-5">
        <div class="grid grid-cols-2 grid-rows-1 gap-4">
            <!-- Primary -->
            <div class="flex flex-col gap-2">
                <!-- Label -->
                <label class="font-bold">
                    {{ 'translate_primary_color' | translate }}
                </label>

                <input
                    [(colorPicker)]="updateThemeProps.primary"
                    [style.background]="updateThemeProps.primary"
                    cpDialogDisplay="inline"
                    cpToggle="true"
                    cpAlphaChannel="disabled"
                    readonly
                    style="
                        width: 50%;
                        display: block;
                        border: 1px solid rgba(0, 0, 0, 0.2);
                        line-height: 1.7rem;
                    "
                />
            </div>

            <!-- Secondary -->
            <div class="flex flex-col gap-2">
                <!-- Label -->
                <label class="font-bold">
                    {{ 'translate_secondary_color' | translate }}
                </label>

                <input
                    [(colorPicker)]="updateThemeProps.secondary"
                    [style.background]="updateThemeProps.secondary"
                    cpDialogDisplay="inline"
                    cpToggle="true"
                    cpAlphaChannel="disabled"
                    readonly
                    style="
                        width: 50%;
                        display: block;
                        border: 1px solid rgba(0, 0, 0, 0.2);
                        line-height: 1.7rem;
                    "
                />
            </div>
        </div>

        <!-- Save button -->
        <button
            type="submit"
            class="btn btn-primary self-center"
            (click)="updateTheme()"
            [disabled]="updateThemeProps.isSubmitting"
        >
            <app-loading-ring
                *ngIf="updateThemeProps.isSubmitting"
                class="me-2"
            ></app-loading-ring>
            <i class="fa-light fa-save me-2"></i>
            <span>{{ 'translate_save' | translate }}</span>
        </button>
    </div>
</app-fieldset>

<!-- Update Top items -->
<app-fieldset [legend]="'translate_dashboard_top_items' | translate">
    <mnm-form
        content
        *ngIf="updateTopItemsProps.formState"
        [state]="updateTopItemsProps.formState"
        [translateLabels]="true"
    >
        <div class="form-group form-row justify-content-center mt-2">
            <button
                type="submit"
                class="btn btn-primary"
                (click)="updateTopItems()"
                [disabled]="updateTopItemsProps.isSubmitting"
            >
                <app-loading-ring
                    *ngIf="updateTopItemsProps.isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
    </mnm-form>
</app-fieldset>

<!-- Top Items Field -->
<ng-template #topItemsFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="
            $any(updateTopItemsProps.formState.group.controls['items'])
        "
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
                <!-- Label Ar-->
                <div class="flex flex-col gap-2">
                    <label
                        >{{ 'translate_label_ar' | translate }}
                        <span style="color: red">*</span>
                    </label>

                    <input
                        type="text"
                        [ngModel]="item.labelAr"
                        (ngModelChange)="item.labelAr = $event; emitChange()"
                    />
                </div>
                <!-- Label  En-->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_label_en' | translate }}</label>
                    <input
                        type="text"
                        [ngModel]="item.labelEn"
                        (ngModelChange)="item.labelEn = $event; emitChange()"
                    />
                </div>

                <!-- Icon Class -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_icon_class' | translate }}</label>
                    <input
                        type="text"
                        [ngModel]="item.iconClass"
                        (ngModelChange)="item.iconClass = $event; emitChange()"
                    />
                    <div class="text-xs text-gray-600">
                        {{ 'translate_icon_class_note' | translate }}
                    </div>
                </div>

                <!-- Tags AR -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_tags_ar' | translate }}</label>
                    <ng-select
                        [(ngModel)]="item.tagsAr"
                        (change)="emitChange()"
                        bindValue="label"
                        [addTag]="true"
                        [multiple]="true"
                    >
                    </ng-select>
                </div>
                <!-- Tags -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_tags_en' | translate }}</label>
                    <ng-select
                        [(ngModel)]="item.tagsEn"
                        (change)="emitChange()"
                        bindValue="label"
                        [addTag]="true"
                        [multiple]="true"
                    >
                    </ng-select>
                </div>

                <!-- Description Ar -->
                <div class="col-span-3 flex flex-col gap-2">
                    <label
                        >{{ 'translate_description_ar' | translate }}
                        <span style="color: red">*</span>
                    </label>
                    <textarea
                        [ngModel]="item.descriptionAr"
                        (ngModelChange)="
                            item.descriptionAr = $event; emitChange()
                        "
                    ></textarea>
                </div>
                <!-- Description -->
                <div class="col-span-3 flex flex-col gap-2">
                    <label>{{ 'translate_description_en' | translate }}</label>
                    <textarea
                        [ngModel]="item.descriptionEn"
                        (ngModelChange)="
                            item.descriptionEn = $event; emitChange()
                        "
                    ></textarea>
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>
