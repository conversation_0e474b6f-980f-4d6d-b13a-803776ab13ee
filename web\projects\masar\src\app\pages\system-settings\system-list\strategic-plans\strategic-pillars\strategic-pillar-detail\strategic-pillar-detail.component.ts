import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { StrategicPillarService } from '../services/strategic-pillar.service';
import { StrategicPillar } from '@masar/common/models/strategic-pillar.model';

@Component({
    selector: 'app-detail',
    templateUrl: './strategic-pillar-detail.component.html',
})
export class StrategicPillarDetailComponent implements OnInit {
    public item?: StrategicPillar;
    public id: string;

    public constructor(
        private readonly strategicPillarService: StrategicPillarService,
        private readonly activatedRoute: ActivatedRoute
    ) {}

    public ngOnInit(): void {
        this.id = this.activatedRoute.snapshot.params['id'];
        if (this.id) {
            this.loadItem();
        }
    }

    private loadItem(): void {
        this.strategicPillarService.get(this.id, false).subscribe(item => {
            this.item = item;
        });
    }
}
