<app-filter-result-box>
    <ng-container *ngFor="let filter of filters" [ngSwitch]="filter.type">
        <!-- Select -->
        <ng-select
            *ngSwitchCase="'select'"
            [items]="filter.items"
            [bindValue]="filter.bindValue"
            [bindLabel]="filter.bindLabel"
            [multiple]="filter.isMultiple"
            [clearable]="filter.isClearable ?? true"
            [searchable]="filter.isSearchable ?? true"
            [placeholder]="filter.label | translate"
            [(ngModel)]="tableController.filter.data[filter.key]"
            (change)="tableController.filter$.next(true)"
        >
        </ng-select>

        <!-- Input -->
        <app-search-input
            *ngSwitchCase="'text'"
            [placeholder]="filter.label | translate"
            [(ngModel)]="tableController.filter.data[filter.key]"
            [tableController]="tableController"
        ></app-search-input>

        <!-- Date -->
        <div class="flex flex-row items-center" *ngSwitchCase="'date'">
            <input
                class="flex-grow"
                appFlatpickr
                #datePicker="appFlatpickr"
                type="date"
                [(ngModel)]="tableController.filter.data[filter.key]"
                (flatPickrChange)="tableController.filter$.next(true)"
                [placeholder]="filter.label | translate"
            />
            <button
                (click)="datePicker.clear()"
                class="btn btn-sm btn-primary ms-1"
            >
                <i class="fa-light fa-times"></i>
            </button>
        </div>
    </ng-container>

    <ng-content></ng-content>
</app-filter-result-box>
