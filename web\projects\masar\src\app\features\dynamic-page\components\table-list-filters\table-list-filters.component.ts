import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { TableController } from '@masar/common/misc/table';
import { MiscApiService } from '@masar/core/services';
import { TableListFilter } from '../../types';

@Component({
    selector: 'app-table-list-filters',
    templateUrl: './table-list-filters.component.html',
})
export class TableListFiltersComponent implements OnChanges {
    @Input() public filters: TableListFilter[];

    @Input() public tableController: TableController<unknown>;

    public constructor(private readonly miscApiService: MiscApiService) {}

    public ngOnChanges(changes: SimpleChanges): void {
        if (changes['filters']) this.filtersHandler();
    }

    private filtersHandler(): void {
        if (!this.filters?.length) return;

        this.filters.forEach(filter => {
            if (filter.type !== 'select' || !filter.miscApiOptions) return;

            this.miscApiService
                .getList(
                    filter.miscApiOptions.endPoint,
                    filter.miscApiOptions.httpQueryParams,
                    filter.miscApiOptions.noCache
                )
                .subscribe(items => (filter.items = items));
        });
    }
}
