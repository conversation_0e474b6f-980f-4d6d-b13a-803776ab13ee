import { PermissionValue } from '@masar/common/types';

/**
 * ItemNavigate Interface
 *
 * The ItemNavigate interface defines the structure for objects that are used to control
 * the navigation behavior for individual items in a UI component, such as a table row or card.
 */
export interface ItemNavigate {
    /**
     * The `targetIdProperty` is the property name from the item object that should be used
     * to uniquely identify it. This identifier is often used to construct the navigation link.
     */
    targetIdProperty: string;

    /**
     * The `link` is a function that takes an identifier (usually derived from `targetIdProperty`)
     * and returns either a string or an array of strings that represents the URL or URLs where the user should be navigated to.
     */
    link: (id: string) => string | string[];

    /**
     * The `permission` field is optional and specifies the required permissions for navigation.
     * It is of type `PermissionValue`, which could be used to control the visibility or click-ability
     * of the item based on user permissions.
     */
    permission?: PermissionValue;
}
