import { Component, Input } from '@angular/core';
import { SharedAttachmentService } from '@masar/features/attachment';

@Component({
    selector: 'app-property-value-file',
    templateUrl: './property-value-file.component.html',
})
export class PropertyValueFileComponent {
    @Input() public value: [string, string];

    public constructor(
        private readonly sharedAttachmentService: SharedAttachmentService
    ) {}

    public downloadAttachment([endPoint, contentType]: [string, string]): void {
        this.sharedAttachmentService
            .downloadAttachment(undefined, undefined, endPoint)
            .subscribe(file =>
                this.sharedAttachmentService.downloadBlob(file, contentType)
            );
    }
}
