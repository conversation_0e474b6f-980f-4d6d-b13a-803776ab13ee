import { ListSectionData } from '@masar/features/dynamic-page/interfaces/list-section-data.interface';
import { MiscApiEndPoint, PermissionValue } from '@masar/common/types';
import { MnmFormField } from '@masar/shared/components';
import { Observable } from 'rxjs';
import { ActionDialog } from '../types';

export interface TableListDataAction<T, D = unknown, C = unknown> {
    tooltip?: string;
    label?: string;
    icon?: string;
    type?: 'info' | 'danger' | 'warning' | 'success' | 'primary';
    click?: (item: T) => () => void | undefined;
    confirm?: (item: T) => { title: string } | undefined;
    isDisabled?: (item: T) => boolean;
    isDone?: boolean;
    progress?: number;
    count?: number;
    permission?: PermissionValue;
    form?: {
        title: string;
        fields: () => MnmFormField[];
        id?: string;
        getForEditCb?: (id: string) => Observable<any>;
        createCb?: (item: any) => Observable<any>;
        updateCb?: (item: any) => Observable<any>;
        miscList?: [MiscApiEndPoint, string][];
    };
    linker?: {
        bodyParameterName: string;
        endPoint: string;
        data: ListSectionData<D>;
        title: string;
    };
    dialog?: ActionDialog<C>;
}
