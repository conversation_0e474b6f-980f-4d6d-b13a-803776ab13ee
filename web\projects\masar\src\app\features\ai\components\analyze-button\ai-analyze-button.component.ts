import { Component, HostBinding, Input, OnDestroy } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { ModalService } from 'mnm-webapp';
import { AiAnalysisResultComponent } from './components/ai-analysis-result/ai-analysis-result.component';
import { takeUntil } from 'rxjs/operators';
import { AppSettingFetcherService } from '@masar/core/services';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-ai-analyze-button',
    templateUrl: './ai-analyze-button.component.html',
    styleUrls: ['./ai-analyze-button.component.scss'],
})
export class AiAnalyzeButtonComponent implements OnDestroy {
    @Input() public analysisObservable: Observable<string>;
    @Input() public style: 'full' | 'compact' = 'full';

    public isEnabled: boolean = false;

    private readonly unsubscribeAll = new Subject();

    public constructor(
        private readonly modalService: ModalService,
        private readonly appSettingFetcherService: AppSettingFetcherService,
        private readonly translateService: TranslateService
    ) {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(settings => {
                this.isEnabled = settings.aiSetting.isEnabled;
            });
    }

    public analyze(): void {
        if (!this.isEnabled) return;
        this.translateService
            .get('translate_analysis_result')
            .subscribe(async title => {
                await this.modalService.show(AiAnalysisResultComponent, {
                    title,
                    beforeInit: c => {
                        c.result$ = this.analysisObservable;
                    },
                });
            });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    @HostBinding('class.hidden')
    protected get className(): boolean {
        return !this.isEnabled;
    }
}
