<app-content contentTitle="{{ 'translate_innovator_overview' | translate }}">
    <div content class="text-center text-xl font-bold">
        <div
            *appWaitUntilLoaded="innovator"
            class="grid grid-cols-1 grid-rows-4 gap-2 md:grid-cols-4 md:grid-rows-1"
        >
            <!-- Innovations -->
            <a
                *ngFor="let item of innovatorOverview"
                class="relative flex flex-col justify-center rounded bg-green-600 p-4 font-bold text-white no-underline transition-colors hover:bg-green-700 hover:text-white hover:no-underline"
            >
                <!-- Background icon -->
                <i
                    class="fa-light absolute bottom-3 end-3 text-6xl opacity-20"
                    [class]="item.icon"
                ></i>

                <!-- Count -->
                <div class="mb-2 text-2xl">
                    {{ innovator?.[item.propertyName] || 0 }}
                </div>

                <!-- Title -->
                <div class="text-lg">
                    {{ item.label | translate }}
                </div>
            </a>
        </div>
    </div>
</app-content>
