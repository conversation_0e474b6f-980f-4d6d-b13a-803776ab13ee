import { Component, OnD<PERSON>roy } from '@angular/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { TableController } from '@masar/common/misc/table';
import { Activity } from '@masar/common/models';
import { ActivityService } from '../activity.service';

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
})
export class ListComponent implements OnD<PERSON>roy {
    public tableController: TableController<
        Activity,
        { keyword?: string; year?: number }
    >;
    public currentlyDeleting: string[] = [];

    public constructor(
        private activityService: ActivityService,
        private notificationService: NotificationService
    ) {
        this.tableController = new TableController<
            Activity,
            { keyword?: string; year?: number }
        >(
            filter =>
                this.activityService.list(
                    filter.data.keyword,
                    filter.data.year,
                    filter.pageNumber,
                    filter.pageSize
                ),
            { data: { keyword: '', year: null } }
        );

        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public delete(item: Activity): void {
        this.currentlyDeleting.push(item.id);

        this.activityService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }
}
