import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MnmFormState } from '@masar/shared/components';
import { finalize } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { AfterSubmitNavigateTo } from '@masar/common/types';
import { HelperService } from '@masar/core/services';

@Component({
    selector: 'app-new-form',
    templateUrl: './new-form.component.html',
})
export class NewFormComponent implements OnInit {
    @Input() public formState: MnmFormState;

    @Input() public link?: string[];

    @Input() public id?: string;

    @Input() public getForEditCb?: <T>(id: string) => Observable<T>;

    @Input() public createCb: <T>(item: T) => Observable<T>;

    @Input() public updateCb?: <T>(item: T) => Observable<T>;

    @Input() public afterSubmitNavigateTo?: AfterSubmitNavigateTo;

    @Input() public hideNavigateCases?: AfterSubmitNavigateTo[];

    @Input() public submitButtonLabel = 'translate_save';

    @Output() public submitted = new EventEmitter<void>();

    public isSubmitting: boolean;

    public constructor(
        private readonly translateService: TranslateService,
        private readonly notificationService: NotificationService,
        private readonly helperService: HelperService
    ) {}

    public ngOnInit(): void {
        if (this.id)
            this.getForEditCb?.(this.id).subscribe(item => this.fillForm(item));
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) return;

        this.isSubmitting = true;

        const observable = this.id
            ? this.updateCb?.(this.formState.group.getRawValue())
            : this.createCb(this.formState.group.getRawValue());

        observable
            ?.pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(item => {
                const label = this.id
                    ? 'translate_item_updated_successfully'
                    : 'translate_item_added_successfully';

                const translatedLabel = this.translateService.instant(label);

                this.notificationService.notifySuccess(translatedLabel);

                if (this.link) {
                    this.helperService.afterSubmitNavigationHandler(
                        this.afterSubmitNavigateTo ?? 'ask',
                        this.link,
                        item['id'],
                        this.hideNavigateCases
                    );
                }

                this.submitted.emit();
            });
    }

    private fillForm<T>(item: T): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }
}
