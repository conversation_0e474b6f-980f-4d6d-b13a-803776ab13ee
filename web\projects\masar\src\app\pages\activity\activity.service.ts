import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TableResult } from '@masar/common/misc/table';
import { Activity, Innovation } from '@masar/common/models';
import { miscFunctions, Result } from 'mnm-webapp';
import { environment } from '@masar/env/environment';
import { map } from 'rxjs/operators';

@Injectable()
export class ActivityService {
    public constructor(private httpClient: HttpClient) {}

    public list(
        keyword: string,
        year: number,
        pageNumber: number,
        pageSize: number
    ): Observable<TableResult<Activity>> {
        let params = new HttpParams();
        params = params.append('keyword', keyword);
        params = params.append('year', year);
        params = params.append('pageNumber', `${pageNumber}`);
        params = params.append('pageSize', `${pageSize}`);

        return this.httpClient
            .get<Result<TableResult<Activity>>>(
                `${environment.apiUrl}/activity`,
                {
                    params,
                }
            )
            .pipe(map(result => result.extra));
    }

    public create(activity: Activity): Observable<Activity> {
        return this.httpClient
            .post<Result<Activity>>(
                `${environment.apiUrl}/activity`,
                miscFunctions.objectToURLParams({
                    activity: JSON.stringify(activity),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(activity: Activity): Observable<Activity> {
        return this.httpClient
            .put<Result<Activity>>(
                `${environment.apiUrl}/activity`,
                miscFunctions.objectToURLParams({
                    activity: JSON.stringify(activity),
                })
            )
            .pipe(map(result => result.extra));
    }

    public get(id: string, forEdit = false): Observable<Activity> {
        return this.httpClient
            .get<Result<Activity>>(
                `${environment.apiUrl}/activity/${id}?forEdit=${forEdit}`
            )
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(`${environment.apiUrl}/activity/${id}`)
            .pipe(map(result => result.messages[0]));
    }

    // Innovation linking methods.
    public listLinkedInnovations(
        id: string,
        title: string,
        pageNumber: number = 0,
        pageSize: number = 20
    ): Observable<TableResult<Innovation>> {
        return this.httpClient
            .get<Result<TableResult<Innovation>>>(
                `${environment.apiUrl}/activity/innovation/${id}`,
                {
                    params: new HttpParams()
                        .append('title', title)
                        .append('pageNumber', `${pageNumber}`)
                        .append('pageSize', `${pageSize}`),
                }
            )
            .pipe(map(res => res.extra));
    }

    public linkInnovation(
        id: string,
        innovationId: string
    ): Observable<string> {
        return this.httpClient
            .post<Result<any>>(
                `${environment.apiUrl}/activity/innovation/${id}`,
                miscFunctions.objectToURLParams({ innovationId: innovationId })
            )
            .pipe(map(res => res.messages[0]));
    }

    public unlinkInnovation(
        id: string,
        innovationId: string
    ): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(
                `${environment.apiUrl}/activity/innovation/${id}?innovationId=${innovationId}`
            )
            .pipe(map(res => res.messages[0]));
    }
}
