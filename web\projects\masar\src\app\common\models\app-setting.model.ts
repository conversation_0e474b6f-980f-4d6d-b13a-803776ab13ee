import { PlanSetting } from './plan-setting.model';
import { KpiSetting } from './kpi-setting.model';
import { ThemeSetting } from './theme-setting.model';
import { Office365Setting } from './office-365-setting.model';
import { LdapSetting } from './ldap-setting.model';
import { DashboardTopItem } from '@masar/pages/dashboard/main-dashboard/interfaces';
import { OperationSetting } from './operation-setting.model';
import { ServiceSetting } from '@masar/pages/system-settings/setting/app-setting/service/service-setting.interface';
import { RiskSetting } from '@masar/common/models/risk-setting.model';
import { AllowedFileType } from '@masar/features/attachment/types';
import { DashboardSetting } from '@masar/common/models/dashboard-setting.model';
import { StatisticalReportSetting } from './statistical-report-setting.model';
import { PartnershipSetting } from '@masar/common/models/partnership-setting.interface';
import { ImprovementOpportunitySetting } from './improvement-opportunity-setting.model';
import { AiSetting } from '@masar/common/models/ai-setting.model';
import { StrategicGoalSetting } from './strategic-goal-setting';
import { BenchmarkSetting } from './benchmark-Setting.model';

export interface AppSetting {
    appName: string;
    appEmailDomain: string;
    appId: string;
    allowedFileTypes?: AllowedFileType[];
    dashboardSetting: DashboardSetting;
    dashboardTopItemsTitle?: string;
    dashboardTopItems?: DashboardTopItem[];
    theme: ThemeSetting;
    ldapSetting: LdapSetting;
    office365Setting: Office365Setting;
    aiSetting: AiSetting;
    planSetting: PlanSetting;
    serviceSetting: ServiceSetting;
    riskSetting: RiskSetting;
    kpiSetting: KpiSetting;
    operationSetting: OperationSetting;
    statisticalReportSetting: StatisticalReportSetting;
    partnershipSetting: PartnershipSetting;
    improvementOpportunitySetting: ImprovementOpportunitySetting;
    strategicGoalSetting: StrategicGoalSetting;
    benchmarkSetting: BenchmarkSetting;
}
