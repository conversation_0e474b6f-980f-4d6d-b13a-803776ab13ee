<!-- Filter -->
<app-filter-result-box>
    <!-- Kpi Name -->
    <app-search-input
        *ngIf="shownFilters.keyword"
        [placeholder]="'translate_search_by_kpi_name'"
        [(ngModel)]="tableController.filter.data.keyword"
        [tableController]="tableController"
    ></app-search-input>

    <!-- Department -->
    <ng-select
        *ngIf="shownFilters.departmentIds"
        [items]="departments"
        bindLabel="name"
        bindValue="id"
        [multiple]="true"
        [(ngModel)]="tableController.filter.data.departmentIds"
        (change)="tableController.filter$.next(true)"
        placeholder="{{ 'translate_department' | translate }}"
    ></ng-select>

    <!-- Status -->
    <ng-select
        *ngIf="shownFilters.assignees"
        [items]="assignees"
        [multiple]="true"
        bindValue="id"
        bindLabel="name"
        [placeholder]="'translate_required_action' | translate"
        [(ngModel)]="tableController.filter.data.assignees"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>
</app-filter-result-box>

<!-- Table -->
<app-list-loading [items]="tableController.items">
    <table>
        <thead>
            <tr>
                <th *ngIf="shownFields.year">
                    {{ 'translate_year' | translate }}
                </th>
                <th *ngIf="shownFields.department">
                    {{ 'translate_department' | translate }}
                </th>
                <th *ngIf="shownFields.kpi">
                    {{ 'translate_kpi' | translate }}
                </th>
                <th *ngIf="shownFields.stage">
                    {{ 'translate_required_action' | translate }}
                </th>
                <th *ngIf="shownFields.time">
                    {{ 'translate_time' | translate }}
                </th>
                <th style="width: 0">
                    <i class="fa-light fa-gear"></i>
                </th>
            </tr>
        </thead>

        <tbody>
            <tr *ngFor="let item of tableController.items">
                <!-- year -->
                <td *ngIf="shownFields.year">
                    {{ item.result.year }}
                </td>

                <!-- department -->
                <td *ngIf="shownFields.department">
                    <a
                        *appHasPermissionId="
                            permissionList.departmentRead;
                            else justNameTemplateRef
                        "
                        [routerLink]="[
                            '',
                            'department',
                            'detail',
                            item.result.department.id
                        ]"
                    >
                        {{ item.result.department.name }}
                    </a>
                    <ng-template #justNameTemplateRef>
                        {{ item.result.department.name }}
                    </ng-template>
                </td>

                <!-- kpi -->
                <td *ngIf="shownFields.kpi">
                    <a
                        *appHasPermissionId="
                            permissionList.kpiRead;
                            else justNameTemplateRef
                        "
                        [routerLink]="['', 'kpi', 'detail', item.result.kpi.id]"
                    >
                        {{ item.result.kpi.name }}
                    </a>
                    <ng-template #justNameTemplateRef>
                        {{ item.result.kpi.name }}
                    </ng-template>
                </td>

                <!-- Status -->
                <td *ngIf="shownFields.stage" class="text-center">
                    <div
                        class="badge text-base"
                        [ngClass]="{
                            'badge-primary':
                                item.currentTransfer.assignee === 'data_entry',
                            'badge-info':
                                item.currentTransfer.assignee === 'level1' ||
                                item.currentTransfer.assignee === 'level2',
                            'badge-warning':
                                item.currentTransfer.assignee === 'kpi_manager',
                            'badge-success':
                                item.currentTransfer.assignee === 'done'
                        }"
                    >
                        {{
                            'translate_' + item.currentTransfer.assignee
                                | translate
                        }}
                    </div>
                </td>

                <!-- Time -->
                <td *ngIf="shownFields.time">
                    <!-- Shortcut for saving result to the variable `status` -->
                    <ng-container
                        *ngIf="
                            item.startTime | daysLeft : item.endTime as status
                        "
                    >
                        <div class="mb-2 flex justify-center gap-2 text-sm">
                            <span>
                                <span class="font-semibold text-gray-400">
                                    {{ 'translate_from' | translate }}:
                                </span>
                                <span class="text-gray-500">
                                    {{ item.startTime | date : 'mediumDate' }}
                                </span>
                            </span>
                            <span>
                                <span class="font-semibold text-gray-400">
                                    {{ 'translate_to' | translate }}:
                                </span>
                                <span class="text-gray-500">
                                    {{ item.endTime | date : 'mediumDate' }}
                                </span>
                            </span>
                        </div>
                        <p [class]="status.class" class="text-center text-sm">
                            {{ status.label }}
                        </p>
                    </ng-container>
                </td>

                <!-- Control buttons -->
                <td>
                    <app-dropdown>
                        <!-- View link -->
                        <button
                            (click)="showResponseDetailDialog(item.id)"
                            class="btn btn-sm btn-success"
                            [appTooltip]="'translate_show' | translate"
                        >
                            <i class="fa-light fa-eye fa-fw"></i>
                        </button>

                        <!-- Transfer Approval -->
                        <ng-container
                            *appHasPermissionId="
                                permissionList.kpiResultResponseApprovalTransfer
                            "
                        >
                            <button
                                *ngIf="
                                    item.currentTransfer.assignee ===
                                        'level1' ||
                                    item.currentTransfer.assignee === 'level2'
                                "
                                (click)="
                                    showTransferApprovalDialog(
                                        item.id,
                                        item.currentTransfer.users
                                    )
                                "
                                class="btn btn-sm btn-primary"
                                [appTooltip]="
                                    'translate_transfer_approval' | translate
                                "
                            >
                                <i class="fa-light fa fa-share"></i>
                            </button>
                        </ng-container>
                    </app-dropdown>
                </td>
            </tr>
        </tbody>
    </table>
    <app-table-pagination
        [tableController]="tableController"
    ></app-table-pagination>
</app-list-loading>
