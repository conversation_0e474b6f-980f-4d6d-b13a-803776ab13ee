import { BenchmarkKpiResult } from './benchmark-kpi-result.model';
import { BenchmarkLibraryFile } from './benchmark-library-file.model';
import { BenchmarkOtherManagement } from './benchmark-other-management.model';
import { BenchmarkRequestReason } from './benchmark-request-reason.model';
import { BenchmarkSelectionReason } from './benchmark-selection-reason.model';
import { BenchmarkVisitor } from './benchmark-visitor.model';
import { Department } from './department.model';
import { Operation } from './operation.model';
import { Opportunity } from './opportunity.model';
import { Partner } from './partner.model';
import { StrategicGoal } from './strategic-goal.model';
import { User } from './user.model';

export interface Benchmark {
    id: string;
    visitDate: Date;
    language: string;
    type: string;
    method: string;
    managementType: string;
    entityType: string;
    entityName: string;
    otherRequestReasons: string;
    otherSelectionReasons: string;
    coordinatorEmployeeNumber: string;
    coordinatorRank: string;
    coordinatorFullName: string;
    coordinatorEmail: string;
    coordinatorPhone: string;
    coordinatorOfficeNumber: string;
    agenda: string;
    department: Department;
    partner: Partner;
    goals: StrategicGoal[];
    operations: Operation[];
    otherManagements: BenchmarkOtherManagement[];
    requestReasons: BenchmarkRequestReason[];
    selectionReasons: BenchmarkSelectionReason[];
    visitors: BenchmarkVisitor[];
    kpiResults: BenchmarkKpiResult[];
    kpiResultCount: number;
    visitorCount: number;
    firstApprovalUser: User;
    firstApprovalTime: Date;
    canBeFirstApproved: boolean;
    secondApprovalUser: User;
    secondApprovalTime: Date;
    canBeSecondApproved: boolean;
    canHaveReport: boolean;
    reportTime: Date;
    reportType: string;
    reportDetails: string;
    libraryFiles: BenchmarkLibraryFile[];
    opportunityCount: number;
    improvementOpportunities: Opportunity[];
    benchmarkPoints: string;
    benefitRate: string;
    isPreviouslyCompared: boolean;
    comparedWith: string;
    reasonForComparison: string;
    currentPerformanceSummary: string;
}
