import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { forkJoin, Observable, of, Subscription } from 'rxjs';
import { Department } from '../departments/departments.component';
import { Service } from '../services/services.component';
import { Center } from '../centers/centers.component';
import { months } from '../../constants';
import { ExcelSheetService } from '../../excel-sheet.service';
import { finalize, switchMap, tap } from 'rxjs/operators';
import { clearFormArray, getRandomInt, getYears } from '../../utils';

export interface DataEntry {
    id: number;
    departmentId: number;
    department: Department;
    serviceId: number;
    service: Service;
    centerId: number;
    center: Center;
    year: number;
    month: number;
    value: number;
}

@Component({
    selector: 'app-data-entry',
    templateUrl: './data-entry.component.html',
})
export class DataEntryComponent implements OnInit, OnD<PERSON>roy {
    public dataEntries: DataEntry[] = [];
    public isLoadingDataEntries = false;
    public isSubmittingDataEntries = false;

    public departments: Department[] = [];
    public centers: Center[] = [];
    public services: Service[] = [];
    public years!: number[];
    public months = months;

    public dataEntryFilterForm = this.fb.group({
        departmentId: [null, [Validators.required]],
        centerId: [null, [Validators.required]],
        year: [
            null,
            [Validators.required, Validators.min(1000), Validators.max(10000)],
        ],
        month: [
            null,
            [Validators.required, Validators.min(1), Validators.max(12)],
        ],
    });

    public dataEntryForm = this.fb.array([]) as any;

    private readonly subs$: Subscription[] = [];

    public constructor(
        private readonly fb: FormBuilder,
        private readonly api: ExcelSheetService
    ) {
        this.years = getYears();
    }

    public ngOnInit(): void {
        forkJoin([
            this.api.getDepartments(),
            this.api.getCenters(),
            this.api.getServices(),
        ]).subscribe(([departments, centers, services]) => {
            this.departments = departments;
            this.centers = centers;
            this.services = services;
            this.formListener();
            // this.seedData();
        });
    }

    public seedData(): void {
        this.years.forEach(year => {
            this.months.forEach(month => {
                this.departments.forEach(department => {
                    this.centers.forEach(center => {
                        let centerServices = 0;

                        this.services.forEach(service => {
                            if (centerServices > 5) return;

                            this.api
                                .createDataEntry(
                                    +department.id,
                                    +center.id,
                                    +year,
                                    +month.value,
                                    service.id,
                                    getRandomInt(0, 200)
                                )
                                .subscribe();
                            centerServices++;
                        });
                    });
                });
            });
        });
    }

    public ngOnDestroy(): void {
        this.subs$.forEach(sub => sub.unsubscribe());
    }

    public onSaveForm(): void {
        if (this.dataEntryFilterForm.invalid) return;
        const { departmentId, year, month, centerId } = this.dataEntryFilterForm
            .value as any;
        const values = this.dataEntryForm.value;

        const obs$ = values
            .filter(v => {
                if (typeof v.value === 'number') return true;
                return !!v.id;
            })
            .map(value => {
                const serviceId = +(value.serviceId as unknown as string);
                const newValue = +(value.value as unknown as string);

                if (!value.id) {
                    // Create new value
                    return this.api.createDataEntry(
                        +departmentId,
                        +centerId,
                        +year,
                        +month,
                        serviceId,
                        newValue
                    );
                }

                return typeof value.value === 'number'
                    ? this.api.updateDataEntryValue(+value.id, newValue)
                    : this.api.deleteDataEntryValue(+value.id);
            });

        this.isSubmittingDataEntries = true;

        forkJoin(obs$)
            .pipe(
                switchMap(() => this.refreshDataEntries()),
                finalize(() => (this.isSubmittingDataEntries = false))
            )
            .subscribe();
    }

    private formListener(): void {
        const sub$ = this.dataEntryFilterForm.valueChanges
            .pipe(switchMap(() => this.refreshDataEntries()))
            .subscribe();

        this.subs$.push(sub$);
    }

    private refreshDataEntries(): Observable<null | DataEntry[]> {
        this.isLoadingDataEntries = true;
        clearFormArray(this.dataEntryForm);

        if (this.dataEntryFilterForm.invalid) return of(null);

        const { departmentId, centerId, month, year } = this.dataEntryFilterForm
            .value as never;

        return this.api
            .getDataEntries(
                `?year=${year}&month=${month}&departmentId=${departmentId}&centerId=${centerId}`
            )
            .pipe(
                tap(dataEntries => {
                    this.services.forEach(service => {
                        const entry = dataEntries.find(
                            d => d.serviceId === service.id
                        );
                        this.addServiceValue(
                            service.name,
                            service.id,
                            entry ? entry.id : null,
                            entry ? entry.value : null
                        );
                    });
                }),
                finalize(() => (this.isLoadingDataEntries = false))
            );
    }

    private addServiceValue(
        serviceLabel: string,
        serviceId: number,
        id: number | null = null,
        value: number | null = null
    ): void {
        const formItem = this.fb.group({
            id: [id],
            serviceId: [serviceId, [Validators.required]],
            serviceLabel: [serviceLabel, [Validators.required]],
            value: [value, [Validators.required]],
        });

        this.dataEntryForm.push(formItem);
    }
}
