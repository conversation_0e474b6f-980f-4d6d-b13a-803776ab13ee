import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { miscFunctions, Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';

@Injectable()
export class ForgetPasswordService {
    public constructor(private httpClient: HttpClient) {}

    public forgetPassword(email: string): Observable<string> {
        return this.httpClient
            .post<Result>(
                `${environment.apiUrl}/account/forget-password`,
                miscFunctions.objectToURLParams({
                    forgetPassword: JSON.stringify({ email }),
                })
            )
            .pipe(map(result => result.messages[0]));
    }
}
