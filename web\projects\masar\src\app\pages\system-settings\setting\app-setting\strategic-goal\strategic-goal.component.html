<app-fieldset legend="{{ 'translate_general' | translate }}">
    <div
        *appWaitUntilLoaded="strategicGoalSetting"
        class="mb-5 grid grid-cols-3 items-center gap-5"
    >
        <!-- Is enabled -->
        <div class="flex flex-row items-center gap-3">
            <label class="text-sm font-medium">
                <span>{{
                    'translate_activate_overall_performance' | translate
                }}</span>
            </label>

            <label class="relative h-6 w-11 flex-shrink-0 cursor-pointer">
                <input
                    type="checkbox"
                    [(ngModel)]="
                        strategicGoalSetting.isOverallPerformanceCalculationEnabled
                    "
                    class="peer sr-only"
                />
                <div
                    class="absolute inset-0 rounded-full transition-colors duration-200 ease-in-out peer-focus:ring-2 peer-focus:ring-blue-300"
                    [ngClass]="{
                        'bg-blue-500':
                            strategicGoalSetting.isOverallPerformanceCalculationEnabled,
                        'bg-gray-300':
                            !strategicGoalSetting.isOverallPerformanceCalculationEnabled
                    }"
                ></div>
                <div
                    class="absolute left-1 top-1 h-4 w-4 rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out"
                    [ngClass]="{
                        'translate-x-5':
                            strategicGoalSetting.isOverallPerformanceCalculationEnabled,
                        'translate-x-0':
                            !strategicGoalSetting.isOverallPerformanceCalculationEnabled
                    }"
                ></div>
            </label>
        </div>
    </div>
</app-fieldset>

<div class="text-center">
    <button
        type="submit"
        class="btn btn-primary"
        (click)="save()"
        [disabled]="isSubmitting"
    >
        <app-loading-ring *ngIf="isSubmitting" class="me-2"></app-loading-ring>
        <i class="fa-light fa-save me-2"></i>
        <span>{{ 'translate_save' | translate }}</span>
    </button>
</div>
