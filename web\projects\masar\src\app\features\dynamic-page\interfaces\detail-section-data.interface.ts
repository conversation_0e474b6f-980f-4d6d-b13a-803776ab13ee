import { PropertyValueOptions } from '@masar/features/dynamic-page/interfaces/property-value-options.interface';

export interface DetailSectionData<T> {
    type: 'detail';

    /**
     * The header text for this particular section.
     * Usually displayed prominently to label the section.
     */
    header: string;

    /**
     * (Optional) Flag to determine if each item should be displayed in its own row.
     * If false or empty, items may share a row based on page innerWidth if is greater than 1200px.
     */
    eachItemInRow?: boolean;

    /**
     * An array of content items for each section.
     * Each item represents a key-value pair to be displayed on the detail page.
     */
    content: PropertyValueOptions<T>[];

    gridSize?: number;
}
