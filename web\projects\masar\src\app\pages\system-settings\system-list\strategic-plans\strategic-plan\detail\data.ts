import { permissionList } from '@masar/common/constants';
import { DetailPageData } from '@masar/features/dynamic-page/interfaces';
import { StrategicPlan } from '@masar/common/models';

export const detailPageData: DetailPageData<StrategicPlan> = {
    route: 'system-list/strategic-plans',
    title: 'translate_strategic_plan_details',
    permissions: {
        write: permissionList.fullAccess,
    },
    sections: [
        {
            type: 'detail',
            header: 'translate_strategic_plan_details',
            eachItemInRow: true,
            content: [
                {
                    label: 'translate_name',
                    property: 'name',
                },
                {
                    label: 'translate_start_year',
                    property: 'startYear',
                },
                {
                    label: 'translate_end_year',
                    property: 'endYear',
                },
                {
                    label: 'translate_vision',
                    property: 'vision',
                },
                {
                    label: 'translate_mission',
                    property: 'mission',
                },
            ],
            gridSize: 8,
        },
        {
            type: 'table-list',
            header: 'translate_strategic_goals',
            propertyName: 'strategicGoals',
            gridSize: 6,
            columns: [
                {
                    label: 'translate_name',
                    property: 'name',
                    itemNavigate: {
                        targetIdProperty: 'id',
                        link: id => [
                            '',
                            'system-list',
                            'strategic-goal',
                            'detail',
                            id,
                        ],
                        permission: permissionList.strategicGoal,
                    },
                },
                {
                    label: 'translate_code',
                    property: 'code',
                },
                {
                    label: 'translate_description',
                    property: 'description',
                },
            ],
        },
        {
            type: 'table-list',
            header: 'translate_strategic_values',
            propertyName: 'strategicValues',
            gridSize: 6,
            columns: [
                {
                    label: 'translate_name',
                    property: 'name',
                },
            ],
        },
    ],
};
