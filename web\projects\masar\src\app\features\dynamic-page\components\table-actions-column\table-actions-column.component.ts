import { Component, EventEmitter, Input, Output } from '@angular/core';
import { PermissionValue } from '@masar/common/types';

@Component({
    selector: 'app-table-actions-column',
    templateUrl: './table-actions-column.component.html',
})
export class TableActionsColumnComponent {
    @Input() public showEditButton?: boolean;

    @Input() public showDeleteButton?: boolean;

    @Input() public isDisabled?: boolean;

    @Input() public editLink?: string[];

    @Input() public editPermission?: PermissionValue;

    @Input() public deletePermission?: PermissionValue;

    @Output() public edit = new EventEmitter<void>();

    @Output() public delete = new EventEmitter<void>();

    public onDelete(): () => void {
        return () => this.delete.emit();
    }

    public onEdit(): () => void {
        return () => this.edit.emit();
    }
}
