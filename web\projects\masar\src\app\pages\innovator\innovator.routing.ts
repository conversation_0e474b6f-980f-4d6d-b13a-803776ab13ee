import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { permissionList } from '@masar/common/constants';
import { DetailComponent } from './detail/detail.component';
import { InnovatorComponent } from './innovator.component';
import { ListComponent } from './list/list.component';
import { NewComponent } from './new/new.component';

const routes: Routes = [
    {
        path: '',
        component: InnovatorComponent,
        children: [
            {
                path: '',
                component: ListComponent,
                data: {
                    title: 'translate_innovators',
                    breadcrumb: ['translate_innovators'],
                    permissionId: permissionList.innovation,
                },
            },
            {
                path: 'new',
                component: NewComponent,
                data: {
                    title: 'translate_new_innovator',
                    breadcrumb: ['translate_innovators', 'translate_add_new'],
                    permissionId: permissionList.innovation,
                },
            },
            {
                path: 'edit/:id',
                component: NewComponent,
                data: {
                    title: 'translate_edit_innovator',
                    breadcrumb: ['translate_innovators', 'translate_edit'],
                    permissionId: permissionList.innovation,
                },
            },
            {
                path: 'detail/:id',
                component: DetailComponent,
                data: {
                    title: 'translate_innovator_details',
                    breadcrumb: ['translate_innovators', 'translate_details'],
                    permissionId: permissionList.innovation,
                },
            },
            {
                path: 'profile',
                component: DetailComponent,
                data: {
                    title: 'translate_innovator_profile',
                    breadcrumb: [
                        'translate_innovators',
                        'translate_innovator_profile',
                    ],
                    permissionId: permissionList.innovator,
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class InnovatorRoutingModule {}
