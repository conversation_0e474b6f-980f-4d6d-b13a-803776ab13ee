<app-page [pageTitle]="data.title | translate">
    <!-- Tools -->
    <ng-container tools>
        <!-- New -->
        <a
            [routerLink]="data.route | pushToArray : undefined : ['new']"
            class="btn btn-sm btn-outline-white"
            *appHasPermissionId="data.permissions.write"
        >
            <i class="fa-light fa-plus me-2"></i>
            <span>{{ 'translate_add_new' | translate }}</span>
        </a>

        <!-- Back -->
        <!-- <app-go-back-button></app-go-back-button> -->
    </ng-container>

    <!-- Content -->
    <ng-container content *ngIf="tableController">
        <app-table-list
            [tableController]="tableController"
            [data]="data"
            (delete)="delete.emit($event)"
        >
            <ng-content select="[filter]"></ng-content>
        </app-table-list>
    </ng-container>
</app-page>
