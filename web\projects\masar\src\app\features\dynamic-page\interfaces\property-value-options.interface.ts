import { ItemNavigate } from './item-navigate.interface';
import { MiscApiEndPoint } from '@masar/common/types';

/**
 * PropertyValueOptions interface defines the shape and requirements
 * for objects used to describe how property values are displayed and interacted with.
 *
 * @typeParam T - The type of the item object that this property value belongs to.
 */
export interface PropertyValueOptions<T> {
    /**
     * The `label` field specifies the text that will serve as the display label
     * for this property. The label is usually localized.
     */
    label: string;

    headerWidth?: string;

    /**
     * The `property` field is used to specify which property of the item's data
     * should be displayed. This serves as the key to look up the value.
     */
    property?: string;

    translateItemNamePipe?: MiscApiEndPoint;

    /**
     * `valueFactory` is an optional function that takes an item of type `T` and
     * returns a string or number. This is useful for custom formatting of the displayed value.
     */
    valueFactory?: (item: T) => string | number | [string, string] | any;

    /**
     * The `itemNavigate` object optionally provides information about navigation
     * when this item is clicked. It can be used to make the item clickable and
     * lead to a different page.
     */
    itemNavigate?: ItemNavigate;

    /**
     * Similar to `itemNavigate`, `itemNavigateFactory` is a function that takes an item of type `T`
     * and returns an `ItemNavigate` object or undefined. Useful for dynamic navigation options.
     */
    itemNavigateFactory?: (item: T) => ItemNavigate | undefined;

    /**
     * The `type` field specifies the data type of the property, which could be
     * 'text', 'date', 'array', or 'boolean'. This is used for rendering the value in a
     * specialized manner.
     */
    type?:
        | 'text'
        | 'date'
        | 'array'
        | 'boolean'
        | 'file'
        | 'library-file'
        | 'ring'
        | 'color';

    /**
     * `nestedProperty` is used when the property value to be displayed is nested within
     * a complex object. This provides the key to access that nested property.
     */
    nestedProperty?: string;

    /**
     * `useBadge` is a boolean flag to indicate if the displayed value should be wrapped in a badge.
     * Badges are generally used to highlight or draw attention to a particular value.
     */
    useBadge?: boolean;

    badgeColor?: (
        item: T
    ) => 'gray' | 'green' | 'yellow' | 'red' | 'blue' | 'orange';

    /**
     * `isCenter` is a boolean flag to indicate if the displayed value should be centered.
     * Useful for stylistic or layout preferences.
     */
    isCenter?: boolean;

    /**
     * `dir` is an optional property to specify the text direction for the displayed value.
     * This is useful for handling different language scripts that may have different writing directions.
     *
     * - 'ltr': Left-to-Right, for languages like English.
     * - 'rtl': Right-to-Left, for languages like Arabic or Hebrew.
     * - 'auto': Automatically determine the direction based on the content.
     */
    dir?: 'ltr' | 'rtl' | 'auto';

    radius?: number;

    stroke?: number;
    isHidden?: boolean;
}
