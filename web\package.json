{"name": "masar-web", "version": "1.159.0", "private": true, "description": "Masar web project", "scripts": {"buildProd": "export NG_ENV='production' && export NODE_OPTIONS='--max_old_space_size=8192' && ng build masar --configuration=production --aot", "buildStaging": "export NG_ENV='staging' && export NODE_OPTIONS='--max_old_space_size=8192' && ng build masar --configuration=staging --aot", "lint": "ng lint", "ng": "ng", "start": "export NG_ENV='development' && ng serve --port=4400", "start:windows": "set NG_ENV=development && ng serve --port=4400"}, "dependencies": {"@angular/animations": "~12.1.1", "@angular/cdk": "^12.2.13", "@angular/common": "~12.1.1", "@angular/compiler": "~12.1.1", "@angular/core": "~12.1.1", "@angular/forms": "~12.1.1", "@angular/platform-browser": "~12.1.1", "@angular/platform-browser-dynamic": "~12.1.1", "@angular/router": "~12.1.1", "@azure/msal-angular": "^2.4.6", "@azure/msal-browser": "^2.31.0", "@microsoft/signalr": "^3.1.7", "@ng-omar/translation": "^12.0.8", "@ng-select/ng-select": "^5.0.1", "@ngx-translate/core": "^13.0.0", "@sweetalert2/ngx-sweetalert2": "^10.0.0", "@tailwindcss/typography": "^0.5.16", "@types/chart.js": "^2.9.24", "@types/marked": "^6.0.0", "angular-circliful": "^0.1.4-beta", "angular-fusioncharts": "^3.1.0", "angular-gauge-chart": "^0.7.2", "animate.css": "^4.1.1", "chart.js": "^2.9.3", "chartjs-plugin-datalabels": "^1.0.0", "chartjs-plugin-labels": "^1.1.0", "d3": "^6.5.0", "date-fns": "^2.29.3", "file-saver": "^2.0.2", "flatpickr": "4.6.6", "gaugeJS": "^1.3.7", "html2canvas": "^1.4.1", "igniteui-angular-core": "^11.1.1", "igniteui-angular-gauges": "^11.1.1", "jquery": "^3.6.0", "jspdf": "^2.5.1", "marked": "^15.0.12", "mnm-frappe-gantt": "^0.6.3", "mnm-webapp": "^3.8.5", "mobile-device-detect": "^0.4.3", "ng-circle-progress": "1.6.0", "ng-zorro-antd": "^12.1.1", "ng2-charts": "^2.4.2", "ngx-color-picker": "^12.0.1", "ngx-date-fns": "8.3", "notyf": "^3.10.0", "rxjs": "~6.5.4", "simple-statistics": "^7.8.2", "sweetalert2": "^10.0.0", "tslib": "^2.0.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.1.1", "@angular-eslint/builder": "12.2.0", "@angular-eslint/eslint-plugin": "12.2.0", "@angular-eslint/eslint-plugin-template": "12.2.0", "@angular-eslint/schematics": "12.2.0", "@angular-eslint/template-parser": "12.2.0", "@angular/cli": "~12.1.1", "@angular/compiler-cli": "~12.1.1", "@angular/language-service": "~12.1.1", "@tailwindcss/forms": "^0.3.3", "@tailwindcss/line-clamp": "^0.4.2", "@types/file-saver": "^2.0.1", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "4.23.0", "@typescript-eslint/parser": "4.23.0", "autoprefixer": "^10.4.12", "codelyzer": "^6.0.0", "eslint": "^7.26.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-prettier": "^3.4.0", "mnm-tailwindcss-rtl": "^0.9.9", "postcss": "^8.4.17", "prettier": "^2.8.4", "prettier-plugin-tailwindcss": "^0.2.2", "protractor": "~7.0.0", "tailwindcss": "^3.1.8", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "4.2.4"}, "engines": {"node": "16.20.0", "npm": ">=7"}}