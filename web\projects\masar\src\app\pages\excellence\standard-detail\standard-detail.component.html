<app-page pageTitle="{{ standard?.tournament.name }}">
    <ng-container content>
        <!-- Standard name -->
        <h2 class="flex flex-row content-start items-center gap-2 text-primary">
            <!-- Tournament logo -->
            <div
                class="h-16 w-16 flex-shrink overflow-hidden rounded-full border-2 border-primary bg-gray-200"
            >
                <app-image
                    *ngIf="standard"
                    [imageObservable]="
                        imageApiService.tournament(standard.tournament.id)
                    "
                    style="height: 100%"
                ></app-image>
            </div>

            <div class="flex flex-grow flex-col gap-2 text-2xl font-bold">
                <ng-container *appWaitUntilLoaded="standard">
                    <!-- Pillar name -->
                    <div>
                        {{ standard.pillar.name }} ({{
                            standard.pillar.weight * 100
                        }}%)
                    </div>

                    <!-- Standard name -->
                    <div class="text-xl">{{ standard.name }}</div>
                </ng-container>
            </div>

            <!-- Principle select -->
            <ng-select
                [items]="standard?.principles || []"
                bindLabel="name"
                bindValue="id"
                style="flex-basis: 200px"
                placeholder="{{ 'translate_select_a_principle' | translate }}"
                (change)="refreshTables($event?.id)"
            >
            </ng-select>
        </h2>

        <hr class="my-5" />

        <!-- Capabilities -->
        <app-capability-linker
            mode="view"
            [listLinkedCapabilitiesCallback]="linkedCapabilitiesCallback()"
            class="mb-5"
        ></app-capability-linker>

        <!-- Kpis -->
        <app-kpi-linker
            mode="view"
            [listLinkedKpisCallback]="linkedKpisCallback()"
            class="mb-5"
        ></app-kpi-linker>

        <!-- Files -->
        <app-library-file-linker
            mode="view"
            [listLinkedFilesCallback]="linkedLibraryFilesCallback()"
            class="mb-5"
        ></app-library-file-linker>
    </ng-container>
</app-page>
