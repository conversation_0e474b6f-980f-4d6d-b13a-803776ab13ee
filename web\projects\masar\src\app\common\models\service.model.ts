import { Department } from './department.model';
import { ServicePartner } from './service-partner.model';
import { ServiceCategory } from './service-category.model';
import { Item } from '@masar/common/models/item.model';
import { Operation } from '@masar/common/models/operation.model';

export interface Service {
    id: string;
    name: string;
    nameDescription: string;
    mainServiceName: string;
    subServiceName: string;
    supplementaryServiceName: string;
    ownership: string;
    categories: ServiceCategory[];
    types: string[];
    description: string;
    limitation: string;
    departments: Department[];
    isThereAPackage: boolean; // eslint-disable-line @typescript-eslint/naming-convention
    package: string;
    deliveryTime: string;
    fees?: { value: number; label: string }[];
    duration: string;
    durationType: string;
    isElectronicallyConvertible: boolean;
    electronicTransformationRatio: string;
    individualServiceInsurance: string;
    businessServiceInsurance: string;
    governmentServiceInsurance: string;
    procedures: string;
    queryStage: string;
    applicationStage: string;
    deliveryStage: string;
    receiptOfTheRequest: string;
    isLinkedWithOtherParties: boolean;
    linkedPartyName: string;
    linkedPartyService: string;
    development: string;
    clientCategories: Item[];
    providerChannels: Item[];
    deliveryChannels: Item[];
    paymentMethods: string[];
    developmentEntrances: string[];
    partners: ServicePartner[];
    developmentEffect: string;
    impactOnQualityOfLife: string;
    postEvaluationScore: number;
    evaluationNotes: string;
    owningDepartment: Department;
    operations: Operation[];
}
