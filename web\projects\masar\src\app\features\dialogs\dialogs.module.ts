import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IframeDialogComponent } from './iframe-dialog/iframe-dialog.component';
import { SharedModule } from '@masar/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
    declarations: [IframeDialogComponent],
    imports: [CommonModule, SharedModule, TranslateModule],
    exports: [IframeDialogComponent],
})
export class DialogsModule {}
