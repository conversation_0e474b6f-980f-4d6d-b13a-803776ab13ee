<app-page
    [pageTitle]="
        (id
            ? 'translate_update_existing_' + label
            : 'translate_add_new_' + label
        ) | translate
    "
>
    <ng-container tools>
        <a
            *ngIf="id && !hidePreviewButton"
            [routerLink]="link | concatArrays : ['detail', id]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-eye me-2"></i>
            <span>{{ 'translate_preview' | translate }}</span>
        </a>

        <!-- Back -->
        <!-- <app-go-back-button></app-go-back-button> -->
        <app-go-back-button
            [label]="labelBack"
            [link]="link"
        ></app-go-back-button>
    </ng-container>

    <!-- Content -->
    <ng-container content>
        <ng-content select="[top]"></ng-content>

        <app-new-form
            [formState]="formState"
            [getForEditCb]="getForEditCb"
            [createCb]="createCb"
            [updateCb]="updateCb"
            [afterSubmitNavigateTo]="afterSubmitNavigateTo"
            [hideNavigateCases]="hideNavigateCases"
            [id]="id"
            [link]="link"
        ></app-new-form>

        <ng-content select="[bottom]"></ng-content>
    </ng-container>
</app-page>
