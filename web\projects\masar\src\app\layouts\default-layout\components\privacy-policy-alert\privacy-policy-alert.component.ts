import { Component, OnDestroy } from '@angular/core';
import { PrivacyPolicyAlertService } from './privacy-policy-alert.service';
@Component({
    selector: 'app-privacy-alert',
    template: `
        <div
            *ngIf="service.isShown()"
            class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-80"
            style="z-index: 9999999"
        >
            <div
                class="relative flex items-center justify-center overflow-hidden rounded-md border border-secondary-400 bg-secondary-100 px-4 py-4 pe-16 text-secondary-700"
                role="alert"
            >
                <p>
                    {{
                        'translate_all_information_displayed_in_the_platform_are_highly_classified_and_should_not_be_shared_externally'
                            | translate
                    }}
                </p>
                <button
                    class="absolute bottom-0 left-0 top-0 flex h-full w-14 items-center justify-center rounded-e-md bg-secondary-50"
                    type="button"
                    (click)="service.setHidden()"
                >
                    <span class="fas fa-times"></span>
                </button>
            </div>
        </div>
    `,
    providers: [PrivacyPolicyAlertService],
})
export class PrivacyPolicyAlertComponent implements OnDestroy {
    public constructor(public service: PrivacyPolicyAlertService) {}

    public ngOnDestroy(): void {
        this.service.reset();
    }
}
