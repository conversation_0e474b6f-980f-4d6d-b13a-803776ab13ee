import {
    Component,
    Input,
    NgModuleRef,
    OnChanges,
    OnInit,
} from '@angular/core';
import { KpiBenchmark } from '@masar/common/models';
import { permissionList } from '@masar/common/constants';
import { finalize } from 'rxjs/operators';
import { KpiBenchmarkService } from '../../kpi-benchmark.service';
import { ModalService, NotificationService } from 'mnm-webapp';
import { KpiBenchmarkNewComponent } from '../kpi-benchmark-new/kpi-benchmark-new.component';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-kpi-benchmark-list',
    templateUrl: 'kpi-benchmark-list.component.html',
})
export class KpiBenchmarkListComponent implements OnInit, OnChanges {
    @Input() public mode: 'default' | 'view' = 'default';
    @Input() public kpiId: string;

    public items: KpiBenchmark[];
    public filteredItems: KpiBenchmark[];
    public years: number[] = [];
    public selectedYear: number;
    public permissionList = permissionList;

    public currentlyDeletingBenchmarks: string[] = [];

    public constructor(
        private kpiBenchmarkService: KpiBenchmarkService,
        private notificationService: NotificationService,
        private translateService: TranslateService,
        private modalService: ModalService,
        private moduleRef: NgModuleRef<any>
    ) {
        // Set current year as default
        this.selectedYear = new Date().getFullYear();
    }

    public ngOnInit(): void {
        this.loadItems();
    }

    public ngOnChanges(): void {
        if (!this.kpiId) return;
        this.loadItems();
    }

    public deleteBenchmark(item: KpiBenchmark): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeletingBenchmarks.push(item.id);
        this.kpiBenchmarkService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeletingBenchmarks =
                        this.currentlyDeletingBenchmarks.filter(
                            x => x !== item.id
                        );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.items = this.items.filter(x => x.id !== item.id);
                this.filterItemsByYear();
            });
    }

    public async showCreateBenchmarkDialog(item: KpiBenchmark): Promise<void> {
        await KpiBenchmarkNewComponent.showAsDialog(
            item,
            this.kpiId,
            this.modalService,
            this.translateService,
            this.moduleRef,
            x => this.addNewBenchmark(x),
            x => this.updateExistingBenchmark(x)
        );
    }

    public addNewBenchmark(item: KpiBenchmark): void {
        this.items = [...this.items, item];
        this.filterItemsByYear();
    }

    public updateExistingBenchmark(item: KpiBenchmark): void {
        const idx = this.items.findIndex(x => x.id === item.id);
        if (idx === -1) {
            return;
        }

        this.items[idx] = item;
        this.filterItemsByYear();
    }

    public filterItemsByYear(): void {
        if (!this.selectedYear) {
            this.filteredItems = this.items;
            return;
        }
        this.filteredItems = this.items.filter(
            item => item.year === this.selectedYear
        );
    }

    public yearChanged(): void {
        this.loadItemsForYear();
    }

    private loadItems(): void {
        if (!this.kpiId) return;

        this.loadBenchmarkYears();
    }

    private loadItemsForYear(): void {
        if (!this.kpiId) return;

        this.kpiBenchmarkService
            .list(this.kpiId, this.selectedYear)
            .subscribe(items => {
                this.items = items;
                this.filteredItems = items;
            });
    }

    private loadBenchmarkYears(): void {
        if (!this.kpiId) return;

        this.kpiBenchmarkService
            .getBenchmarkYears(this.kpiId)
            .subscribe(years => {
                this.years = years;
                // If no years returned and no benchmarks yet, use current year
                if (years.length === 0) {
                    this.years = [this.selectedYear];
                } else if (!this.years.includes(this.selectedYear)) {
                    // If current year not in the list, use the first available year
                    this.selectedYear = years[0];
                }
                this.loadItemsForYear();
            });
    }
}
