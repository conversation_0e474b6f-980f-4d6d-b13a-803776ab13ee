import { StandardTask } from './standard-task.model';
import { User } from './user.model';
import { StandardSubtaskApproval } from './standard-subtask-approval.model';
import { StandardSubtaskComment } from './standard-subtask-comment.model';
import { SubtaskSubmitAction } from '@masar/pages/tournament/types';

export interface StandardSubtask {
    id: string;
    name: string;
    nameAr: string;
    nameEn: string;
    from: Date;
    to: Date;
    progress: number;
    progressDescription: string;
    status: SubtaskSubmitAction;
    task: StandardTask;
    assigned: User[];
    approvals: StandardSubtaskApproval[];
    comments: StandardSubtaskComment[];
    enabledActions: {
        canInitiallySubmit: boolean;
        canInitiallyApprove: boolean;
        canInitiallyReject: boolean;
        canSubmit: boolean;
        canApprove: boolean;
        canReject: boolean;
        canFinalize: boolean;
        canAddDetails: boolean;
        canAssign: boolean;
        canEdit: boolean;
        canDelete: boolean;
    };
}
