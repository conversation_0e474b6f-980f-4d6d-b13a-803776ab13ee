import { Component, Input } from '@angular/core';
import { Pillar, Standard, Tournament } from '@masar/common/models';
import { ImageApiService } from '@masar/core/services';

declare type ItemType = 'tournament' | 'pillar' | 'standard';

@Component({
    selector: 'app-excellence-item',
    templateUrl: './excellence-item.component.html',
})
export class ExcellenceItemComponent {
    @Input() public item: Tournament | Pillar | Standard;
    @Input() public type: ItemType;

    public constructor(public imageApiService: ImageApiService) {}
}
