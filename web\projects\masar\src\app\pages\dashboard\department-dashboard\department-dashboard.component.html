<app-page
    pageTitle="{{
        parent?.name ?? ('translate_departments_stats' | translate)
    }}"
>
    <!-- Tools -->
    <a
        tools
        *ngIf="parent"
        [routerLink]="['', 'dashboard', 'department']"
        [queryParams]="
            parent.parentDepartment
                ? { parentId: parent.parentDepartment.id }
                : {}
        "
        class="btn btn-sm btn-outline-white"
    >
        <i class="fa-light fa-share me-2"></i>
        <span>{{ 'translate_back' | translate }}</span>
    </a>

    <!-- Content -->
    <ng-container content>
        <app-content-loading [isLoading]="!children">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
                <app-department-dashboard-item
                    *ngFor="let item of children"
                    [department]="item"
                ></app-department-dashboard-item>
            </div>
        </app-content-loading>
    </ng-container>
</app-page>
