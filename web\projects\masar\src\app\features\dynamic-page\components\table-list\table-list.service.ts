import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { HttpCrud } from '@masar/features/http-crud/http-crud.class';
import { NotificationService } from 'mnm-webapp';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class TableListService extends HttpCrud implements OnDestroy {
    public constructor(
        notificationService: NotificationService,
        http: HttpClient
    ) {
        super(http, notificationService);
    }

    public init(endPoint: string): void {
        this.endPoint = endPoint;
    }

    public ngOnDestroy(): void {
        this.stop();
    }
}
