import {
    AfterViewInit,
    Component,
    ElementRef,
    Input,
    Renderer2,
    RendererStyleFlags2,
    ViewChild,
    ViewChildren,
} from '@angular/core';
import { flippingCard } from '@masar/common/animations';
import { Kpi } from '@masar/common/models';

@Component({
    selector: 'app-kpi-grid',
    templateUrl: './kpi-grid.component.html',
    styleUrls: ['./kpi-grid.component.scss'],
    animations: [...flippingCard],
})
export class KpiGridComponent implements AfterViewInit {
    @Input() public kpis: Kpi[];
    @Input() public gaugeStyle: 'default' | 'style_1' = 'default';

    // default: displays the grid inside a content container
    // style_1: displays the grid without the container + adds a centered title.
    @Input() public displayStyle: 'default' | 'style_1';

    @ViewChild('cardGrid') private cardGridRef: ElementRef<HTMLDivElement>;
    @ViewChildren('card') private cardRefs: ElementRef<HTMLDivElement>[];

    public constructor(private renderer: Renderer2) {}

    public trackByKpiId(_index: number, item: Kpi): string {
        return item.id;
    }

    public ngAfterViewInit(): void {
        this.cardGridRef?.nativeElement.addEventListener('mousemove', e => {
            this.cardRefs.forEach(cardRef => {
                const card = cardRef.nativeElement;
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                this.renderer.setStyle(
                    card,
                    '--mouse-x',
                    `${x}px`,
                    RendererStyleFlags2.DashCase
                );
                this.renderer.setStyle(
                    card,
                    '--mouse-y',
                    `${y}px`,
                    RendererStyleFlags2.DashCase
                );
            });
        });

        this.cardRefs.forEach(cardRef => {
            const card = cardRef.nativeElement;
            card.addEventListener('mousemove', e => {
                const rect = card.getBoundingClientRect();
                const degX =
                    (e.clientX - rect.left - rect.width / 2) / (rect.width / 2);
                const degY =
                    (e.clientY - rect.top - rect.height / 2) /
                    (rect.height / 2);

                card.style.setProperty('--deg-x', `${degY}deg`);
                card.style.setProperty('--deg-y', `${-degX}deg`);
            });
        });
    }
}
