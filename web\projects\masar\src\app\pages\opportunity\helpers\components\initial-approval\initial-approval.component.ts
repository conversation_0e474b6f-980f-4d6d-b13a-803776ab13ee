import { Component, EventEmitter, Input, Output } from '@angular/core';
import { OpportunityService } from '../../../opportunity.service';
import { finalize } from 'rxjs/operators';
import { NotificationService } from 'mnm-webapp';

@Component({
    selector: 'app-initial-approval',
    templateUrl: './initial-approval.component.html',
})
export class InitialApprovalComponent {
    @Input() public itemId: string;

    @Output() public done = new EventEmitter<boolean>();

    public isSubmitting = false;

    public constructor(
        private opportunityService: OpportunityService,
        private notificationService: NotificationService
    ) {}

    public approve(note: string): void {
        if (this.isSubmitting) return;

        this.isSubmitting = true;
        this.opportunityService
            .initiallyApprove(this.itemId, note)
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.done.next(true);
            });
    }
}
