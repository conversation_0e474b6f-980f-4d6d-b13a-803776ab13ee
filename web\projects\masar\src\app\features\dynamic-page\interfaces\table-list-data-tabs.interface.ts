import { PermissionValue } from '@masar/common/types';
import { ListSectionData } from './list-section-data.interface';

export interface TableListDataTab<D = unknown> {
    label?: string;
    isDone?: boolean;
    progress?: number;
    count?: number;
    permission?: PermissionValue;
    linker?: {
        bodyParameterName: string;
        endPoint: string;
        data: ListSectionData<D>;
        title: string;
    };
}
