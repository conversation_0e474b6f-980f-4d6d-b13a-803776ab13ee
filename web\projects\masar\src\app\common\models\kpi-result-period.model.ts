import { KpiResult } from '@masar/common/models/kpi-result.model';
import { EvaluateItem } from '@masar/features/evaluate/interfaces';

export interface KpiResultPeriod extends EvaluateItem {
    id: string;
    a?: number;
    b?: number;
    target?: number;
    result?: number;
    achieved?: number;
    period?: number;
    resultAnalysis: string;
    attachmentCount: number;
    capabilityCount: number;
    improvementProcedure: string;
    improvementProcedureCompletionPercentage: number;
    improvementProcedureExpectedCompletionDate: Date;
    supervisorNote: string;
    leadershipDirective: string;
    isApproved: boolean;
    kpiResult: KpiResult;
}
