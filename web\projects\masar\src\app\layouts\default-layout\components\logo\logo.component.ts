import {
    AfterViewInit,
    Component,
    Input,
    OnDestroy,
    ViewChildren,
} from '@angular/core';
import { AppSettingFetcherService, SignalrService } from '@masar/core/services';
import { ImageComponent } from '@masar/shared/components';
import { filter, takeUntil } from 'rxjs/operators';
import { messageTypeList } from '@masar/common/misc/message-type-list';
import { Observable, of, Subject } from 'rxjs';
import { convertBlobToBase64 } from '@masar/common/utils';
import { defaultImages } from '@masar/common/constants';

@Component({
    selector: 'app-logo',
    templateUrl: 'logo.component.html',
})
export class LogoComponent implements AfterViewInit, OnDestroy {
    @Input() public isSidebarCollapsed: boolean = false;

    @ViewChildren('appLogo') public appLogos: ImageComponent[];

    public appLogo$?: Observable<Blob>;

    private readonly unsubscribeAll = new Subject();

    public constructor(
        public readonly appSettingFetcherService: AppSettingFetcherService,
        private readonly signalrService: SignalrService
    ) {
        appSettingFetcherService.appLogo().subscribe(async appLogo => {
            const appLogoBase64 = await convertBlobToBase64(appLogo);

            if (!appLogo || defaultImages.appLogo === appLogoBase64) return;

            this.appLogo$ = of(appLogo);
        });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public ngAfterViewInit(): void {
        this.signalrService
            .messages()
            .pipe(
                takeUntil(this.unsubscribeAll),
                filter(x => x.type === messageTypeList.appImageUpdate)
            )
            .subscribe(() => this.appLogos.forEach(x => x.refreshImage()));
    }
}
