<div class="grid gap-2 md:gap-4">
    <ng-container *ngFor="let section of sections">
        <app-content
            *ngIf="!(section.type === 'detail' && section.content.length === 0)"
            class="detail-section-content"
            [ngStyle]="{
                'grid-column': 'span ' + (section.gridSize || 12),
                'width': '100%'
            }"
            [contentTitle]="section.header | translate"
            [noPadding]="true"
        >
            <ng-container content>
                <app-detail-section
                    *ngIf="section.type === 'detail'"
                    [section]="$any(section)"
                    [item]="item"
                >
                </app-detail-section>

                <app-progress-ring-section
                    *ngIf="section.type === 'progress-ring'"
                    [value]="
                        item
                            ? section.propertyName
                                ? item[section.propertyName]
                                : section.valueFactory(item)
                            : 0
                    "
                    [radius]="section.radius"
                    [stroke]="section.stroke"
                ></app-progress-ring-section>

                <app-table-list-basic
                    *ngIf="section.type === 'table-list'"
                    [section]="$any(section)"
                    [items]="item | nestedProperty : section.propertyName"
                ></app-table-list-basic>
            </ng-container>
        </app-content>
    </ng-container>
</div>
