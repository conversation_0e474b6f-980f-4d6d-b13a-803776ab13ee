<app-content contentTitle="{{ 'translate_kpi_results' | translate }}">
    <ng-container content>
        <!-- Filter -->
        <app-filter-result-box>
            <!-- Keyword field -->
            <input
                type="text"
                placeholder="{{ 'translate_search_by_name' | translate }}"
                [(ngModel)]="tableController.filter.data.keyword"
                (keyup)="tableController.filter$.next()"
            />

            <!-- Year field -->
            <ng-select
                [items]="years"
                placeholder="{{ 'translate_year' | translate }}"
                [(ngModel)]="tableController.filter.data.year"
                (change)="tableController.filter$.next(true)"
            >
            </ng-select>
        </app-filter-result-box>

        <app-list-loading [items]="tableController.items">
            <div class="table-responsive mb-5">
                <table>
                    <thead>
                        <tr>
                            <th>{{ 'translate_year' | translate }}</th>
                            <th>{{ 'translate_kpi_name' | translate }}</th>
                            <th>{{ 'translate_target' | translate }}</th>
                            <th>{{ 'translate_result' | translate }}</th>
                            <th>{{ 'translate_achieved' | translate }}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of tableController.items">
                            <td>{{ item.year }}</td>
                            <td>{{ item.kpi.name }}</td>
                            <td>{{ item.target | round : 2 }}</td>
                            <td>{{ item.result | round : 2 }}</td>
                            <td>{{ item.achieved * 100 | round : 2 }}%</td>
                            <td>
                                <button
                                    (click)="selected.emit(item)"
                                    class="btn btn-sm btn-info"
                                >
                                    <i class="fa-light fa-link"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <app-table-pagination
                [tableController]="tableController"
            ></app-table-pagination>
        </app-list-loading>
    </ng-container>
</app-content>
