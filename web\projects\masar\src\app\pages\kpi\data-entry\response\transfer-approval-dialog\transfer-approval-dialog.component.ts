import {
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { MnmFormState } from '@masar/shared/components';
import { FormBuilder } from '@angular/forms';
import { KpiResultDataEntrySharedService } from '@masar/pages/kpi/kpi-result-data-entry-shared.service';
import { Subject } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NotificationService } from 'mnm-webapp';
import { TranslateService } from '@ngx-translate/core';
import { fields } from '@masar/pages/kpi/data-entry/response/transfer-approval-dialog/fields';
import { MiscItem } from '@masar/common/types';

@Component({ templateUrl: './transfer-approval-dialog.component.html' })
export class TransferApprovalDialogComponent implements OnInit, OnDestroy {
    @Input() public kpiResultDataEntryResponseId: string;
    @Input() public currentApprovers: MiscItem[];

    @Output() public transferMove = new EventEmitter<boolean>();

    public moveFormState: MnmFormState;
    public isMoving: boolean = false;

    private unsubscribeAll = new Subject();

    public constructor(
        private fb: FormBuilder,
        private kpiResultDataEntrySharedService: KpiResultDataEntrySharedService,
        private notificationService: NotificationService,
        private translateService: TranslateService
    ) {}

    public ngOnInit(): void {
        this.moveFormState = new MnmFormState(fields(), this.fb);
        this.moveFormState.group.controls['id'].setValue(
            this.kpiResultDataEntryResponseId
        );

        if (this.kpiResultDataEntryResponseId) this.getNext();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public save(): void {
        this.moveFormState.setTriedToSubmit();
        if (this.moveFormState.group.invalid) return;

        this.isMoving = true;

        this.kpiResultDataEntrySharedService
            .transferApproval(this.moveFormState.group.getRawValue())
            .pipe(finalize(() => (this.isMoving = false)))
            .subscribe(_ => {
                this.translateService
                    .get('translate_item_updated_successfully')
                    .subscribe(str => {
                        this.notificationService.notifySuccess(str);
                    });

                this.emitTransferMove(true);
            });
    }

    public getNext(): void {
        this.kpiResultDataEntrySharedService
            .getApprovers(this.kpiResultDataEntryResponseId)
            .subscribe(({ users }) => {
                // exclude current signatories from list
                let idsToExclude: string[] = this.currentApprovers.map(
                    x => x.id
                );

                this.moveFormState.get('users').items = users.filter(
                    x => !idsToExclude.includes(x.id)
                );
            });
    }

    public emitTransferMove(updateTable: boolean): void {
        this.transferMove.emit(updateTable);
    }
}
