import {
    Item,
    Kpi,
    LibraryFile,
    OperationEnhancement,
    OperationProcedure,
    OperationUpdateRequest,
    Service,
} from './index';
import { OperationFormsModel } from './operation-forms.model';

export interface Operation {
    id: string;
    code: string;
    number: number;
    name: string;
    nameAr: string;
    nameEn: string;
    description: string;
    descriptionAr: string;
    descriptionEn: string;
    output: string;
    weight: number;
    level: number;
    sustainabilityImpact: string[];
    purpose: string;
    duration: string;
    dangerNumber: string;
    version: string;

    //4th level
    types?: string[];
    outputs?: string;
    inputType?: string;
    inputs?: string;
    supplierCategory?: string;
    technicalSolutions?: string;
    terminologies: string;
    outputType?: string;
    supplierName?: string;
    mainFlowChart?: LibraryFile;
    beneficiaries?: string[];
    executors?: Item[];
    rulesAndRegulations?: Item[];
    policies?: Item[];
    specifications?: Item[];
    formFiles?: OperationFormsModel[];

    enhancements?: OperationEnhancement[];
    procedures?: OperationProcedure[];
    lastEnhancementDate?: string;

    ownerDepartment?: Item;
    partners?: Item[];
    strategicGoals?: Item[];
    children?: Operation[];
    childCount: number;
    successFactors?: Item[];
    kpis?: Kpi[];
    services: Service[];
    ministerialCode: string;
    localCode: string;
    mainOperationOwner: Item;

    hasPendingUpdateRequest: boolean;
    rejectedUpdateRequest: OperationUpdateRequest;
}
