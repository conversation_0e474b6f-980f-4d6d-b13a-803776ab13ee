import { Component, Input, OnDestroy } from '@angular/core';
import { fromEvent, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DetailSectionData } from '@masar/features/dynamic-page/interfaces';

@Component({
    selector: 'app-detail-section',
    templateUrl: './detail-section.component.html',
    styles: [
        `
            tr:nth-child(even) {
                @apply bg-gray-100;
            }
        `,
    ],
})
export class DetailSectionComponent implements OnDestroy {
    @Input() public section!: DetailSectionData<unknown>;

    @Input() public item?: unknown;

    public twoItemsInOneRow = false;

    private unsubscribeAll = new Subject();

    public constructor() {
        this.twoItemsInOneRow = window.innerWidth > 1200;

        fromEvent(window, 'resize')
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(
                () => (this.twoItemsInOneRow = window.innerWidth > 1200)
            );
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }
}
