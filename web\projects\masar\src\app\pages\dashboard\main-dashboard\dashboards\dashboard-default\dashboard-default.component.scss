.double-border-circle-outer {
    @apply flex  items-center justify-center rounded-full bg-secondary-300 transition-colors;
}

.double-border-circle-inner {
    @apply flex items-center justify-center rounded-full bg-secondary-200 text-white;
}

.strategic-goal-performance-card {
    @apply relative h-52 w-[437px] cursor-pointer rounded bg-primary-800 hover:no-underline;

    transform: perspective(100px) rotateY(0) rotateX(0);
    transition: transform 150ms;
}

.strategic-goal-performance-card:hover {
    transform: perspective(100px) rotateY(var(--deg-y)) rotateX(var(--deg-x));
}

.strategic-goal-performance-card::after,
.strategic-goal-performance-card::before {
    @apply absolute left-0 top-0 h-full opacity-0;

    border-radius: inherit;
    content: '';
    transition: opacity 500ms;
}

.strategic-goal-performance-card::before {
    z-index: 1;
    background: radial-gradient(
        800px circle at var(--mouse-x) var(--mouse-y),
        rgb(255 255 255 / 30%),
        transparent 40%
    );
}

.strategic-goal-performance-card::after {
    z-index: 3;
    background: radial-gradient(
        800px circle at var(--mouse-x) var(--mouse-y),
        rgb(255 255 255 / 10%),
        transparent 40%
    );
}

.strategic-goal-performance-card:hover::after,
.strategic-goal-performance-card:hover::before {
    @apply opacity-100;
}

.strategic-goal-performance-card-content {
    @apply relative flex flex-col items-center justify-center gap-2 bg-primary-500 p-6 font-bold text-white hover:text-white;

    z-index: 2;
    height: calc(100% - 4px);
    border-radius: inherit;
    margin: 2px;
}
