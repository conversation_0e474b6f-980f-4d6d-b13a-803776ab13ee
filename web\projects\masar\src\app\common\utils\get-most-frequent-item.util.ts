export const getMostFrequentItem = <T>(array: T[]): T | undefined => {
    if (array.length === 0) {
        return undefined;
    }

    let frequencyMap = new Map<T, number>();
    let mostFrequentItem: T = array[0];
    let maxFrequency = 1;

    for (const item of array) {
        let count = (frequencyMap.get(item) || 0) + 1;
        frequencyMap.set(item, count);

        if (count > maxFrequency) {
            maxFrequency = count;
            mostFrequentItem = item;
        }
    }

    return mostFrequentItem;
};
