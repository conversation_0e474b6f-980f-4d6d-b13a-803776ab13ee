import { Department } from './department.model';
import { KpiResultPeriod } from './kpi-result-period.model';
import { Kpi } from './kpi.model';
import { KpiMeasurementCycleType } from '../types';
import { KpiResultTargetSettingMethod } from '@masar/common/models/kpi-result-target-setting-method.model';
import { EvaluateItem } from '@masar/features/evaluate/interfaces';

export interface KpiResult {
    id: string;
    kpi: Kpi;
    department: Department;
    year: number;
    units:
        | 'percentage'
        | 'number'
        | 'average'
        | 'rate'
        | 'currency'
        | 'time'
        | 'minute'
        | 'hour';
    unitsDescription: string;
    measurementCycle: KpiMeasurementCycleType;
    measurementMethod: 'repeat' | 'split' | 'last';
    targetSettingMethod: KpiResultTargetSettingMethod;
    targetSettingMethodTarget: string;
    targetSettingMethodEntity: string;
    target: number;
    targetZero: number;
    result: number;
    currentPeriod: number;
    achieved: number;
    aggregateA: number;
    aggregateB: number;
    periods: KpiResultPeriod[];
    aggregationTypeA: 'sum' | 'last';
    aggregationTypeB: 'sum' | 'last';

    // aValues: string;
    // bValues: string;
    // rValues: string;
    // tValues: string;
    // a: number[];
    // b: number[];
    // r: number[];
    // t: number[];
    capabilityCount: number;
    libraryFileCount: number;
    formulaDescriptionA: string;
    formulaDescriptionB: string;
    formula: string;

    // isApproved: boolean;
    // supervisorNote: string;
    // leadershipDirective: string;

    isOwningDepartment: boolean;
    shouldRespectOwning: boolean;
    canChangeInputMode: boolean;

    // Describes if the user is involved with the kpi/result either
    // by department, or by the virtue of having full access.
    isUserInvolved: boolean;

    // for creation/update
    kpiId: string;
    totalPeriodsEvaluationScore: EvaluateItem['evaluationScoreDetail'];
    decimalPlaces: number;
}
