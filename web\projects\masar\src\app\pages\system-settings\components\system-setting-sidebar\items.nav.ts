import { permissionList } from '@masar/common/constants';
import { NavItem } from '@masar/shared/components/sidebar/types';
import { isOrigin } from '@masar/common/utils';
import { OrganizationOrigin } from '@masar/common/enums';

export const settingNavItems: NavItem[] = [
    {
        type: 'item',
        title: 'translate_hierarchical_structure',
        permissionIdList: [permissionList.departmentRead],
        link: ['', 'department'],
        icon: 'fa fa-sitemap',
    },
    {
        type: 'item',
        title: 'translate_system_users',
        permissionIdList: [permissionList.userRead],
        link: ['', 'user'],
        icon: 'fa fa-users',
    },
    {
        type: 'item',
        title: 'translate_new_user_requests',
        tag: 'newUserRequests',
        permissionIdList: [permissionList.user],
        link: ['', 'new-user-request'],
        icon: 'fa fa-hand-point-up',
    },
    {
        type: 'item',
        title: 'translate_app_settings',
        permissionIdList: [permissionList.fullAccess],
        link: ['', 'setting', 'app-setting'],
        icon: 'fa fa-gear',
    },
    {
        type: 'item',
        title: 'translate_permission_groups',
        permissionIdList: [permissionList.fullAccess],
        link: ['', 'setting', 'permission-group'],
        icon: 'fa fa-lock',
    },
    {
        type: 'item',
        title: 'translate_linked_applications',
        permissionIdList: [permissionList.fullAccess],
        link: ['', 'setting', 'linked-application'],
        icon: 'fa fa-link',
    },
    {
        type: 'item',
        title: 'translate_teams',
        permissionIdList: [permissionList.team],
        link: ['', 'team'],
        icon: 'fa fa-user-group',
    },
    {
        type: 'item',
        title: 'translate_languages',
        permissionIdList: [permissionList.fullAccess],
        link: ['', 'setting', 'language'],
        icon: 'fa fa-globe',
        isShownOnlyInSafeMode: true,
    },
    {
        type: 'item',
        title: 'translate_translations',
        permissionIdList: [permissionList.fullAccess],
        link: ['', 'setting', 'translation-string'],
        icon: 'fa fa-language',
    },
    {
        type: 'item',
        title: 'translate_changes_record',
        permissionIdList: [permissionList.fullAccess],
        link: ['', 'setting', 'system-event'],
        icon: 'fa fa-bolt',
    },

    {
        type: 'section',
        title: 'translate_strategic_plans',
        tag: 'strategicPlans',
        children: [
            {
                type: 'item',
                title: 'translate_manage_strategic_plans',
                link: ['', 'system-list', 'strategic-plans'],
                permissionIdList: [permissionList.fullAccess],
            },
            {
                type: 'item',
                title: 'translate_manage_strategic_pillars',
                link: [
                    '',
                    'system-list',
                    'strategic-plans',
                    'strategic-pillar',
                ],
                permissionIdList: [permissionList.fullAccess],
            },
        ],
    },
    {
        type: 'section',
        title: 'translate_operations',
        children: [
            {
                type: 'item',
                title: 'translate_operations_rules_and_regulations',
                link: ['', 'system-list', 'operation-rule-and-regulation'],
                permissionIdList: [permissionList.operationRuleAndRegulation],
            },
            {
                type: 'item',
                title: 'translate_operations_enhancements_types',
                link: ['', 'system-list', 'operation-enhancement-type'],
                permissionIdList: [permissionList.operationEnhancementType],
            },
            {
                type: 'item',
                title: 'translate_iso_specifications',
                link: ['', 'system-list', 'operation-specification'],
                permissionIdList: [permissionList.operationSpecification],
            },
            {
                type: 'item',
                title: 'translate_main_operation_owners_list',
                link: ['', 'system-list', 'main-operation-owner'],
                permissionIdList: [permissionList.operation],
            },
        ],
    },
    {
        type: 'section',
        title: 'translate_opportunities',
        children: [
            {
                type: 'item',
                title: 'translate_improvement_opportunity_inputs_sources',
                link: [
                    '',
                    'system-list',
                    'improvement-opportunity-input-source',
                ],
                permissionIdList: [
                    permissionList.improvementOpportunityInputSource,
                ],
            },
            {
                type: 'item',
                title: 'translate_improvement_opportunity_inputs_categories',
                link: [
                    '',
                    'system-list',
                    'improvement-opportunity-input-category',
                ],
                permissionIdList: [
                    permissionList.improvementOpportunityInputCategory,
                ],
            },
        ],
    },
    {
        type: 'section',
        title: 'translate_kpis',
        children: [
            {
                type: 'item',
                title: 'translate_kpi_result_capability_types',
                permissionIdList: [permissionList.kpiResultCapabilityType],
                link: ['', 'system-list', 'kpi-result-capability-type'],
            },
            {
                type: 'item',
                title: 'translate_kpi_types',
                permissionIdList: [permissionList.kpiType],
                link: ['', 'system-list', 'kpi-type'],
            },
            {
                type: 'item',
                title: 'translate_kpi_categories',
                permissionIdList: [permissionList.kpiTag],
                link: ['', 'system-list', 'kpi-tag'],
            },
            {
                type: 'item',
                title: 'translate_kpi_result_categories',
                permissionIdList: [permissionList.kpiResultCategory],
                link: ['', 'system-list', 'kpi-result-category'],
            },
            {
                type: 'item',
                title: 'translate_kpi_result_subcategories',
                permissionIdList: [permissionList.kpiResultCategory],
                link: ['', 'system-list', 'kpi-result-subcategory'],
            },
            {
                type: 'item',
                title: 'translate_target_setting_methods',
                permissionIdList: [permissionList.kpiResultTargetSettingMethod],
                link: ['', 'system-list', 'kpi-result-target-setting-method'],
            },

            {
                type: 'item',
                title: 'translate_balanced_behavior_cards',
                permissionIdList: [permissionList.kpiBalancedBehaviorCard],
                link: ['', 'system-list', 'kpi-balanced-behavior-card'],
            },
        ],
    },
    {
        type: 'section',
        title: 'translate_operational_plans',
        tag: 'plans',
        children: [
            {
                type: 'item',
                title: 'translate_plan_inputs',
                permissionIdList: [permissionList.planInput],
                link: ['', 'system-list', 'plan-input'],
            },
            {
                type: 'item',
                title: 'translate_plan_categories',
                tag: 'planCategories',
                permissionIdList: [permissionList.planCategory],
                link: ['', 'system-list', 'plan-category'],
            },
            {
                type: 'item',
                title: 'translate_plan_task_categories',
                tag: 'planTaskCategories',
                permissionIdList: [permissionList.planTaskCategory],
                link: ['', 'system-list', 'plan-task-category'],
            },
            {
                type: 'item',
                title: 'translate_plan_task_stakeholder',
                tag: 'planTaskStakeholder',
                permissionIdList: [permissionList.plan],
                link: ['', 'system-list', 'plan-task-stakeholder'],
            },
            {
                type: 'item',
                title: 'translate_plan_future_plans',
                permissionIdList: [permissionList.plan],
                link: ['', 'system-list', 'plan-future-plan'],
            },
            {
                type: 'item',
                title: 'translate_plan_resources_classification',
                tag: 'planResourcesClassification',
                permissionIdList: [permissionList.plan],
                link: ['', 'system-list', 'plan-resources-classification'],
            },
            {
                type: 'item',
                title: 'translate_resources_list',
                tag: 'planClassifiedResources',
                permissionIdList: [permissionList.plan],
                link: ['', 'system-list', 'plan-classified-resources'],
            },
        ],
    },
    {
        type: 'section',
        title: 'translate_partners',
        children: [
            {
                type: 'item',
                title: 'translate_partner_evaluation_standards',
                permissionIdList: [permissionList.partner],
                link: ['', 'system-list', 'partner-evaluation-standard'],
            },
            {
                type: 'item',
                title: 'translate_partner_standards',
                permissionIdList: [permissionList.partner],
                link: ['', 'system-list', 'partner-standard'],
            },
        ],
    },
    {
        type: 'section',
        title: 'translate_partnerships',
        children: [
            {
                type: 'item',
                title: 'translate_partnership_types',
                permissionIdList: [permissionList.partnership],
                link: ['', 'system-list', 'partnership-type'],
            },
            {
                type: 'item',
                title: 'translate_partnership_fields',
                permissionIdList: [permissionList.partnership],
                link: ['', 'system-list', 'partnership-field'],
            },
            {
                type: 'item',
                title: 'translate_partnership_scopes',
                permissionIdList: [permissionList.partnership],
                link: ['', 'system-list', 'partnership-scope'],
            },
            {
                type: 'item',
                title: 'translate_partnership_frameworks',
                permissionIdList: [permissionList.partnership],
                link: ['', 'system-list', 'partnership-framework'],
            },
            {
                type: 'item',
                title: 'translate_communication_tools',
                permissionIdList: [permissionList.partnership],
                link: [
                    '',
                    'system-list',
                    'partnership-activity-communication-tool',
                ],
            },
        ],
    },
    {
        type: 'section',
        title: 'translate_strategic_goals',
        tag: 'strategicGoal',
        children: [
            {
                type: 'item',
                title: 'translate_strategic_goals',
                link: ['', 'system-list', 'strategic-goal'],
                permissionIdList: [permissionList.strategicGoal],
            },
            {
                type: 'item',
                title: 'translate_government_strategic_goals',
                link: ['', 'system-list', 'government-strategic-goal'],
                permissionIdList: [permissionList.governmentStrategicGoal],
            },
            {
                type: 'item',
                title: 'translate_ministry_strategic_goals',
                tag: 'ministryStrategicGoal',
                permissionIdList: [permissionList.fullAccess],
                link: ['', 'system-list', 'ministry-strategic-goal'],
            },
        ],
    },
    {
        type: 'section',
        title: 'translate_risk_management',
        children: [
            {
                type: 'item',
                title: 'translate_risk_categories',
                link: ['', 'system-list', 'risk-category'],
                permissionIdList: [permissionList.risk],
            },
            {
                type: 'item',
                title: 'translate_risk_management_strategies',
                link: ['', 'system-list', 'risk-management-strategy'],
                permissionIdList: [permissionList.risk],
            },
            {
                type: 'item',
                title: 'translate_risk_probabilities',
                link: ['', 'system-list', 'risk-probability'],
                permissionIdList: [permissionList.risk],
            },
            {
                type: 'item',
                title: 'translate_risk_impacts',
                link: ['', 'system-list', 'risk-impact'],
                permissionIdList: [permissionList.risk],
            },

            {
                type: 'item',
                title: 'translate_risk_acceptance_level_categories',
                link: ['', 'system-list', 'risk-acceptance-level-category'],
                permissionIdList: [permissionList.risk],
            },
        ],
        isHidden: isOrigin([
            OrganizationOrigin.injaz,
            OrganizationOrigin.police,
        ]),
    },

    {
        type: 'section',
        title: 'translate_services',
        children: [
            {
                type: 'item',
                title: 'translate_service_categories',
                permissionIdList: [permissionList.serviceCategory],
                link: ['', 'system-list', 'service-category'],
            },
            {
                type: 'item',
                title: 'translate_service_provider_channels',
                link: ['', 'system-list', 'service-provider-channel'],
                permissionIdList: [permissionList.service],
            },
            {
                type: 'item',
                title: 'translate_client_categories',
                link: ['', 'system-list', 'service-client-category'],
                permissionIdList: [permissionList.service],
            },
            {
                type: 'item',
                title: 'translate_service_delivery_channels',
                link: ['', 'system-list', 'service-delivery-channel'],
                permissionIdList: [permissionList.service],
            },
        ],
    },

    {
        type: 'section',
        title: 'translate_general',
        children: [
            {
                type: 'item',
                title: 'translate_policies',
                link: ['', 'system-list', 'policy'],
                permissionIdList: [permissionList.policy],
            },
            {
                type: 'item',
                title: 'translate_success_factors',
                link: ['', 'system-list', 'success-factor'],
                permissionIdList: [permissionList.successFactor],
            },
            {
                type: 'item',
                title: 'translate_capability_types',
                link: ['', 'system-list', 'capability-type'],
                permissionIdList: [permissionList.capabilityType],
            },
            {
                type: 'item',
                title: 'translate_national_agenda',
                permissionIdList: [permissionList.partner],
                link: ['', 'system-list', 'national-agenda'],
            },
            {
                type: 'item',
                title: 'translate_evaluations',
                permissionIdList: [permissionList.fullAccess],
                link: ['', 'evaluation'],
            },
        ],
    },
];
