<div
    *ngIf="selection"
    (mouseup)="clear()"
    class="fixed start-0 top-0 z-30 h-full w-full bg-black bg-opacity-20"
>
    <!-- Dialog-->
    <div
        (mouseup)="$event.stopPropagation()"
        class="relative top-16 mx-auto w-10/12 rounded bg-white p-6 shadow-lg md:w-6/12 lg:w-4/12"
    >
        <!-- Title -->
        <div class="mb-4 flex items-center">
            <div
                class="mr-3 flex h-10 w-10 items-center justify-center rounded-full"
                [ngClass]="{
                    'bg-green-600': selection.type === 'vision',
                    'bg-gray-600': selection.type === 'mission'
                }"
            >
                <i
                    class="text-lg text-white"
                    [ngClass]="{
                        'fa-light fa-eye': selection.type === 'vision',
                        'fa-light fa-target': selection.type === 'mission'
                    }"
                ></i>
            </div>
            <h1 class="text-xl font-bold text-gray-800">
                {{ selection.title }}
            </h1>
        </div>

        <!-- Content -->
        <div class="leading-relaxed text-gray-700">
            <p>{{ selection.content }}</p>
        </div>

        <!-- Close Button -->
        <div class="mt-6 text-right">
            <button
                (click)="clear()"
                class="rounded bg-gray-500 px-4 py-2 text-white transition-colors hover:bg-gray-600"
            >
                Close
            </button>
        </div>
    </div>
</div>
