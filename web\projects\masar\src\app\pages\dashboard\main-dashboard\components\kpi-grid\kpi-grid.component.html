<ng-container [ngSwitch]="displayStyle">
    <!-- Style 1 -->
    <div *ngSwitchCase="'style_1'" class="flex flex-col items-center gap-4">
        <h2 class="text-3xl font-bold text-primary">
            {{ 'translate_primary_kpis' | translate }}
        </h2>
        <ng-container [ngTemplateOutlet]="gridTemplate"></ng-container>
    </div>

    <!-- Default -->
    <ng-container *ngSwitchDefault>
        <app-content
            *ngIf="kpis?.length"
            [contentTitle]="'translate_primary_kpis' | translate"
        >
            <ng-container
                content
                [ngTemplateOutlet]="gridTemplate"
            ></ng-container>
        </app-content>
    </ng-container>
</ng-container>

<ng-template #gridTemplate>
    <div
        content
        #cardGrid
        class="cards grid w-full grid-cols-1 gap-2 md:grid-cols-3"
        [@flippingCardGrid]="kpis.length"
    >
        <!-- Card -->
        <a
            [routerLink]="['', 'kpi', 'detail', item.id]"
            #card
            *ngFor="let item of kpis; trackBy: trackByKpiId"
            class="card"
            [@flippingCard]
        >
            <div class="card-content px-4 py-8">
                <!-- Gauge -->
                <app-achievement-gauge
                    [radius]="150"
                    [value]="item.achieved"
                    [gaugeStyle]="gaugeStyle"
                    appearance="light"
                ></app-achievement-gauge>

                <!-- Name -->
                <h3 class="text-center">
                    {{ item.name }}
                </h3>
            </div>
        </a>
    </div>
</ng-template>
