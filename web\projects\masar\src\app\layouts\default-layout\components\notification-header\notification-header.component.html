<header
    class="background relative z-30 w-full p-2 pb-0 ps-2"
    *ngIf="this.notifications.length > 0"
>
    <div
        class="flex w-full items-center justify-between rounded-md border bg-[#ededb0] px-4 py-2 shadow-lg"
        style="height: 50px"
    >
        <div class="flex-grow md:block">
            <div class="marquee-container">
                <div class="marquee__content" [ngStyle]="marqueeStyle">
                    <ul class="flex">
                        <li
                            *ngFor="
                                let notification of notifications;
                                let index = index
                            "
                            class="me-2 border-e border-gray-400 pe-2 last-of-type:border-e-0"
                        >
                            <button
                                (click)="navigateTo(notification)"
                                class="cursor-pointer text-sm font-bold text-gray-600 hover:text-primary hover:underline"
                            >
                                {{ notification.title }}
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- View All Notifications -->
        <button
            [routerLink]="['', 'my-notification']"
            class="btn btn-sm btn-primary"
        >
            {{ 'translate_show_all' | translate }}
        </button>
    </div>
</header>
