import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InnovatorComponent } from './innovator.component';
import { ListComponent } from './list/list.component';
import { NewComponent } from './new/new.component';
import { DetailComponent } from './detail/detail.component';
import { InnovatorService } from './innovator.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { MasarModule } from '@masar/features/masar/masar.module';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InnovatorRoutingModule } from './innovator.routing';
import { SharedModule } from '@masar/shared/shared.module';
import { AwardListComponent } from './detail/award-list/award-list.component';
import { AwardNewComponent } from './detail/award-new/award-new.component';
import { TrainingProgramListComponent } from './detail/training-program-list/training-program-list.component';
import { TrainingProgramNewComponent } from './detail/training-program-new/training-program-new.component';
import { TrainingProgramService } from './training-program.service';
import { InnovatorOverviewComponent } from './detail/innovator-overview/innovator-overview.component';
import { AwardService } from './award.service';
import { ActivityService } from './activity.service';
import { TranslationModule } from '@ng-omar/translation';

@NgModule({
    declarations: [
        InnovatorComponent,
        ListComponent,
        NewComponent,
        DetailComponent,
        AwardListComponent,
        AwardNewComponent,
        TrainingProgramListComponent,
        TrainingProgramNewComponent,
        InnovatorOverviewComponent,
    ],
    imports: [
        CommonModule,
        SharedModule,
        InnovatorRoutingModule,
        TranslationModule,
        FormsModule,
        ReactiveFormsModule,
        SweetAlert2Module,
        MasarModule,
        NgSelectModule,
    ],
    providers: [
        InnovatorService,
        TrainingProgramService,
        AwardService,
        ActivityService,
    ],
})
export class InnovatorModule {}
