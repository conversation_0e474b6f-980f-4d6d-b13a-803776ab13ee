import { Injectable } from '@angular/core';
import { StrategicGoalSetting } from '@masar/common/models/strategic-goal-setting';
import { environment } from '@masar/env/environment';
import { HttpClient } from '@angular/common/http';
import { Result, miscFunctions } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class StrategicGoalService {
    public constructor(private httpClient: HttpClient) {}

    public get(): Observable<StrategicGoalSetting> {
        return this.httpClient
            .get<Result<StrategicGoalSetting>>(
                `${environment.apiUrl}/setting/strategic-goal`
            )
            .pipe(map(res => res.extra));
    }

    public update(
        strategicGoalSetting: StrategicGoalSetting
    ): Observable<StrategicGoalSetting> {
        return this.httpClient
            .put<Result<StrategicGoalSetting>>(
                `${environment.apiUrl}/setting/strategic-goal`,
                miscFunctions.objectToURLParams({
                    strategicGoalSetting: JSON.stringify({
                        isOverallPerformanceCalculationEnabled:
                            strategicGoalSetting.isOverallPerformanceCalculationEnabled,
                    }),
                })
            )
            .pipe(map(res => res.extra));
    }
}
