import { Component, NgModuleRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { finalize, first, takeUntil } from 'rxjs/operators';
import { InnovatorService } from '../innovator.service';
import {
    Innovator,
    TrainingProgram,
    Award,
    Innovation,
} from '@masar/common/models';
import { ImageApiService } from '@masar/core/services';
import { Subject } from 'rxjs';
import { ModalService, NotificationService } from 'mnm-webapp';
import { TrainingProgramNewComponent } from './training-program-new/training-program-new.component';
import { TranslateService } from '@ngx-translate/core';
import { AwardNewComponent } from './award-new/award-new.component';
import { permissionList } from '@masar/common/constants';
import { SharedInnovationService } from '@masar/shared/services/shared-innovation.service';

@Component({
    templateUrl: './detail.component.html',
})
export class DetailComponent {
    public readonly permissionList = permissionList;

    public innovator: Innovator;
    public innovatorId: string;
    public isInnovator: boolean;

    public isTogglingLogo: boolean = false;
    public currentlyDeletingInnovations: string[] = [];

    public constructor(
        public imageApiService: ImageApiService,
        private innovatorService: InnovatorService,
        private activatedRoute: ActivatedRoute,
        private modalService: ModalService,
        private moduleRef: NgModuleRef<any>,
        private translateService: TranslateService,
        private notificationService: NotificationService,
        private innovationService: SharedInnovationService
    ) {
        this.activatedRoute.params.pipe(first()).subscribe(params => {
            {
                this.innovatorId = params.id;

                if (this.innovatorId) {
                    this.getInnovator();
                    return;
                }

                this.innovatorService
                    .currentInnovator()
                    .subscribe(innovator => {
                        if (!innovator) {
                            this.isInnovator = false;
                            return;
                        }

                        this.innovatorId = (innovator as any).id;
                        this.getInnovator();
                    });
            }
        });
    }

    public getInnovator(): void {
        this.innovatorService.get(this.innovatorId).subscribe(item => {
            this.innovator = item;
        });
    }

    public async showTrainingProgramDialog(
        item?: TrainingProgram
    ): Promise<void> {
        const subject = new Subject();
        const title = !item
            ? 'translate_add_new_training_program'
            : 'translate_update_existing_training_program';

        const component = await this.modalService.show(
            TrainingProgramNewComponent,
            {
                title: this.translateService.instant(title),
                moduleRef: this.moduleRef,
                onDismiss: () => {
                    subject.next();
                    subject.complete();
                },
                beforeInit: c => {
                    c.innovatorId = this.innovator.id;
                    if (item) c.item = item;
                },
            }
        );

        component.trainingProgramCreated
            .pipe(takeUntil(subject))
            .subscribe(_ => {
                this.getInnovator();
                this.modalService.dismiss(component);
            });

        component.trainingProgramUpdated
            .pipe(takeUntil(subject))
            .subscribe(_ => {
                this.getInnovator();
                this.modalService.dismiss(component);
            });
    }

    public async showAwardDialog(item?: Award): Promise<void> {
        const subject = new Subject();
        const title = !item
            ? 'translate_add_new_award'
            : 'translate_update_existing_award';

        const component = await this.modalService.show(AwardNewComponent, {
            title: this.translateService.instant(title),
            moduleRef: this.moduleRef,
            onDismiss: () => {
                subject.next();
                subject.complete();
            },
            beforeInit: c => {
                c.innovatorId = this.innovator.id;
                if (item) c.item = item;
            },
        });

        component.awardCreated.pipe(takeUntil(subject)).subscribe(_ => {
            this.getInnovator();
            this.modalService.dismiss(component);
        });

        component.awardUpdated.pipe(takeUntil(subject)).subscribe(_ => {
            this.getInnovator();
            this.modalService.dismiss(component);
        });
    }

    public toggleLogo(item: Innovator): void {
        // add the id of the item to the being toggled array
        // to disable the toggle button in the list.
        this.isTogglingLogo = true;
        this.innovatorService
            .toggleLogo(item.id)
            .pipe(
                finalize(() => {
                    // remove the toggled item id from the being toggled
                    // list when the deletion is complete.
                    this.isTogglingLogo = false;
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.getInnovator();
            });
    }

    public deleteInnovation(item: Innovation): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeletingInnovations.push(item.id);
        this.innovationService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeletingInnovations =
                        this.currentlyDeletingInnovations.filter(
                            x => x !== item.id
                        );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.getInnovator();
            });
    }
}
