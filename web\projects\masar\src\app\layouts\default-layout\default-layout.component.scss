.custom-shadow {
    box-shadow: rgb(50 50 93 / 25%) 0 2px 5px -1px,
        rgb(0 0 0 / 30%) 0 1px 3px -1px;
    transition: box-shadow 250ms ease-in-out;
}

/* width */
::-webkit-scrollbar {
    width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
    background: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background: var(--secondary-500);
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #808080;
}

aside {
    width: 300px;
    min-width: 300px;
    transition: width 200ms ease-in-out;
}

aside.sidebar-collapse {
    width: 80px;
    min-width: 80px;

    .link-notification {
        position: absolute;
        top: 5px;
        right: -6px;
        display: flex;
        width: 20px;
        height: 20px;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
    }

    .sidebar-item a,
    .sidebar-item-child a {
        padding: 15px;
        margin-top: 10px !important;
        margin-bottom: 10px !important;
    }
}
