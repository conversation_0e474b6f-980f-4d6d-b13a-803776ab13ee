import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },

    {
        fields: [
            {
                name: 'employeeNumber',
                type: 'text',
                label: 'translate_employee_number',
                size: 6,
                validators: [Validators.required],
            },
            {
                name: 'rank',
                type: 'text',
                label: 'translate_rank',
                size: 6,
            },
        ],
    },
    {
        fields: [
            {
                name: 'nameAr',
                type: 'text',
                label: 'translate_employee_name_in_arabic',
                size: 6,
                validators: [Validators.required],
            },
            {
                name: 'nameEn',
                type: 'text',
                label: 'translate_employee_name_in_english',
                size: 6,
            },
        ],
    },
    {
        fields: [
            {
                name: 'hasALogo',
                type: 'checkbox',
                label: 'translate_enable_innovation_logo',
                size: 6,
                defaultValue: false,
            },
        ],
    },
];
