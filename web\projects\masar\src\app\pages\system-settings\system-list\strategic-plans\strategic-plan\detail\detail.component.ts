import {
    Component,
    OnInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    OnDestroy,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { StrategicPlan } from '@masar/common/models';
import { StrategicPlanService } from '../services/strategic-plan.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-detail',
    templateUrl: './detail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailComponent implements OnInit, OnDestroy {
    public item?: StrategicPlan;
    public id: string;

    // Memoized badge class cache
    private readonly badgeClassCache = new Map<string, string>();
    private readonly weightCache = new Map<number, string>();
    private readonly destroy$ = new Subject<void>();

    public constructor(
        private readonly strategicPlanService: StrategicPlanService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly cdr: ChangeDetectorRef
    ) {}

    public ngOnInit(): void {
        this.id = this.activatedRoute.snapshot.params['id'];
        if (this.id) {
            this.loadItem();
        }
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.clearCaches();
    }

    /**
     * TrackBy function for strategic pillars to optimize *ngFor performance
     */
    public trackByPillarId(index: number, pillar: any): string {
        return pillar?.id || index;
    }

    /**
     * TrackBy function for strategic goals to optimize *ngFor performance
     */
    public trackByGoalId(index: number, goal: any): string {
        return goal?.id || index;
    }

    /**
     * Optimized badge class calculation with memoization
     */
    public getBadgeClass(category: string): string {
        if (!this.badgeClassCache.has(category)) {
            const badgeClass = `badge badge-${
                category === 'support' ? 'info' : 'primary'
            }`;
            this.badgeClassCache.set(category, badgeClass);
        }
        return this.badgeClassCache.get(category)!;
    }

    /**
     * Optimized weight display calculation with memoization
     */
    public getFormattedWeight(weight: number): string {
        if (weight == null) {
            return '-';
        }

        if (!this.weightCache.has(weight)) {
            const formatted = `${Math.round(weight * 100 * 100) / 100}%`;
            this.weightCache.set(weight, formatted);
        }
        return this.weightCache.get(weight)!;
    }

    /**
     * Check if strategic values exist to avoid unnecessary DOM operations
     */
    public hasStrategicValues(): boolean {
        return !!this.item?.values?.length;
    }

    private loadItem(): void {
        this.strategicPlanService
            .get(this.id, false)
            .pipe(takeUntil(this.destroy$))
            .subscribe(item => {
                this.item = item;
                this.clearCaches();
                this.cdr.markForCheck(); // Trigger change detection
            });
    }

    private clearCaches(): void {
        this.badgeClassCache.clear();
        this.weightCache.clear();
    }
}
