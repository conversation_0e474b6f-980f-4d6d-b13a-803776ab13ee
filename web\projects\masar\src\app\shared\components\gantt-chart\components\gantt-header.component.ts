import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { MonthHeader } from '../services';

@Component({
    selector: 'app-gantt-header',
    // cspell:disable
    template: `
        <div class="flex w-full border-b" role="row" aria-rowindex="1">
            <div
                class="flex w-1/3 min-w-[300px] flex-col items-center justify-center border-l border-r p-3"
                role="columnheader"
                [attr.aria-label]="getTimelineAriaLabel()"
            >
                <div
                    class="flex items-center justify-center gap-3"
                    *ngIf="title"
                >
                    <h2
                        class="text-lg font-bold"
                        [id]="'gantt-title-' + titleId"
                    >
                        {{ title }}
                    </h2>
                    <span
                        *ngIf="progress !== undefined && progress !== null"
                        class="inline-flex items-center rounded bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
                        [attr.aria-label]="getProgressAriaLabel()"
                    >
                        {{ getProgressDisplay() }}%
                    </span>
                </div>
                <p
                    class="text-sm text-gray-600"
                    [attr.aria-describedby]="'gantt-title-' + titleId"
                >
                    {{ getDateRangeDisplay() }}
                </p>
            </div>

            <div class="flex flex-1 gap-1" role="row">
                <div
                    *ngFor="
                        let month of displayMonths;
                        trackBy: trackByMonth;
                        index as i
                    "
                    class="flex-1 border-e p-3 text-center text-[16px] md:text-base lg:text-lg"
                    role="columnheader"
                    [attr.aria-label]="'Month: ' + month.month"
                    [attr.aria-colindex]="i + 2"
                >
                    <span class="hidden sm:inline">{{ month.month }}</span>
                    <span class="sm:hidden">{{
                        getShortMonth(month.month)
                    }}</span>
                </div>
            </div>
        </div>
    `,
    // cspell:enable
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GanttHeaderComponent {
    @Input() public title?: string;
    @Input() public progress?: number;
    @Input() public startDate: Date | null = null;
    @Input() public endDate: Date | null = null;
    @Input() public displayMonths: MonthHeader[] = [];
    @Input() public titleId: string = Math.random().toString(36).substr(2, 9);

    public trackByMonth(index: number, month: MonthHeader): string {
        return `${month.month}-${index}`;
    }

    public getTimelineAriaLabel(): string {
        if (!this.startDate || !this.endDate) {
            return 'Project timeline';
        }
        return `Project timeline from ${this.formatDate(
            this.startDate
        )} to ${this.formatDate(this.endDate)}`;
    }

    public getProgressAriaLabel(): string {
        if (this.progress === undefined || this.progress === null) return '';
        return `Overall progress: ${Math.round(this.progress * 100)} percent`;
    }

    public getProgressDisplay(): string {
        if (this.progress === undefined || this.progress === null) return '0';
        return Math.round(this.progress * 100).toString();
    }

    public getDateRangeDisplay(): string {
        if (!this.startDate || !this.endDate) return '';
        return `${this.formatDate(this.startDate)} - ${this.formatDate(
            this.endDate
        )}`;
    }

    public getShortMonth(monthName: string): string {
        return monthName.substring(0, 3);
    }

    private formatDate(date: Date): string {
        return date.toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
        });
    }
}
