<!-- Content -->
<ng-container *ngIf="tableController">
    <!-- Filter box-->
    <app-table-list-filters
        *ngIf="data?.filters"
        [filters]="data.filters"
        [tableController]="tableController"
    >
    </app-table-list-filters>

    <!-- Table -->
    <app-list-loading [items]="tableController.items">
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th
                            *ngIf="data.tabs?.length || data.tabsFactory"
                            class="w-0"
                        ></th>
                        <ng-container *ngFor="let column of data.columns">
                            <th
                                *ngIf="!column.isHidden"
                                [ngStyle]="{ width: column.headerWidth ?? '' }"
                                [ngClass]="{ 'w-0': column.type === 'color' }"
                            >
                                {{ column.label | translate }}
                            </th>
                        </ng-container>
                        <th style="width: 0">
                            <i class="fa-light fa-gear"></i>
                        </th>
                    </tr>
                </thead>

                <tbody>
                    <ng-container
                        *ngFor="
                            let item of tableController.items;
                            let i = index
                        "
                    >
                        <ng-container
                            *ngIf="
                                data.groupByKey &&
                                (i === 0 ||
                                    tableController.items[i - 1][
                                        data.groupByKey
                                    ] !== item[data.groupByKey])
                            "
                        >
                            <tr class="bg-gray-50 text-center text-gray-400">
                                <td
                                    [attr.colspan]="
                                        data.columns.length +
                                        1 +
                                        (data.tabs?.length || data.tabsFactory
                                            ? 1
                                            : 0)
                                    "
                                >
                                    {{ item[data.groupByKey] }}
                                </td>
                            </tr>
                        </ng-container>

                        <tr>
                            <td *ngIf="data.tabs?.length || data.tabsFactory">
                                <app-action-button
                                    type="primary"
                                    [icon]="
                                        itemTabOpen[item['id']]
                                            ? 'fas fa-chevron-up'
                                            : 'fas fa-chevron-down'
                                    "
                                    (clicked)="
                                        itemTabOpen[item['id']] =
                                            !itemTabOpen[item['id']]
                                    "
                                ></app-action-button>
                            </td>

                            <ng-container *ngFor="let column of data.columns">
                                <td *ngIf="!column.isHidden">
                                    <app-property-value
                                        [item]="item"
                                        [propertyValueOptions]="column"
                                    ></app-property-value>

                                    <span
                                        class="text-sm text-gray-400"
                                        *ngIf="column.descriptionFactory"
                                    >
                                        {{
                                            column.descriptionFactory
                                                | callFunction : item
                                        }}
                                    </span>
                                </td>
                            </ng-container>

                            <td>
                                <app-table-actions-column
                                    [showDeleteButton]="
                                        item | isNotEqualFalse : data.canDelete
                                    "
                                    [showEditButton]="
                                        data.editMode !== 'none' &&
                                        (item | isNotEqualFalse : data.canEdit)
                                    "
                                    [editPermission]="data.permissions.write"
                                    [deletePermission]="data.permissions.delete"
                                    [editLink]="
                                        ['event-emitter', 'none'].includes(
                                            data.editMode
                                        )
                                            ? undefined
                                            : (data.route
                                              | pushToArray
                                                  : undefined
                                                  : ['edit', item.id])
                                    "
                                    (edit)="edit.emit(item.id)"
                                    [isDisabled]="isDeleting[item.id]"
                                    (delete)="delete.emit(item.id)"
                                >
                                    <!-- View Button -->
                                    <app-action-button
                                        class="w-full"
                                        *ngIf="data.detail as detail"
                                        icon="fas fa-eye"
                                        tooltip="translate_view"
                                        (clicked)="
                                            showDetailDialog(item.id, detail)
                                        "
                                    >
                                    </app-action-button>

                                    <!-- Flow -->
                                    <app-flow-move-button
                                        *ngIf="
                                            data.dynamicFlow &&
                                            (!data.dynamicFlow.show ||
                                                (item
                                                    | isNotEqualFalse
                                                        : data.dynamicFlow
                                                              .show)) &&
                                            ($any(item)
                                                | showFlowMoveButtonForItem)
                                        "
                                        [itemType]="data.dynamicFlow.type"
                                        [item]="$any(item)"
                                        (transfer)="
                                            tableController.filter$.next()
                                        "
                                    ></app-flow-move-button>

                                    <ng-container
                                        *ngFor="
                                            let actionBtn of data
                                                | getTableListActions : item
                                        "
                                    >
                                        <ng-container
                                            *ngIf="actionBtn.permission"
                                        >
                                            <ng-container
                                                *appHasPermissionId="
                                                    actionBtn.permission
                                                "
                                                [ngTemplateOutlet]="
                                                    actionButtonTemplate
                                                "
                                            ></ng-container>
                                        </ng-container>

                                        <ng-container
                                            *ngIf="!actionBtn.permission"
                                            [ngTemplateOutlet]="
                                                actionButtonTemplate
                                            "
                                        ></ng-container>

                                        <ng-template #actionButtonTemplate>
                                            <app-action-button
                                                [icon]="actionBtn.icon"
                                                [type]="actionBtn.type"
                                                [tooltip]="actionBtn.tooltip"
                                                [label]="actionBtn.label"
                                                [isDone]="actionBtn.isDone"
                                                [count]="actionBtn.count"
                                                [progress]="actionBtn.progress"
                                                [isDisabled]="
                                                    actionBtn.isDisabled
                                                        ? actionBtn.isDisabled(
                                                              item
                                                          )
                                                        : false
                                                "
                                                [action]="
                                                    actionBtn.click
                                                        ? actionBtn.click(item)
                                                        : actionBtn.linker
                                                        ? showLinker(
                                                              actionBtn.linker
                                                          )
                                                        : actionBtn.form
                                                        ? getShowFormDialog(
                                                              actionBtn.form
                                                          )
                                                        : actionBtn.dialog
                                                        ? getShowDialog(
                                                              item.id,
                                                              actionBtn.dialog
                                                          )
                                                        : undefined
                                                "
                                                [confirm]="
                                                    actionBtn.confirm
                                                        ? actionBtn.confirm(
                                                              item
                                                          )
                                                        : undefined
                                                "
                                            >
                                            </app-action-button>
                                        </ng-template>
                                    </ng-container>
                                </app-table-actions-column>
                            </td>
                        </tr>

                        <!-- Tabs -->
                        <ng-container
                            *ngIf="data | getTableListTabs : item as tabs"
                        >
                            <tr *ngIf="itemTabOpen[item['id']] && tabs.length">
                                <td
                                    [attr.colspan]="data.columns.length + 2"
                                    class="bg-primary-50"
                                >
                                    <app-tab-list>
                                        <app-tab
                                            *ngFor="let tabAction of tabs"
                                            [tabTitle]="
                                                (tabAction.label | translate) +
                                                (tabAction.count >= 0
                                                    ? ' (' +
                                                      tabAction.count +
                                                      ')'
                                                    : '') +
                                                (tabAction.progress >= 0
                                                    ? ' (' +
                                                      tabAction.progress +
                                                      '%)'
                                                    : '')
                                            "
                                        >
                                            <ng-container
                                                *ngIf="
                                                    tabAction.permission;
                                                    else tabsTemplate
                                                "
                                            >
                                                <ng-container
                                                    *appHasPermissionId="
                                                        tabAction.permission
                                                    "
                                                    [ngTemplateOutlet]="
                                                        tabsTemplate
                                                    "
                                                ></ng-container>
                                            </ng-container>

                                            <ng-template #tabsTemplate>
                                                <app-table-list-linker
                                                    *ngIf="
                                                        tabAction?.linker as linker
                                                    "
                                                    [bodyParameterName]="
                                                        linker.bodyParameterName
                                                    "
                                                    [endPoint]="linker.endPoint"
                                                    [data]="linker.data"
                                                    (changed)="refreshTable()"
                                                ></app-table-list-linker>
                                            </ng-template>
                                        </app-tab>
                                    </app-tab-list>
                                </td>
                            </tr>
                        </ng-container>
                    </ng-container>
                </tbody>
            </table>
        </div>

        <app-table-pagination
            [tableController]="tableController"
        ></app-table-pagination>
    </app-list-loading>
</ng-container>
