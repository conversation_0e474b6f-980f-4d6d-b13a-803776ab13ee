import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { StatisticalReportCategoryResult } from '@masar/common/models';
import { StatisticalReportService } from '@masar/pages/statistical-report/statistical-report.service';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { MnmFormState } from '@masar/shared/components';
import { AbstractControl, FormBuilder, ValidationErrors } from '@angular/forms';
import { rejectionFields } from '@masar/pages/statistical-report/statistical-report-rejection/fields';

@Component({
    selector: 'app-statistical-report-rejection',
    templateUrl: './statistical-report-rejection.component.html',
})
export class StatisticalReportRejectionComponent implements OnInit {
    @Input() public result: StatisticalReportCategoryResult;
    @Output() public updateResult = new EventEmitter();

    public formState: MnmFormState;
    public isSubmitting = false;
    public validationMessages = {
        required: 'translate_field_required',
        atLeastOne: 'translate_at_least_one_checkbox_required',
    };

    public constructor(
        private fb: FormBuilder,
        private statisticalReportService: StatisticalReportService,
        private notificationService: NotificationService
    ) {}

    public ngOnInit(): void {
        // Create the form state
        this.formState = new MnmFormState(rejectionFields(), this.fb);

        // Add custom validator to ensure at least one checkbox is checked
        this.formState.group.setValidators(this.atLeastOneCheckboxValidator());
        this.formState.group.updateValueAndValidity();
    }

    public rejectResult(): void {
        if (this.formState.group.invalid) {
            // Mark all fields as touched to show validation errors
            this.formState.group.markAllAsTouched();
            this.formState.setTriedToSubmit();
            return;
        }

        this.isSubmitting = true;
        const formValue = this.formState.group.value;

        this.statisticalReportService
            .rejectResult(
                this.result.id,
                formValue.removeValue,
                formValue.removeAttachment,
                formValue.notes
            )
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(result => {
                // Extract the first message from the result.messages[]
                let message =
                    result.messages && result.messages.length > 0
                        ? result.messages[0]
                        : 'Operation completed successfully';

                this.notificationService.notifySuccess(message);

                // Update the local result object if needed
                if (formValue.removeValue) {
                    this.result.value = null;
                }
                if (formValue.removeAttachment) {
                    this.result.attachmentCount = 0;
                }

                this.updateResult.emit();
            });
    }

    private atLeastOneCheckboxValidator(): (
        control: AbstractControl
    ) => ValidationErrors | null {
        return (control: AbstractControl): ValidationErrors | null => {
            const removeValue = control.get('removeValue')?.value;
            const removeAttachment = control.get('removeAttachment')?.value;

            return removeValue || removeAttachment
                ? null
                : { atLeastOne: true };
        };
    }
}
