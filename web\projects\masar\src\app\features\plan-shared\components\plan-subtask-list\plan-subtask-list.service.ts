import { Injectable } from '@angular/core';
import { Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient, HttpParams } from '@angular/common/http';
import { PlanSubtask } from '@masar/common/models';
import { environment } from '@masar/env/environment';
import { TableResult } from '@masar/common/misc/table';

@Injectable()
export class PlanSubtaskListService {
    public constructor(private httpClient: HttpClient) {}

    public list(
        keyword: string,
        assigneeType: string | null,
        departmentIds: string[],
        includeChildDepartments: boolean,
        teamIds: string[],
        userIds: string[],
        planIds: string[],
        taskIds: string[],
        from: Date,
        to: Date,
        status: string | null,
        orderBy: string,
        pageNumber: number,
        pageSize: number = 20,
        considerPlanFilterAsBase: boolean = false
    ): Observable<TableResult<PlanSubtask>> {
        let params = new HttpParams();
        params = params
            .append('keyword', keyword)
            .append('from', from?.toISOString())
            .append('to', to?.toISOString())
            .append('considerPlanFilterAsBase', `${considerPlanFilterAsBase}`)
            .append('assigneeType', assigneeType || '')
            .append('status', status || '');

        departmentIds.forEach(
            item => (params = params.append('departmentIds', item))
        );
        params = params.append(
            'includeChildDepartments',
            includeChildDepartments
        );
        teamIds.forEach(item => (params = params.append('teamIds', item)));
        userIds.forEach(item => (params = params.append('userIds', item)));
        planIds.forEach(item => (params = params.append('planIds', item)));
        taskIds.forEach(item => (params = params.append('taskIds', item)));

        params = params.append('orderBy', orderBy);
        params = params.append('pageNumber', `${pageNumber}`);
        params = params.append('pageSize', `${pageSize}`);

        return this.httpClient
            .get<Result<TableResult<PlanSubtask>>>(
                environment.apiUrl + '/plan-subtask',
                {
                    params,
                }
            )
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(environment.apiUrl + '/plan-subtask/' + id)
            .pipe(map(result => result.messages[0]));
    }
}
