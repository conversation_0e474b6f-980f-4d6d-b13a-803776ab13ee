import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';

export const planClassifiedResourcesFields: MnmFormField[] = [
    {
        fields: [
            {
                name: 'id',
                hide: true,
            },
            {
                name: 'nameAr',
                type: 'text',
                label: 'translate_name_in_arabic',
                size: 6,
                validators: [
                    Validators.required,
                    Validators.maxLength(256),
                    Validators.minLength(3),
                ],
            },
            {
                name: 'nameEn',
                type: 'text',
                label: 'translate_name_in_english',
                size: 6,
                validators: [
                    Validators.maxLength(256),
                    Validators.minLength(3),
                ],
            },
            {
                name: 'planResourceClassification',
                type: 'select',
                label: 'translate_plan_resources_classification',
                bindLabel: 'name',
                validators: [Validators.required],
            },
        ],
    },
];
