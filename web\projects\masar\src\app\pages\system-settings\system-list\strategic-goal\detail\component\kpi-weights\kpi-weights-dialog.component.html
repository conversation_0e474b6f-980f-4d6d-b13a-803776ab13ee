<div class="p-5">
    <mnm-form *ngIf="formState" [state]="formState" [translateLabels]="true">
        <span class="text-sm text-gray-400">
            {{ 'translate_kpi_weights_must_not_exceed_100' | translate }}
        </span>
        <div class="mt-4 flex items-center justify-center gap-1 font-bold">
            <div>
                {{ 'translate_total_weights' | translate }}
                :
            </div>
            <div
                [class.text-red-500]="totalWeight !== 100"
                [class.text-green-500]="totalWeight === 100"
            >
                {{ totalWeight }}
                %
            </div>
        </div>
    </mnm-form>

    <div class="mt-4 flex justify-center">
        <button
            class="btn btn-primary"
            [disabled]="totalWeight > 100"
            (click)="submit()"
        >
            <i
                *ngIf="isSubmitting"
                class="fa-light fa-spinner-third fa-spin mr-2"
            ></i>
            {{ 'translate_save' | translate }}
        </button>
    </div>
</div>
