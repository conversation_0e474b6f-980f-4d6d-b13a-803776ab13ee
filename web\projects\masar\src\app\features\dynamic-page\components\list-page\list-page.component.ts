import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TableController } from '@masar/common/misc/table';
import { TableListData } from '@masar/features/dynamic-page/interfaces';

@Component({
    selector: 'app-list-page',
    templateUrl: './list-page.component.html',
})
export class ListPageComponent {
    @Input() public tableController: TableController<unknown, unknown>;

    @Input() public data: TableListData<unknown>;

    @Input() public isDeleting: { [key: string]: boolean } = {};

    @Output() public delete = new EventEmitter<string>();
}
