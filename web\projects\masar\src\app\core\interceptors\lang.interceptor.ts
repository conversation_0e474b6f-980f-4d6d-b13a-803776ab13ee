import {
    <PERSON>ttpEvent,
    <PERSON>ttpHand<PERSON>,
    HttpInterceptor,
    HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable()
export class LangInterceptor implements HttpInterceptor {
    public intercept(
        req: HttpRequest<any>,
        next: <PERSON>ttpHandler
    ): Observable<HttpEvent<any>> {
        const lang = localStorage.getItem('lang') === 'ar' ? 'ar-AE' : 'en-AE';

        // set the language
        req = req.clone({ headers: req.headers.set('lang', lang) });

        return next.handle(req);
    }
}
