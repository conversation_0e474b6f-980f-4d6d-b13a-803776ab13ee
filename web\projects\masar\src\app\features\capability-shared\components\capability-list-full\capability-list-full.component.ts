import {
    Component,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    SimpleChanges,
    ViewChild,
} from '@angular/core';
import { Capability, Item } from '@masar/common/models';
import { TableController } from '@masar/common/misc/table/table-controller';
import { MiscApiService } from '@masar/core/services';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { CapabilityListComponent } from '../capability-list/capability-list.component';
import { Filter, TableResult } from '@masar/common/misc/table';
import { Loader } from '@masar/common/misc/loader';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { SharedCapabilityService } from '@masar/shared/services';

interface FilterData {
    keyword?: string;
    tournamentIds?: string[];
    pillarIds?: string[];
    standardIds?: string[];
}

type ShownFilter = 'keyword' | 'tournament' | 'pillar' | 'standard';

@Component({
    selector: 'app-capability-list-full',
    templateUrl: './capability-list-full.component.html',
})
export class CapabilityListFullComponent
    implements OnInit, OnDestroy, OnChanges
{
    @ViewChild('list') public list: CapabilityListComponent;

    @Input() public isFilterInUrl = false;
    @Input() public shownFilters: ShownFilter[] = [
        'keyword',
        'tournament',
        'pillar',
        'standard',
    ];

    @Input() public orderCallback: (
        orderedItems: Capability[],
        filter: Filter<FilterData>
    ) => Observable<any>;

    @Input() public alternateListCallback: (
        filter: Filter<FilterData>
    ) => Observable<TableResult<Capability>>;

    public tournamentLoader: Loader<Item>;
    public pillarLoader: Loader<Item>;
    public standardLoader: Loader<Item>;

    public tableController: TableController<Capability, FilterData>;

    private unsubscribeAll = new Subject();

    public constructor(
        private activatedRoute: ActivatedRoute,
        private sharedCapabilityService: SharedCapabilityService,
        private router: Router,
        private miscApiService: MiscApiService
    ) {
        this.initItems();
    }

    public ngOnInit(): void {
        this.initTableController();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
        this.tableController.stop();

        this.tournamentLoader?.dispose();
        this.pillarLoader?.dispose();
        this.standardLoader?.dispose();
    }

    public ngOnChanges(changes: SimpleChanges): void {
        const alternateListCallbackChange = changes['alternateListCallback'];
        if (!alternateListCallbackChange) return;

        this.reload();
    }

    public refreshItems(resetPageNumber: boolean = false): void {
        this.tableController.filter$.next(resetPageNumber);
    }

    public order(event: CdkDragDrop<Capability>): void {
        if (!this.orderCallback) return;

        this.tableController.order(event.previousIndex, event.currentIndex);
    }

    private reload(): void {
        this.initTableController();
    }

    private initTableController(): void {
        this.tableController?.stop();
        this.tableController = null;

        const callback: (
            filter: Filter<FilterData>
        ) => Observable<TableResult<Capability>> =
            this.alternateListCallback ??
            (filter => {
                return this.sharedCapabilityService.list(
                    filter.data.keyword,
                    filter.data.tournamentIds,
                    filter.data.pillarIds,
                    filter.data.standardIds,
                    filter.pageNumber,
                    filter.pageSize
                );
            });

        this.tableController = new TableController<Capability, FilterData>(
            callback,
            {
                data: {
                    keyword: '',
                    tournamentIds: [],
                    pillarIds: [],
                    standardIds: [],
                },
                pageSize: 50,
            },
            this.isFilterInUrl
                ? {
                      routingControls: {
                          router: this.router,
                          activatedRoute: this.activatedRoute,
                      },
                      orderCallback: this.orderCallback,
                  }
                : {
                      orderCallback: this.orderCallback,
                  }
        );
        this.tableController.start();
    }

    private initItems(): void {
        this.tournamentLoader = new Loader(keyword =>
            this.miscApiService.tournaments({ keyword })
        );
        this.pillarLoader = new Loader(keyword =>
            this.miscApiService.pillars({ keyword })
        );
        this.standardLoader = new Loader(keyword =>
            this.miscApiService.standards({ keyword })
        );
    }
}
