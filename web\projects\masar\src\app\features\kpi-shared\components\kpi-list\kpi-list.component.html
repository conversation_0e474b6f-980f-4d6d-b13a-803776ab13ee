<div class="table-responsive mb-5">
    <table>
        <thead>
            <tr>
                <th *ngIf="shownFields.includes('evaluation')">
                    {{ 'translate_evaluation_score' | translate }}
                </th>

                <th *ngIf="shownFields.includes('achieved')">
                    {{ 'translate_achieved' | translate }}
                </th>

                <th *ngIf="shownFields.includes('name')">
                    {{ 'translate_kpi_title' | translate }}
                </th>

                <th *ngIf="shownFields.includes('cycle')">
                    {{ 'translate_measuring_cycle' | translate }}
                </th>

                <th
                    class="hidden sm:table-cell"
                    *ngIf="shownFields.includes('last_modified')"
                >
                    {{ 'translate_latest_result_modification' | translate }}
                </th>

                <th
                    class="hidden sm:table-cell"
                    *ngIf="shownFields.includes('last_evaluation')"
                >
                    {{ 'translate_last_evaluation' | translate }}
                </th>

                <th *ngFor="let col of customFields" style="width: 0">
                    {{ col.name | translate }}
                </th>
            </tr>
        </thead>

        <tbody
            cdkDropList
            [cdkDropListDisabled]="!enableDrag"
            (cdkDropListDropped)="order.emit($event)"
        >
            <tr *ngFor="let item of items; let idx = index" cdkDrag>
                <td
                    *ngIf="shownFields.includes('evaluation')"
                    class="text-center"
                >
                    <app-progress-ring
                        [value]="item.evaluateAverageScores"
                        [radius]="30"
                        [strokeWidth]="6"
                    ></app-progress-ring>
                </td>

                <!-- Achieved  -->
                <td
                    *ngIf="shownFields.includes('achieved')"
                    class="text-center"
                >
                    <ng-container *ngIf="!item.isTrend; else isTrend">
                        <app-progress-ring
                            *ngIf="item.achieved !== null; else noAchieved"
                            [value]="item.achieved"
                            [radius]="30"
                            [strokeWidth]="6"
                        ></app-progress-ring>

                        <ng-template #noAchieved>
                            <span class="text-gray-500">
                                {{ 'translate_not_yet' | translate }}
                            </span>
                        </ng-template>
                    </ng-container>

                    <!-- A template for when the kpi is a trend -->
                    <ng-template #isTrend>
                        <span class="text-gray-500">
                            {{ 'translate_na' | translate }}
                        </span>
                    </ng-template>
                </td>

                <!-- Name -->
                <td
                    *ngIf="shownFields.includes('name')"
                    class="whitespace-nowrap"
                >
                    <!-- Badges row -->
                    <div class="mb-0.5 flex flex-row items-center gap-0.5">
                        <app-kpi-code-badge [kpi]="item"></app-kpi-code-badge>

                        <!-- has benchmarks -->
                        <p
                            *ngIf="item.benchmarkCount > 0"
                            class="benchmark-badge badge badge-primary inline-block cursor-pointer items-center px-[5px]"
                            (click)="showBenchmarkListModal(item)"
                        >
                            <em class="fa fa-badge-check py-1 text-white"></em>
                            <span
                                class="inline-block overflow-hidden whitespace-nowrap text-xs transition"
                            >
                                <span class="pe-1 ps-1">
                                    {{ 'translate_benchmarks' | translate }}
                                </span>
                            </span>
                        </p>
                    </div>

                    <!-- Name -->
                    <p
                        class="whitespace-normal"
                        style="max-width: 400px"
                        [title]="item.name"
                    >
                        <a
                            *appHasPermissionId="
                                permissionList.kpiRead;
                                else nameWhenNotActiveTemplate
                            "
                            [routerLink]="['', 'kpi', 'detail', item.id]"
                        >
                            {{ item.name }}
                        </a>
                        <ng-template #nameWhenNotActiveTemplate>
                            {{ item.name }}
                        </ng-template>
                    </p>

                    <!-- Department -->
                    <p class="mb-0.5 me-1 text-gray-400">
                        <em class="fa-light fa-buildings"></em>
                        {{ item.owningDepartment.name }}
                    </p>
                </td>

                <!-- Measurement Cycle -->
                <td
                    *ngIf="shownFields.includes('cycle')"
                    class="text-center text-gray-500"
                >
                    {{
                        item.measurementCycle
                            | translateItem : 'kpi-cycle'
                            | async
                    }}
                </td>

                <!-- Latest modification -->
                <td
                    class="hidden sm:table-cell"
                    *ngIf="shownFields.includes('last_modified')"
                >
                    <ng-container
                        *ngIf="item.latestResultModification; else notModified"
                    >
                        <p class="text-sm text-gray-500">
                            <em class="fa-light fa-calendar-days me-1"></em>
                            {{
                                item.latestResultModification
                                    | date : 'yyyy/MM/dd'
                            }}
                        </p>
                        <p
                            class="overflow-hidden overflow-ellipsis whitespace-nowrap text-sm text-gray-500"
                            style="max-width: 100px"
                            [appTooltip]="item.resultModifiedBy.name"
                        >
                            <em class="fa-light fa-user me-1"></em>
                            <span>{{ item.resultModifiedBy.name }}</span>
                        </p>
                    </ng-container>
                    <ng-template #notModified>-</ng-template>
                </td>

                <td
                    class="hidden sm:table-cell"
                    *ngIf="shownFields.includes('last_evaluation')"
                >
                    <ng-container
                        *ngIf="
                            item.latestEvaluateModification;
                            else notModified
                        "
                    >
                        <p class="text-sm text-gray-500">
                            <em class="fa-light fa-calendar-days me-1"></em>
                            {{
                                item.latestEvaluateModification
                                    | date : 'yyyy/MM/dd'
                            }}
                        </p>
                        <p
                            class="overflow-hidden overflow-ellipsis whitespace-nowrap text-sm text-gray-500"
                            style="max-width: 100px"
                            [appTooltip]="item.evaluateModifiedBy.name"
                        >
                            <em class="fa-light fa-user me-1"></em>
                            <span>{{ item.evaluateModifiedBy.name }}</span>
                        </p>
                    </ng-container>
                    <ng-template #notModified>-</ng-template>
                </td>

                <!-- Custom fields -->
                <td *ngFor="let col of customFields">
                    <ng-container
                        [ngTemplateOutlet]="col.template"
                        [ngTemplateOutletContext]="{item}"
                    >
                    </ng-container>
                </td>
            </tr>
        </tbody>
    </table>
</div>
