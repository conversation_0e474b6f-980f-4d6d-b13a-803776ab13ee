import { MnmFormField } from '@masar/shared/components';

export const fields = (kpiWeights: any[]): MnmFormField[] => [
    {
        name: 'id',
        hide: true,
    },
    ...kpiWeights.map(kpi => ({
        name: `weight_${kpi.id}`,
        label: kpi.name,
        type: 'number' as 'number',
        size: 3,
        defaultValue: kpi.weight || 0,
        minValue: 0,
        maxValue: 100,
    })),
];
