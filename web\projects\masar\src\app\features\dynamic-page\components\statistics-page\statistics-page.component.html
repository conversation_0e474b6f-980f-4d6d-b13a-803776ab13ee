<app-page [pageTitle]="statisticsTitle">
    <ng-container tools>
        <ng-container
            *ngIf="toolsTemplateRef"
            [ngTemplateOutlet]="toolsTemplateRef"
        ></ng-container>
    </ng-container>
    <div content class="flex flex-col gap-8">
        <!-- Statistics -->
        <app-statistics-section
            *ngIf="statisticsGroups?.length"
            [groups]="statisticsGroups"
            [statisticsGroupLinkMap]="statisticsGroupLinkMap"
            [statisticsGroupCallbackMap]="statisticsGroupCallbackMap"
        >
        </app-statistics-section>

        <!-- Departments -->
        <app-content
            *ngIf="departmentStatisticsLoader"
            [contentTitle]="'translate_departments_statistics' | translate"
        >
            <div content>
                <div *ngIf="!isLoadingDepartments">
                    <div
                        *ngIf="previousParents.length"
                        class="mb-4 flex justify-end"
                    >
                        <button
                            dir="ltr"
                            (click)="goToPreviousDepartmentStatistics()"
                            class="btn btn-sm btn-info flex items-center gap-1"
                        >
                            <i class="fas fa-left"></i>

                            <span class="hidden md:inline">
                                {{ 'translate_back' | translate }}
                            </span>
                        </button>
                    </div>

                    <div
                        *ngIf="departmentStatistics"
                        class="grid grid-cols-1 justify-items-center gap-6 transition-all sm:grid-cols-2 md:grid-cols-3"
                    >
                        <ng-container
                            *ngFor="let item of departmentStatistics.children"
                            [ngTemplateOutlet]="departmentStatisticTemplateRef"
                            [ngTemplateOutletContext]="{item}"
                        >
                        </ng-container>
                    </div>
                </div>

                <div class="flex items-center justify-center">
                    <app-loading-ring
                        *ngIf="isLoadingDepartments"
                        [spinnerBorderWidth]="4"
                        [spinnerDim]="100"
                    ></app-loading-ring>
                </div>
            </div>
        </app-content>
    </div>
</app-page>
