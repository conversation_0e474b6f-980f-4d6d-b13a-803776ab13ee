import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-action-button',
    templateUrl: './action-button.component.html',
    styles: [
        `
            :host {
                @apply inline-block;
            }
        `,
    ],
})
export class ActionButtonComponent {
    @Input() public type?:
        | 'info'
        | 'danger'
        | 'warning'
        | 'success'
        | 'primary';

    @Input() public tooltip?: string;

    @Input() public label?: string;

    @Input() public isDisabled?: boolean;

    @Input() public icon?: string;

    @Input() public text: string;

    @Input() public link?: string[];

    @Input() public action?: () => void;

    @Input() public confirm?: { title: string };

    @Input() public isDone?: boolean;

    @Input() public progress?: number;

    @Input() public count?: number;

    @Output() public clicked = new EventEmitter<void>();

    public constructor(
        private readonly router: Router,
        private readonly translateService: TranslateService
    ) {}

    public onClick(): void {
        if (!this.confirm) this.confirmed();
        else this.showConfirm();
    }

    public showConfirm(): void {
        if (!this.confirm) return;

        const title = this.translateService.instant(this.confirm.title);
        const confirmButtonText =
            this.translateService.instant('translate_yes');
        const cancelButtonText =
            this.translateService.instant('translate_cancel');

        Swal.fire({
            title,
            confirmButtonText,
            cancelButtonText,
            showCancelButton: true,
            showCloseButton: true,
        }).then(result => {
            if (result.isConfirmed) this.confirmed();
        });
    }

    public confirmed(): void {
        if (this.link?.length) this.router.navigate(this.link).then();
        else this.action?.();

        this.clicked.emit();
    }
}
