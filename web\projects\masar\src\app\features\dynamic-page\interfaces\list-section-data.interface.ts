import { Type } from '@angular/core';
import { MnmFormField } from '@masar/shared/components';
import { MiscApiEndPoint, PermissionValue } from '@masar/common/types';
import { Alert } from '@masar/common/interfaces/alert.interface';
import { TableListFilter } from '../types';
import { TableListData } from './table-list-data.interface';

export interface ListSectionData<T, C = never> extends TableListData<T> {
    alerts?: Alert[];
    newComponent?: Type<C>;
    newForm?: {
        fields: () => MnmFormField[];
        misc?: [MiscApiEndPoint, string][];
        isOverflowVisible?: boolean;
    };
    newLabel: string;
    editLabel?: string;
    hideNew?: boolean;
    sectionActions?: [
        {
            label: string;
            icon: string;
            callbackFn: () => void;
            permission?: PermissionValue;
        }
    ];
    filters?: TableListFilter[];
}
