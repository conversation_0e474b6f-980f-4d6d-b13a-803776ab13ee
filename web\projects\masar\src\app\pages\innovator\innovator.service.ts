import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Result, miscFunctions } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';
import { TableResult } from '@masar/common/misc/table';
import { CheckEmployeeNumber, Innovator } from '@masar/common/models';

@Injectable()
export class InnovatorService {
    public constructor(private httpClient: HttpClient) {}

    public list(
        employeeNumber: string,
        keyword: string,
        rank: string,
        innovationLogo: string,
        pageNumber: number,
        pageSize: number = 20
    ): Observable<TableResult<Innovator>> {
        let params = new HttpParams();

        params = params.append('employeeNumber', employeeNumber);
        params = params.append('keyword', keyword);
        params = params.append('rank', rank);
        params = params.append('hasALogo', innovationLogo);

        params = params.append('pageNumber', `${pageNumber}`);
        params = params.append('pageSize', `${pageSize}`);

        return this.httpClient
            .get<Result<TableResult<Innovator>>>(
                environment.apiUrl + '/innovator',
                { params }
            )
            .pipe(map(result => result.extra));
    }

    public create(innovator: Innovator): Observable<Innovator> {
        return this.httpClient
            .post<Result<Innovator>>(
                environment.apiUrl + '/innovator',
                miscFunctions.objectToURLParams({
                    innovator: JSON.stringify(innovator),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(innovator: Innovator): Observable<Innovator> {
        return this.httpClient
            .put<Result<Innovator>>(
                environment.apiUrl + '/innovator',
                miscFunctions.objectToURLParams({
                    innovator: JSON.stringify(innovator),
                })
            )
            .pipe(map(result => result.extra));
    }

    public get(id: string, forEdit: boolean = false): Observable<Innovator> {
        return this.httpClient
            .get<Result<Innovator>>(environment.apiUrl + '/innovator/' + id, {
                params: new HttpParams().append('forEdit', `${forEdit}`),
            })
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.httpClient
            .delete<Result<any>>(environment.apiUrl + '/innovator/' + id)
            .pipe(map(result => result.messages[0]));
    }

    public toggleLogo(id: string): Observable<string> {
        return this.httpClient
            .post<Result<any>>(
                environment.apiUrl + '/innovator/toggle-logo/' + id,
                {}
            )
            .pipe(map(result => result.messages[0]));
    }

    public checkEmployeeNumber(
        employeeNumber: string
    ): Observable<CheckEmployeeNumber> {
        return this.httpClient
            .get<Result<CheckEmployeeNumber>>(
                environment.apiUrl + '/innovator/check-employee-number',
                {
                    params: new HttpParams().append(
                        'employeeNumber',
                        employeeNumber
                    ),
                }
            )
            .pipe(map(result => result.extra));
    }

    public currentInnovator(): Observable<Innovator> {
        return this.httpClient
            .get<Result<Innovator>>(`${environment.apiUrl}/innovator/current`)
            .pipe(map(result => result.extra));
    }
}
