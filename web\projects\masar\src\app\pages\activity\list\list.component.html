<app-page pageTitle="{{ 'translate_activities' | translate }}">
    <!-- Header -->
    <a
        tools
        [routerLink]="['', 'activity', 'new']"
        class="btn btn-sm btn-outline-white"
    >
        <i class="fa-light fa-plus me-2"></i>
        <span>{{ 'translate_add_new' | translate }}</span>
    </a>

    <!-- Content -->
    <div content>
        <!-- Filter -->
        <app-filter-result-box>
            <!-- keyword field -->
            <app-search-input
                [(ngModel)]="tableController.filter.data.keyword"
                [tableController]="tableController"
            ></app-search-input>

            <!-- keyword field -->
            <app-search-input
                [placeholder]="'translate_search_by_year'"
                [(ngModel)]="tableController.filter.data.year"
                [tableController]="tableController"
                [type]="'number'"
            ></app-search-input>
        </app-filter-result-box>

        <!-- Table -->
        <app-list-loading [items]="tableController.items">
            <table class="mb-5">
                <thead>
                    <tr>
                        <th>{{ 'translate_name' | translate }}</th>
                        <th>{{ 'translate_year' | translate }}</th>
                        <th>{{ 'translate_innovation_count' | translate }}</th>
                        <th>{{ 'translate_innovator_count' | translate }}</th>
                        <th style="width: 0%">
                            <i class="fa-light fa-gear"></i>
                        </th>
                    </tr>
                </thead>

                <tbody>
                    <tr
                        *ngFor="
                            let item of tableController.items;
                            let idx = index
                        "
                    >
                        <!-- Name -->
                        <td>
                            <div class="flex flex-row items-center gap-1">
                                <a
                                    [routerLink]="[
                                        '',
                                        'activity',
                                        'detail',
                                        item.id
                                    ]"
                                    >{{ item.name }}</a
                                >
                            </div>
                        </td>

                        <!-- Year -->
                        <td>{{ item.year }}</td>

                        <!-- Innovation count -->
                        <td>{{ item.innovationCount }}</td>

                        <!-- Innovator count -->
                        <td>{{ item.innovatorCount }}</td>

                        <!-- Control buttons -->
                        <td>
                            <app-dropdown>
                                <!-- Edit link -->
                                <a
                                    class="btn btn-sm btn-info"
                                    [appTooltip]="'translate_edit' | translate"
                                    [routerLink]="[
                                        '',
                                        'activity',
                                        'edit',
                                        item.id
                                    ]"
                                >
                                    <i class="fa-light fa-edit fa-fw"></i>
                                    <!--                                    {{ 'translate_edit' | translate }}-->
                                </a>

                                <!-- Delete button -->
                                <!-- (confirm)="delete(item)" -->
                                <button
                                    [disabled]="
                                        currentlyDeleting.includes(item.id)
                                    "
                                    [swal]="{
                                        title:
                                            'translate_delete_this_item_question_mark'
                                            | translate,
                                        confirmButtonText:
                                            'translate_yes' | translate,
                                        cancelButtonText:
                                            'translate_cancel' | translate,
                                        showCancelButton: true,
                                        showCloseButton: true
                                    }"
                                    class="btn btn-sm btn-danger"
                                    [appTooltip]="
                                        'translate_delete' | translate
                                    "
                                >
                                    <i class="fas fa-trash fa-fw"></i>
                                    <!--                                    {{ 'translate_delete' | translate }}-->
                                </button>
                            </app-dropdown>
                        </td>
                    </tr>
                </tbody>
            </table>
            <app-table-pagination
                [tableController]="tableController"
            ></app-table-pagination>
        </app-list-loading>
    </div>
</app-page>
