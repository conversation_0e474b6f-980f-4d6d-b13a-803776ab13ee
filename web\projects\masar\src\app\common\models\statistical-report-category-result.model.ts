import { StatisticalReportCategory } from './statistical-report-category.model';
import { Department } from './department.model';

export interface StatisticalReportCategoryResult {
    id: string;
    year: number;
    period: number;
    value: number;
    isLocked: boolean | number;
    attachmentCount: number;
    category: StatisticalReportCategory;
    department: Department;
    parent: StatisticalReportCategory;
    noteCount: number;
}
