<div class="table-responsive">
    <table class="min-w-full overflow-hidden rounded-md bg-white shadow-md">
        <tbody>
            <ng-container
                *ngFor="
                    let contentItem of section.content;
                    let even = even;
                    let index = index
                "
            >
                <tr *ngIf="section.eachItemInRow || !twoItemsInOneRow || even">
                    <ng-container
                        [ngTemplateOutlet]="itemTemplate"
                        [ngTemplateOutletContext]="{
                            contentItem: contentItem
                        }"
                    ></ng-container>

                    <ng-container
                        *ngIf="
                            !section.eachItemInRow &&
                            twoItemsInOneRow &&
                            section.content[index + 1] as nextContentItem
                        "
                        [ngTemplateOutlet]="itemTemplate"
                        [ngTemplateOutletContext]="{
                            contentItem: nextContentItem
                        }"
                    ></ng-container>
                </tr>
            </ng-container>
        </tbody>
    </table>
</div>

<ng-template #itemTemplate let-contentItem="contentItem">
    <ng-container
        [ngTemplateOutlet]="labelTdTemplate"
        [ngTemplateOutletContext]="{
            contentItem: contentItem
        }"
    ></ng-container>

    <td class="w-1/4 border-b p-4" *ngIf="!contentItem.isHidden">
        <app-property-value
            [item]="item"
            [propertyValueOptions]="contentItem"
        ></app-property-value>
    </td>
</ng-template>

<ng-template let-contentItem="contentItem" #labelTdTemplate>
    <td *ngIf="!contentItem.isHidden" class="w-1/4 border-b p-4 font-bold">
        {{ contentItem.label | translate }}
    </td>
</ng-template>
