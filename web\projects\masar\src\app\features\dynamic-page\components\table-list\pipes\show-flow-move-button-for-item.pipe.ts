import { Pipe, PipeTransform } from '@angular/core';
import { extractAvailableStates } from '@masar/features/flow/utils/extract-available-states';
import { FlowItem } from '@masar/features/flow/interfaces';

@Pipe({ name: 'showFlowMoveButtonForItem' })
export class ShowFlowMoveButtonForItemPipe implements PipeTransform {
    public transform(item: unknown & FlowItem): boolean {
        return extractAvailableStates(item).length > 0;
    }
}
