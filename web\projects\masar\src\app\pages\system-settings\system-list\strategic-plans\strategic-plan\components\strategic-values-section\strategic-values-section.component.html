<div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
    <div class="mb-4 flex items-center justify-between">
        <div class="flex items-center">
            <h3 class="text-lg font-semibold text-gray-900">
                {{ 'translate_strategic_values' | translate }}
            </h3>
        </div>
    </div>

    <div
        *ngIf="strategicPlan?.values.length; else noValues"
        class="overflow-hidden"
    >
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                        {{ 'translate_name' | translate }}
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
                <tr
                    *ngFor="let value of strategicPlan.values; let i = index"
                    [class.bg-gray-50]="i % 2 === 1"
                >
                    <td class="px-4 py-3">
                        <div class="text-sm font-medium text-gray-900">
                            {{ value.name }}
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <ng-template #noValues>
        <div class="py-8 text-center">
            <p class="text-sm text-gray-500">
                {{ 'translate_no_strategic_values_found' | translate }}
            </p>
        </div>
    </ng-template>
</div>
