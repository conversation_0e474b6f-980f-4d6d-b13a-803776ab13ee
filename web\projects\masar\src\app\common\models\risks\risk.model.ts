import {
    Department,
    Operation,
    Plan,
    RiskImpact,
    RiskManagementStrategy,
    RiskProbability,
    StrategicGoal,
    RiskCategory,
} from '@masar/common/models';
import { EvaluateItem } from '@masar/features/evaluate/interfaces';

export interface Risk extends EvaluateItem {
    id: string;
    name: string;
    description: string;
    order: number;
    code: string;
    causes: string;
    impactDetails: string;
    probabilityDetails: string;
    owner: string;
    managementProcedureDetails: string;
    managementProcedureSteps: string;
    category: RiskCategory;
    department: Department;
    managementStrategy: RiskManagementStrategy;
    impact: RiskImpact;
    probability: RiskProbability;
    goals: StrategicGoal[];
    plans: Plan[];
    operations: Operation[];
    type: string;
    probabilityAfterMitigation?: RiskProbability;
    impactAfterMitigation?: RiskImpact;
}
