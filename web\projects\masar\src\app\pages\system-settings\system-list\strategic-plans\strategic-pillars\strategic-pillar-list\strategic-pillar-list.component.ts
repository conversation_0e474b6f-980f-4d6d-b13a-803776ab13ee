import { Component, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { StrategicPillarService } from '../services/strategic-pillar.service';
import { NotificationService } from 'mnm-webapp';
import { TableController } from '@masar/common/misc/table/table-controller';
import { finalize } from 'rxjs/operators';
import { StrategicPillar } from '@masar/common/models/strategic-pillar.model';

@Component({
    selector: 'app-list',
    templateUrl: './strategic-pillar-list.component.html',
})
export class StrategicPillarListComponent implements OnD<PERSON>roy {
    public tableController: TableController<
        StrategicPillar,
        {
            keyword?: string;
        }
    >;

    public currentlyDeleting: string[] = [];

    public constructor(
        private strategicPillarService: StrategicPillarService,
        private notificationService: NotificationService
    ) {
        this.tableController = new TableController<
            StrategicPillar,
            {
                keyword?: string;
            }
        >(
            filter =>
                strategicPillarService.list(
                    filter.data.keyword,
                    filter.pageNumber,
                    filter.pageSize
                ),
            {
                data: {
                    keyword: '',
                },
            }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public deleteStrategicPillar(item: StrategicPillar): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id!);
        this.strategicPillarService
            .delete(item.id!)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }
}
