import {
    Component,
    EventEmitter,
    Input,
    NgModuleRef,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { KpiWeightsService } from './kpi-weights.service';
import { finalize, first, takeUntil } from 'rxjs/operators';
import { ModalService, NotificationService } from 'mnm-webapp';
import { TranslateService } from '@ngx-translate/core';
import { MnmFormState } from '@masar/shared/components';
import { FormBuilder } from '@angular/forms';
import { fields } from './fields';
import { Subject, Subscription } from 'rxjs';

interface KpiWeight {
    id: string;
    name: string;
    weight: number;
}

@Component({
    templateUrl: './kpi-weights-dialog.component.html',
    providers: [KpiWeightsService],
})
export class KpiWeightsDialogComponent implements OnInit, OnDestroy {
    @Input() public strategicGoalId: string;

    @Output() public submitted = new EventEmitter<void>();

    public kpiWeights: KpiWeight[] = [];
    public isSubmitting = false;
    public totalWeight = 0;
    public formState: MnmFormState;

    private destroy$ = new Subject<void>();
    private formValueChanges$: Subscription;

    public constructor(
        private kpiWeightsService: KpiWeightsService,
        private notificationService: NotificationService,
        private translateService: TranslateService,
        private fb: FormBuilder
    ) {}

    public static async showAsDialog(
        strategicGoalId: string,
        modalService: ModalService,
        translateService: TranslateService,
        moduleRef: NgModuleRef<any>,
        onSuccessCallback: () => void
    ): Promise<void> {
        translateService
            .get('translate_assign_kpi_weights')
            .pipe(first())
            .subscribe(async title => {
                const unsubscribeAll = new Subject();

                const component = await modalService.show(
                    KpiWeightsDialogComponent,
                    {
                        onDismiss: () => {
                            unsubscribeAll.next();
                            unsubscribeAll.complete();
                        },
                        beforeInit: c => {
                            c.strategicGoalId = strategicGoalId;
                        },
                        moduleRef: moduleRef,
                        title,
                        size: { width: '600px' },
                    }
                );

                component.submitted
                    .pipe(takeUntil(unsubscribeAll))
                    .subscribe(() => {
                        onSuccessCallback();
                        modalService.dismiss(component);
                    });
            });
    }

    public ngOnInit(): void {
        this.loadKpis();
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();

        if (this.formValueChanges$) {
            this.formValueChanges$.unsubscribe();
        }
    }

    public isValidTotal(): boolean {
        return this.totalWeight <= 100;
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        if (this.totalWeight > 100) {
            this.notificationService.notifyError(
                this.translateService.instant(
                    'translate_kpi_weights_must_not_exceed_100'
                )
            );
            return;
        }

        this.isSubmitting = true;

        const formValues = this.formState.group.getRawValue();
        const payload = {
            id: this.strategicGoalId,
            kpiWeights: this.kpiWeights.map(kpi => ({
                id: kpi.id,
                weight: parseFloat(formValues[`weight_${kpi.id}`]) / 100,
            })),
        };

        this.kpiWeightsService
            .updateKpiWeights(payload)
            .pipe(
                takeUntil(this.destroy$),
                finalize(() => (this.isSubmitting = false))
            )
            .subscribe(() => {
                this.submitted.emit();
            });
    }

    private loadKpis(): void {
        this.kpiWeightsService
            .getKpis(this.strategicGoalId)
            .pipe(takeUntil(this.destroy$))
            .subscribe(kpis => {
                this.kpiWeights = kpis.map(kpi => ({
                    id: kpi.id,
                    name: kpi.name,
                    weight: parseFloat(((kpi.weight || 0) * 100).toFixed(2)),
                }));

                this.formState = new MnmFormState(
                    fields(this.kpiWeights),
                    this.fb
                );

                // Clean up previous subscription if it exists
                if (this.formValueChanges$) {
                    this.formValueChanges$.unsubscribe();
                }

                // Calculate initial total
                this.calculateTotal();

                // Store the subscription reference
                this.formValueChanges$ =
                    this.formState.group.valueChanges.subscribe(() => {
                        this.calculateTotal();
                    });
            });
    }

    private calculateTotal(): void {
        if (!this.formState) return;

        const formValues = this.formState.group.getRawValue();
        let total = 0;

        this.kpiWeights.forEach(kpi => {
            const weight = formValues[`weight_${kpi.id}`] || 0;
            total += parseFloat(weight.toString());
        });

        this.totalWeight = parseFloat(total.toFixed(2));
    }
}
