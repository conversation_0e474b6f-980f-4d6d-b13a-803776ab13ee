export type MiscApiEndPoint =
    | 'plan-expected-benefit-achievement-target'
    | 'service-electronic-transformation-ratio'
    | 'improvement-opportunity-input-category'
    | 'improvement-opportunity-input-source'
    | 'improvement-opportunity-priority'
    | 'plan-risk-occurring-probability'
    | 'standard-subtask-approval-type'
    | 'plan-subsubtask-approval-type'
    | 'service-development-entrance'
    | 'partner-evaluation-standard'
    | 'kpi-balanced-behavior-card'
    | 'kpi-initial-result-source'
    | 'government-strategic-goal'
    | 'service-provider-channel'
    | 'service-delivery-channel'
    | 'statistical-report-cycle'
    | 'strategic-goal-category'
    | 'ministry-strategic-goal'
    | 'service-client-category'
    | 'kpi-calculation-method'
    | 'service-payment-method'
    | 'sustainability-impacts'
    | 'partner-standard-type'
    | 'plan-category-type'
    | 'plan-task-category'
    | 'plan-task-stakeholder'
    | 'kpi-data-entry-method'
    | 'service-duration-type'
    | 'team-member-position'
    | 'kpi-progress-state'
    | 'plan-future-plan'
    | 'creation-year'
    | 'plan-assignee-type'
    | 'proactive-standard'
    | 'organization-type'
    | 'kpi-creation-year'
    | 'user-request-type'
    | 'service-ownership'
    | 'partner-standard'
    | 'kpi-cycle-period'
    | 'plan-risk-impact'
    | 'service-category'
    | 'proactive-status'
    | 'national-agenda'
    | 'capability-type'
    | 'strategic-goal'
    | 'success-factor'
    | 'kpi-direction'
    | 'plan-category'
    | 'resource-type'
    | 'service-type'
    | 'content-type'
    | 'plan-subtask'
    | 'request-type'
    | 'user-status'
    | 'kpi-source'
    | 'plan-input'
    | 'kpi-cycle'
    | 'plan-task'
    | 'innovator'
    | 'kpi-unit'
    | 'activity'
    | 'service'
    | 'partner'
    | 'partner-type'
    | 'policy'
    | 'plan'
    | 'team'
    | 'user'
    | 'kpi'
    | 'plan-resource-classification'
    | 'plan-classified-resource'
    | 'strategic-plan'
    | 'strategic-pillar'

    // Risk-related
    | 'risk-acceptance-level-category'
    | 'risk-management-strategy'
    | 'risk-probability'
    | 'risk-category'
    | 'risk-impact'
    | 'risk-type'
    | 'risk'

    // Evaluation-related
    | 'evaluation-type'
    | 'evaluations'

    // partnership contract
    | 'partnership-activity-communication-tool'
    | 'partnership-time-frame'
    | 'partnership-framework'
    | 'partnership-contract'
    | 'partnership-scope'
    | 'partnership-field'
    | 'partnership-type'

    // operation
    | 'operation-procedure-periodicity'
    | 'operation-procedure-duration-type'
    | 'operation-rule-and-regulation'
    | 'operation-supplier-category'
    | 'operation-enhancement-type'
    | 'operation-beneficiary-type'
    | 'operation-procedure-type'
    | 'operation-executor-type'
    | 'operation-specification'
    | 'main-operation-owner'
    | 'operation-procedure'
    | 'operation-form-type'
    | 'operation-output'
    | 'operation-type'
    | 'operation'

    // kpi result
    | 'kpi-result-data-entry-response-transfer-assignee'
    | 'kpi-result-data-entry-request-path'
    | 'kpi-result-target-setting-method'
    | 'kpi-result-breakdown-parameter'
    | 'kpi-result-calculation-formula'
    | 'kpi-result-aggregation-type'
    | 'kpi-result-category-type'
    | 'kpi-result-input-mode'

    // Library
    | 'library-tag'

    // benchmark
    | 'benchmark-other-management-type'
    | 'benchmark-selected-visit-year'
    | 'benchmark-selection-reason'
    | 'benchmark-management-type'
    | 'benchmark-request-reason'
    | 'benchmark-entity-type'
    | 'benchmark-report-type'
    | 'benchmark-language'
    | 'benchmark-method'
    | 'benchmark-type'
    | 'benchmark'

    // other
    | 'department';
