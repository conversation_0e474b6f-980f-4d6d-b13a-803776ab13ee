<!-- Dialog Overlay -->
<div
    *ngIf="selection"
    (mouseup)="clear()"
    class="fixed start-0 top-0 z-30 h-full w-full bg-black bg-opacity-20"
>
    <!-- Dialog-->
    <div
        (mouseup)="$event.stopPropagation()"
        class="relative top-14 mx-auto w-10/12 rounded bg-gray-500 bg-opacity-90 p-6 shadow-lg md:w-6/12 lg:w-4/12"
    >
        <!-- Title -->
        <div class="mb-4 flex items-center">
            <h1 class="text-xl font-bold text-white">
                {{ selection.title | translate }}
            </h1>
        </div>

        <!-- Content -->
        <div class="leading-relaxed text-white">
            <p>{{ selection.content }}</p>
        </div>
    </div>
</div>
