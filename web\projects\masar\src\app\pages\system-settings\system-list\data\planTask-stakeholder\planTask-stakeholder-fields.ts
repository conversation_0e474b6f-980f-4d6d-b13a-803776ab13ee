import { MnmFormField } from '@masar/shared/components';
import { Validators } from '@angular/forms';

export const planTaskStakeholderFields: MnmFormField[] = [
    {
        fields: [
            {
                name: 'id',
                hide: true,
            },
            {
                name: 'nameAr',
                type: 'text',
                label: 'translate_name_in_arabic',
                size: 6,
                validators: [
                    Validators.required,
                    Validators.maxLength(256),
                    Validators.minLength(3),
                ],
            },
            {
                name: 'nameEn',
                type: 'text',
                label: 'translate_name_in_english',
                size: 6,
                validators: [
                    Validators.maxLength(256),
                    Validators.minLength(3),
                ],
            },
        ],
    },
];
