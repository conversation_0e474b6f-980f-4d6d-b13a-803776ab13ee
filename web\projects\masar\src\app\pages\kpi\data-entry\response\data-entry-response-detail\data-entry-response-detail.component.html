<app-page [pageTitle]="'translate_data_entry_response_details' | translate">
    <div content class="grid gap-2 md:gap-4">
        <!-- Details -->
        <app-content
            [contentTitle]="'translate_data_entry_response_details' | translate"
        >
            <table content>
                <tbody>
                    <!-- Year -->
                    <tr>
                        <td class="whitespace-nowrap">
                            {{ 'translate_year' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="item">
                                {{ item.result.year }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Department -->
                    <tr>
                        <td class="whitespace-nowrap">
                            {{ 'translate_department' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="item">
                                {{ item.result.department.name }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Kpi -->
                    <tr>
                        <td class="whitespace-nowrap">
                            {{ 'translate_kpi' | translate }}
                        </td>
                        <td>
                            <div
                                *appWaitUntilLoaded="item"
                                class="flex flex-row items-center gap-2"
                            >
                                <app-kpi-code-badge
                                    [kpi]="item.result.kpi"
                                ></app-kpi-code-badge>
                                <span>{{ item.result.kpi.name }}</span>
                            </div>
                        </td>
                    </tr>

                    <!-- Stage -->
                    <tr>
                        <td class="whitespace-nowrap">
                            {{ 'translate_stage' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="item">
                                <div
                                    class="badge text-base"
                                    [ngClass]="{
                                        'badge-primary':
                                            item.transfers[0]?.assignee ===
                                            'data_entry',
                                        'badge-info':
                                            item.transfers[0]?.assignee ===
                                                'level1' ||
                                            item.transfers[0]?.assignee ===
                                                'level2',
                                        'badge-warning':
                                            item.transfers[0]?.assignee ===
                                            'kpi_manager',
                                        'badge-success':
                                            item.transfers[0]?.assignee ===
                                            'done'
                                    }"
                                >
                                    {{
                                        'translate_' +
                                            item.transfers[0]?.assignee
                                            | translate
                                    }}
                                </div>
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Time -->
                    <tr>
                        <td class="whitespace-nowrap">
                            {{ 'translate_time' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="item">
                                <!-- Shortcut for saving result to the variable `status` -->
                                <ng-container
                                    *ngIf="
                                        item.request.startTime
                                            | daysLeft
                                                : item.request.endTime as status
                                    "
                                >
                                    <span [class]="status.class">
                                        {{ status.label }}
                                    </span>
                                </ng-container>
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <!-- Details -->
        <app-content [contentTitle]="'translate_result' | translate">
            <!-- Content -->
            <mnm-form
                content
                *ngIf="periodFormState && item"
                [state]="periodFormState"
                [translateLabels]="true"
            >
                <div class="mt-2 text-center">
                    <button
                        type="submit"
                        (click)="save()"
                        [disabled]="isSaving || isMoving"
                        class="btn-lg btn btn-primary me-2"
                        *ngIf="actionMode === 'save-and-move'"
                    >
                        <app-loading-ring
                            *ngIf="isSaving"
                            class="me-2"
                        ></app-loading-ring>
                        <i class="fa-light fa-save me-2"></i>
                        <span>{{ 'translate_save_as_draft' | translate }}</span>
                    </button>
                </div>
            </mnm-form>
        </app-content>

        <!-- Action -->
        <app-content
            [contentTitle]="'translate_action' | translate"
            *ngIf="
                item &&
                item.canApprove &&
                item.transfers[0]?.assignee !== 'done'
            "
        >
            <!-- Content -->
            <mnm-form
                content
                *ngIf="moveFormState && item"
                [state]="moveFormState"
                [translateLabels]="true"
            >
                <div
                    class="mt-2 text-center"
                    *ngIf="actionMode === 'save-and-move'"
                >
                    <button
                        type="button"
                        class="btn-lg btn btn-success me-2"
                        (confirm)="save(true)"
                        [swal]="{
                            title:
                                'translate_save_and_send_this_item_question_mark'
                                | translate,
                            confirmButtonText: 'translate_yes' | translate,
                            cancelButtonText: 'translate_cancel' | translate,
                            showCancelButton: true,
                            showCloseButton: true
                        }"
                        [disabled]="
                            isMoving ||
                            (item.transfers[0]?.assignee === 'data_entry' &&
                                !moveFormState.group.get('users').value
                                    ?.length &&
                                !isReturnedFromKpiManager)
                        "
                    >
                        <app-loading-ring
                            *ngIf="isMoving === 'transfer'"
                            class="me-2"
                        ></app-loading-ring>
                        <i class="fa-light fa-save me-2"></i>
                        <span>{{ 'translate_save_and_send' | translate }}</span>
                    </button>
                </div>

                <div
                    class="mt-2 text-center"
                    *ngIf="actionMode === 'move-and-reject'"
                >
                    <button
                        type="submit"
                        class="btn-lg btn btn-success me-2"
                        (confirm)="move('transfer')"
                        [swal]="{
                            title:
                                'translate_move_this_item_question_mark'
                                | translate,
                            confirmButtonText: 'translate_yes' | translate,
                            cancelButtonText: 'translate_cancel' | translate,
                            showCancelButton: true,
                            showCloseButton: true
                        }"
                        [disabled]="
                            isMoving ||
                            (item.transfers[0]?.assignee === 'level1' &&
                                !moveFormState.group.get('users').value?.length)
                        "
                    >
                        <app-loading-ring
                            *ngIf="isMoving === 'transfer'"
                            class="me-2"
                        ></app-loading-ring>
                        <i class="fa fa-check me-2"></i>
                        <span>{{ 'translate_approve' | translate }}</span>
                    </button>

                    <button
                        type="submit"
                        class="btn-lg btn btn-danger me-2"
                        (confirm)="move('reject')"
                        [swal]="{
                            title:
                                'translate_reject_this_item_question_mark'
                                | translate,
                            confirmButtonText: 'translate_yes' | translate,
                            cancelButtonText: 'translate_cancel' | translate,
                            showCancelButton: true,
                            showCloseButton: true
                        }"
                        [disabled]="isMoving"
                    >
                        <app-loading-ring
                            *ngIf="isMoving === 'reject'"
                            class="me-2"
                        ></app-loading-ring>
                        <i class="fa fa-undo me-2"></i>
                        <span>{{ 'translate_reject' | translate }}</span>
                    </button>

                    <button
                        *ngIf="
                            item.transfers[0]?.assignee === 'level1' &&
                            (permissionList.kpiResultDirectApprove
                                | hasPermissionId
                                | async) === true &&
                            showDirectApprovalButton
                        "
                        type="submit"
                        [appTooltip]="
                            'translate_when_you_click_on_the_direct_approval_button_the_application_will_be_transferred_to_the_final_approval'
                                | translate
                        "
                        class="btn-lg btn btn-primary me-2"
                        (confirm)="move('forward_to_kpi_manager')"
                        [swal]="{
                            title:
                                'translate_approve_this_item_and_forward_to_kpi_manager_level_question_mark'
                                | translate,
                            confirmButtonText: 'translate_yes' | translate,
                            cancelButtonText: 'translate_cancel' | translate,
                            showCancelButton: true,
                            showCloseButton: true
                        }"
                        [disabled]="isMoving"
                    >
                        <app-loading-ring
                            *ngIf="isMoving === 'forward_to_kpi_manager'"
                            class="me-2"
                        ></app-loading-ring>
                        <i class="fa-light fa-thumbs-up me-2"></i>
                        <span>{{
                            'translate_direct_approval' | translate
                        }}</span>
                    </button>
                </div>
            </mnm-form>
        </app-content>

        <!-- Transfers History Table -->
        <app-transfers-table
            class="block"
            [transfers]="item?.transfers"
        ></app-transfers-table>
    </div>
</app-page>

<!-- Result periods (editable) -->
<ng-template #periodsRef>
    <app-periods-table
        *ngIf="item"
        mode="edit"
        type="data-entry-response"
        [actionMode]="actionMode"
        [targetId]="item.id"
        [kpiResult]="item.result"
        [periods]="item.periods"
        [formula]="item.result.formula"
        [measurementCycle]="item.request.measurementCycle"
        [currentTransferAssignee]="item.transfers[0].assignee"
        [readOnly]="readOnly"
        [isSubmitting]="isSaving"
        (forceSave)="save()"
        (forceRefresh)="getItem()"
    ></app-periods-table>
</ng-template>
