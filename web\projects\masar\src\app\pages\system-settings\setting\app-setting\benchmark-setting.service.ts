import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { miscFunctions, Result } from 'mnm-webapp';
import { environment } from '@masar/env/environment';
import { map } from 'rxjs/operators';
import { BenchmarkSetting } from '@masar/common/models/benchmark-Setting.model';

@Injectable()
export class BenchmarkSettingService {
    public constructor(private httpClient: HttpClient) {}

    public get(): Observable<BenchmarkSetting> {
        return this.httpClient
            .get<Result<BenchmarkSetting>>(
                `${environment.apiUrl}/setting/benchmark`
            )
            .pipe(map(res => res.extra));
    }

    public update(
        benchmarkSetting: BenchmarkSetting
    ): Observable<BenchmarkSetting> {
        return this.httpClient
            .put<Result<BenchmarkSetting>>(
                `${environment.apiUrl}/setting/benchmark`,
                miscFunctions.objectToURLParams({
                    benchmarkSetting: JSON.stringify(benchmarkSetting),
                })
            )
            .pipe(map(res => res.extra));
    }
}
