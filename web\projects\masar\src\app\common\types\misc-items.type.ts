import { Item, KpiType } from '@masar/common/models';

export type KpiMiscItem = Item & { type: KpiType; code: string };

export type StrategicGoalMiscItem = Item & {
    fromYear: string;
    toYear: string;
    yearRange: string;
};

export type GovernmentStrategicGoalMiscItem = Item & {
    fromYear: string;
    toYear: string;
    yearRange: string;
};

export type BenchmarkMiscItem = {
    id: string;
    entityType: string;
    entityName: string;
    partnerName: string;
    visitDate: string;
};

export type KpiResultTargetSettingMethodMiscItem = Item & {
    showExtraTargetFields: boolean;
};

export type RiskImpactMiscItem = Item & {
    degree: number;
    color: string;
};

export type RiskProbabilityMiscItem = Item & {
    degree: number;
    color: string;
};

export type ServiceMiscItem = Item & {
    nameDescription: string;
    mainServiceName: string;
    subServiceName: string;
    supplementaryServiceName: string;
};

export type MiscItem =
    | Item
    | StrategicGoalMiscItem
    | GovernmentStrategicGoalMiscItem
    | KpiMiscItem
    | RiskImpactMiscItem
    | RiskProbabilityMiscItem
    | KpiResultTargetSettingMethodMiscItem;
