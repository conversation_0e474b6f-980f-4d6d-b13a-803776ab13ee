import { OperationProcedureStep } from './operation-procedure-step.model';
import { LibraryFile } from './library-file.model';
import { Operation } from './operation.model';
import { OperationProcedureInCharge } from './operation-procedure-in-charge.model';

export interface OperationProcedure {
    id: string;
    code: string;
    name: string;
    nameAr: string;
    nameEn: string;
    goal: string;
    version: number;
    durationType: string;
    versionTime: Date;
    inCharge: OperationProcedureInCharge[];
    duration: number;
    type: 'manual' | 'electronic';
    risk: string;
    operation: Operation;
    flowchartFile: LibraryFile;
    steps: OperationProcedureStep[];
    periodicity: string;
    procedures: OperationProcedure[];
    isDurationCalculatedFromSteps: boolean;
}
