import { Department } from './department.model';
import { Kpi } from './kpi.model';
import { KpiMeasurementCycleType } from '../types';
import { User } from './user.model';

export interface KpiResultDataEntryRequest {
    id: string;
    year: number;
    startTime: Date;
    endTime: Date;
    measurementCycle: KpiMeasurementCycleType;
    periods: number[];
    departments: Department[];
    kpis: Kpi[];
    note: string;
    completionRate: number;
    path: 'default' | 'immediate';
    isSpecial: boolean;
    shouldExcludeResultsWithValues: boolean;
    kpiTypes: string[];
    createdTime: Date;
    createdBy: User;
}
