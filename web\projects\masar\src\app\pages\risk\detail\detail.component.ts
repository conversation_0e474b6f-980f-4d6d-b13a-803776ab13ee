import { Component, NgModuleRef, OnDestroy } from '@angular/core';
import { detailPageData } from './data';
import { Observable, Subject } from 'rxjs';
import { RiskService } from '@masar/pages/risk/risk.service';
import { Risk } from '@masar/common/models';
import { DetailPageData } from '@masar/features/dynamic-page/interfaces';
import { takeUntil } from 'rxjs/operators';
import { AppSettingFetcherService } from '@masar/core/services';
import { permissionList } from '@masar/common/constants';
import { ModalService } from 'mnm-webapp';
import { TranslateService } from '@ngx-translate/core';
import { RiskManagementProcedureNewComponent } from '@masar/pages/risk/components/risk-management-procedure-new/risk-management-procedure-new.component';

@Component({ templateUrl: './detail.component.html' })
export class DetailComponent implements OnDestroy {
    public data?: DetailPageData<Risk>;

    public isPlansEnabled: boolean = false;
    public item?: Risk;
    public permissionList = permissionList;

    private unsubscribeAll = new Subject();

    public constructor(
        private readonly riskService: RiskService,
        private readonly appSettingFetcherService: AppSettingFetcherService,
        private readonly modalService: ModalService,
        private readonly ngModuleRef: NgModuleRef<any>,
        private readonly translateService: TranslateService
    ) {
        this.syncRiskSettings();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public getItemCb(): (id: string) => Observable<Risk> {
        return (id: string) => {
            return this.riskService.get(id, false);
        };
    }

    public async openNewDialog(): Promise<void> {
        const subject = new Subject();

        const component = await this.modalService.show(
            RiskManagementProcedureNewComponent,
            {
                moduleRef: this.ngModuleRef,

                title: this.translateService.instant(
                    'translate_add_risk_management_procedure'
                ),

                beforeInit: c => {
                    c.riskId = this.item?.id;
                    c.item = null;
                },

                onDismiss: () => {
                    subject.next();
                    subject.complete();
                },
            }
        );

        component.create.pipe(takeUntil(subject)).subscribe(_ => {
            // Refresh the page data
            this.syncRiskSettings();
            this.modalService.dismiss(component);
        });

        component.update.pipe(takeUntil(subject)).subscribe(_ => {
            // Refresh the page data
            this.syncRiskSettings();
            this.modalService.dismiss(component);
        });
    }

    private syncRiskSettings(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(({ riskSetting }) => {
                const isFieldEnabled: (name: string) => boolean = (
                    name: string
                ) =>
                    riskSetting.optionalFields.find(x => x.name === name)
                        .isEnabled;

                this.isPlansEnabled = isFieldEnabled('plans');

                this.data = detailPageData(
                    isFieldEnabled('impact_details'),
                    isFieldEnabled('probability_details'),
                    isFieldEnabled('type')
                );
            });
    }
}
