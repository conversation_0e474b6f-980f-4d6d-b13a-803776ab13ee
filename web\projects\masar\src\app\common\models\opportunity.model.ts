import { Standard } from './standard.model';
import { Operation } from './operation.model';
import { Department } from './department.model';
import { Team } from './team.model';
import { User } from './user.model';
import { ImprovementOpportunityInputSource } from './improvement-opportunity-input-source.model';
import { ImprovementOpportunityInputCategory } from './improvement-opportunity-input-category.model';
import { Tournament } from './tournament.model';
import { Principle } from './principle.model';
import { Benchmark } from './benchmark.model';
import { Kpi } from './kpi.model';
import { FlowItem } from '@masar/features/flow/interfaces';
import { DefaultFlowState } from '@masar/features/flow/types';
import { Item } from '@masar/common/models/item.model';

export interface Opportunity extends FlowItem<DefaultFlowState> {
    id: string;
    inputSources: ImprovementOpportunityInputSource[];
    inputsDate?: Date;
    inputsDescription: string;
    inputCategory: ImprovementOpportunityInputCategory;
    priority: string;
    cost: number;
    applicationEffect: string;
    risks: string;
    actionDescription: string;
    closingProjectName: string;
    closingStartDate?: Date;
    plannedClosingDate?: Date;
    completionRate: string;
    result: string;
    operations: Operation[];
    owningDepartment: Department;
    assignedDepartment: Department;
    assignedTeam: Team;
    assignedUser: User;
    assigned: string;
    tournament?: Tournament;
    principles: Principle[];
    standards: Standard[];
    benchmarks: Benchmark[];
    benchmarkCount: number;
    mainReason: string;
    statusNumber: number;
    kpis: Kpi[];
    opportunityRisks: Item[];
}
