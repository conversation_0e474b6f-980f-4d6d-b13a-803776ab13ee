import { ResourceOptionalField } from './resource-optional-field.model';
import { KpiSettingAchievementColorCoding } from './kpi-setting-achievement-color-coding.model';
import { KpiResultReminderConfiguration } from '@masar/common/models/kpi-result-reminder-configuration.model';

export interface KpiSetting {
    canResultAchievementTakeNegativeValues: boolean;
    canHaveEntityForInternalSources: boolean;
    isGraphTicksEnabled: boolean;
    achievementColorCodings: KpiSettingAchievementColorCoding[];
    resultReminderConfigurations: KpiResultReminderConfiguration[];
    isKpiResultValueBreakdownEnabled: boolean;
    isKpiResultDataEntryResponseAttachmentRequired: boolean;
    optionalFields: ResourceOptionalField[];
    isDecimalPlacesEnabled: boolean;
    showUsersWithSignatureApprovalInTopLevels: boolean;
}
