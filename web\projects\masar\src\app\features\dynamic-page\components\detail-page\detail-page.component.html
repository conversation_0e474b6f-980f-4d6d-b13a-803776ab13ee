<app-page [pageTitle]="data.title | translate">
    <!-- Tools -->
    <ng-container tools>
        <!-- Flow -->
        <app-flow-move-button
            *ngIf="
                item &&
                data.dynamicFlow &&
                (!data.dynamicFlow.show ||
                    (item | isNotEqualFalse : data.dynamicFlow.show)) &&
                ($any(item) | showFlowMoveButtonForItem)
            "
            [itemType]="data.dynamicFlow.type"
            [item]="$any(item)"
            (transfer)="refresh()"
        ></app-flow-move-button>

        <!-- New -->
        <a
            *appHasPermissionId="data.permissions.write"
            class="btn btn-sm flex items-center gap-1 rounded-md bg-green-500 px-3 py-2 text-white transition hover:bg-green-600"
            [appTooltip]="'translate_add_new' | translate"
            [routerLink]="['', data.route, 'new']"
        >
            <i class="fas fa-plus"></i>
            <span class="hidden md:inline">
                {{ 'translate_add_new' | translate }}
            </span>
        </a>

        <!-- Edit -->
        <ng-container *ngIf="item">
            <a
                *appHasPermissionId="data.permissions.write"
                class="btn btn-sm flex items-center gap-1 rounded-md bg-blue-500 px-3 py-2 text-white transition hover:bg-blue-600"
                [routerLink]="['', data.route, 'edit', item.id]"
                [appTooltip]="'translate_edit' | translate"
            >
                <i class="fas fa-edit"></i>
                <span class="hidden md:inline">
                    {{ 'translate_edit' | translate }}
                </span>
            </a>
        </ng-container>
        <!-- Projected tools from child components -->
        <ng-content select="[tools]"></ng-content>

        <!-- Back -->
        <app-go-back-button [targetRoute]="data.route"></app-go-back-button>
    </ng-container>

    <div content class="grid grid-cols-1 gap-4">
        <ng-container *ngIf="item && (data | getAlerts : item) as alerts">
            <div *ngIf="alerts.length" class="mb-2 flex flex-col gap-2">
                <app-alert
                    *ngFor="let alert of alerts"
                    [id]="alert.id"
                    [mode]="alert.mode"
                    [label]="alert.label"
                    [description]="alert.description"
                >
                </app-alert>
            </div>
        </ng-container>

        <app-detail-sections
            [sections]="sections"
            [item]="item"
        ></app-detail-sections>

        <ng-content></ng-content>

        <!-- Approvals history -->
        <app-flow-transaction-history
            *ngIf="item && data.dynamicFlow && data.dynamicFlow.type as type"
            [itemId]="item.id"
            [itemType]="type"
        ></app-flow-transaction-history>
    </div>
</app-page>
