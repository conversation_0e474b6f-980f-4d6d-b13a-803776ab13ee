import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { FlowTransaction } from '@masar/common/models';
import { miscFunctions, Result } from 'mnm-webapp';
import { environment } from '@masar/env/environment';
import { FlowItemType } from '@masar/features/flow/types/flow-item-type.type';
import { map } from 'rxjs/operators';

@Injectable()
export class FlowService {
    public constructor(private httpClient: HttpClient) {}

    public move(
        id: string,
        state: string,
        note: string,
        itemType: FlowItemType
    ): Observable<any> {
        return this.httpClient
            .post<Result>(
                `${environment.apiUrl}/${itemType}/flow/move/${id}/${state}`,
                miscFunctions.objectToURLParams({ note })
            )
            .pipe(map(res => res.extra));
    }

    public history(
        id: string,
        itemType: FlowItemType
    ): Observable<FlowTransaction[]> {
        return this.httpClient
            .get<Result<FlowTransaction[]>>(
                `${environment.apiUrl}/${itemType}/flow/transaction/${id}`
            )
            .pipe(map(res => res.extra));
    }
}
