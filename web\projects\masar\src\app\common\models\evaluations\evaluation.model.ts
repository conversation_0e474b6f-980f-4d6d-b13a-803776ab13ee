import { EvaluationStandard } from '@masar/common/models/evaluations/evaluation-standard.model';
import { EvaluationType } from '@masar/features/evaluate/types';
import { EvaluationScoreBand } from '@masar/common/models';

export interface Evaluation {
    id: string;
    name: string;
    description: string;
    type: EvaluationType;
    standardCount: number;
    entityCount: number;
    standards: EvaluationStandard[];
    scoreBands: EvaluationScoreBand[];
    isDefault: boolean;
    isDisabled: boolean;
}
