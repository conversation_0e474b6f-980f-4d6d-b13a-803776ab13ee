import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';
import { StrategicPlan } from '@masar/common/models';
import { miscFunctions, Result } from 'mnm-webapp';

@Injectable({
    providedIn: 'root',
})
export class StrategicPlanService {
    private readonly baseUrl = `${environment.apiUrl}/strategic-plan`;

    public constructor(private http: HttpClient) {}

    public create(data: StrategicPlan): Observable<StrategicPlan> {
        return this.http
            .post<Result<StrategicPlan>>(
                this.baseUrl,
                miscFunctions.objectToURLParams({
                    'strategic': JSON.stringify(data),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(data: StrategicPlan): Observable<StrategicPlan> {
        return this.http
            .put<Result<StrategicPlan>>(
                this.baseUrl,
                miscFunctions.objectToURLParams({
                    'strategic-plan': JSON.stringify(data),
                })
            )
            .pipe(map(result => result.extra));
    }

    public get(id: string): Observable<StrategicPlan> {
        return this.http
            .get<Result<StrategicPlan>>(`${this.baseUrl}/${id}`)
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<void> {
        return this.http.delete<void>(`${this.baseUrl}/${id}`);
    }

    public getList(): Observable<StrategicPlan[]> {
        return this.http
            .get<Result<StrategicPlan[]>>(this.baseUrl)
            .pipe(map(result => result.extra));
    }

    public getDashboardData(year: number): Observable<StrategicPlan> {
        return this.http
            .get<Result<StrategicPlan>>(`${this.baseUrl}/${year}`)
            .pipe(
                map(result =>
                    this.transformApiResponseToStrategicPlan(result.extra)
                )
            );
    }

    private transformApiResponseToStrategicPlan(
        apiResponse: StrategicPlan
    ): StrategicPlan {
        return {
            id: apiResponse.id,
            name: apiResponse.name,
            nameAr: apiResponse.nameAr,
            nameEn: apiResponse.nameEn,
            startYear: apiResponse.startYear,
            endYear: apiResponse.endYear,
            period: `${apiResponse.startYear} - ${apiResponse.endYear}`,
            vision: apiResponse.vision,
            mission: apiResponse.mission,
            pillars: apiResponse.pillars.map(pillar => ({
                id: pillar.id,
                name: pillar.name,
                iconClass: pillar.iconClass,
                goals: pillar.goals,
            })),
            goals: apiResponse.goals,
            values: apiResponse.values,
            isActive: apiResponse.isActive,
            visionAr: apiResponse.visionAr,
            visionEn: apiResponse.visionEn,
            missionAr: apiResponse.missionAr,
            missionEn: apiResponse.missionEn,
        };
    }
}
