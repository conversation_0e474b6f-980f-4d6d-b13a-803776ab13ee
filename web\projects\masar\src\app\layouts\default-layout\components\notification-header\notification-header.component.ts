import { Component, OnInit } from '@angular/core';
import { MyNotificationService } from '@masar/pages/my-notification/my-notification.service';
import { Notification } from '@masar/common/models';

@Component({
    selector: 'app-notification-header',
    templateUrl: './notification-header.component.html',
    providers: [MyNotificationService],
})
export class NotificationHeaderComponent implements OnInit {
    public notifications: Notification[] = [];
    public marqueeStyle: any;
    public constructor(
        private readonly myNotificationService: MyNotificationService
    ) {}

    public ngOnInit(): void {
        this.showUnreadNotifications();
    }
    public showUnreadNotifications(): void {
        this.myNotificationService
            .list('', null, null, true, 0, 40)
            .subscribe(notifications => {
                this.notifications = notifications.items;
                this.marqueeStyle = {
                    'animation-duration': this.notifications.length * 6 + 's',
                };
            });
    }
    public navigateTo(notification: Notification): void {
        this.myNotificationService.navigateTo(notification);
        this.showUnreadNotifications();
    }
}
