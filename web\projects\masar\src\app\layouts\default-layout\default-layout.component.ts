import {
    Compo<PERSON>,
    ElementRef,
    HostList<PERSON>,
    OnDestroy,
    OnInit,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import { Subject } from 'rxjs';
import { AppSettingFetcherService } from '@masar/core/services';
import {
    ActivatedRoute,
    NavigationEnd,
    NavigationError,
    Router,
} from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { navItems } from '@masar/layouts/default-layout/constants';
import { DefaultLayoutService } from '@masar/layouts/default-layout/services/default-layout.service';

@Component({
    templateUrl: './default-layout.component.html',
    styleUrls: ['./default-layout.component.scss'],
    encapsulation: ViewEncapsulation.None,
    providers: [DefaultLayoutService],
})
export class DefaultLayoutComponent implements OnInit, OnDestroy {
    @ViewChild('contentWrapper') public contentWrapper: ElementRef;

    public menuLabel = '';
    public title: string;
    public name = '';
    public date = new Date();
    public breadcrumb: string[] = [];
    public icon = '';

    // Used by the sidebar to determine
    // if we are dealing with a smaller
    // screen.
    public isSmallScreen = false;
    public isSidebarCollapsed = false;
    public navItems = navItems;

    // Throttles monitoring
    // window resize events for
    // performance.
    private isThrottled = false;

    private unsubscribeAll = new Subject();

    public constructor(
        public appSettingFetcherService: AppSettingFetcherService,
        public defaultLayoutService: DefaultLayoutService,
        private router: Router,
        private route: ActivatedRoute
    ) {
        this.isSidebarCollapsed =
            appSettingFetcherService.snapshot.dashboardSetting
                .selectedDashboard === 'style_1';

        let isSystemListRoute = false;
        this.router.events
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(evt => {
                if (
                    evt instanceof NavigationEnd ||
                    evt instanceof NavigationError
                ) {
                    // Shuts down the main sidebar to make space
                    // for the settings sidebar.
                    // The reason why `isSystemListRoute` is reset
                    // until the page is refreshed is to prevent spamming
                    // the sidebar collapse action.
                    // By checking against false, we ensured that the sidebar
                    // collapse action only occurs once per session,
                    // improving the user experience by minimizing
                    // unnecessary UI changes during internal
                    // navigation within the system.
                    if (
                        (evt.url.includes('system-list') ||
                            evt.url.includes('system-setting')) &&
                        !isSystemListRoute
                    ) {
                        this.isSidebarCollapsed = true;
                        isSystemListRoute = true;
                    }

                    this.contentWrapper?.nativeElement.scrollTo({
                        top: 0,
                        left: 0,
                        behavior: 'smooth',
                    });
                }
            });

        this.detectSmallScreen();
    }

    @HostListener('window:resize')
    private detectSmallScreen(): void {
        if (this.isThrottled) {
            return;
        }
        this.isThrottled = true;
        this.isSmallScreen = window.innerWidth <= 768;
        if (this.isSmallScreen) this.isSidebarCollapsed = true;
        this.isThrottled = false;
    }

    public ngOnInit(): void {
        this.applyRouteProperties();

        this.router.events
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(evt => {
                if (!(evt instanceof NavigationEnd)) {
                    return;
                }

                this.applyRouteProperties();
            });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    private applyRouteProperties(): void {
        const routeData = this.getRouteData();
        this.menuLabel = routeData.menuLabel == null ? '' : routeData.menuLabel;
        this.title = routeData.title;
        this.icon = routeData.icon;
        this.breadcrumb = routeData.breadcrumb;
    }

    private getRouteData(): any {
        let data = {};
        let current = this.route.root;
        while (current) {
            const d = current.snapshot.data;
            data = Object.assign(data, d);
            current = current.firstChild;
        }

        return data;
    }
}
