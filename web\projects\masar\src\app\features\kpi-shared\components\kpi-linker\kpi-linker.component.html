<app-content contentTitle="{{ 'translate_kpis' | translate }}">
    <!-- Tools -->
    <ng-container tools>
        <ng-container *appHasPermissionId="requiredPermissionForLinking">
            <button
                (click)="showKpiDialog()"
                *ngIf="mode === 'default'"
                class="btn btn-sm btn-outline-white"
            >
                <i class="fa-light fa-plus"></i>
                <span> {{ 'translate_link_with_kpis' | translate }}</span>
            </button>
        </ng-container>
    </ng-container>

    <!-- Content -->
    <app-kpi-list-full
        content
        view="with_result"
        [shownFilters]="['name']"
    ></app-kpi-list-full>
</app-content>

<!-- Template for the unlinking button -->
<ng-template #controlColumnTemplateRef let-item="item">
    <button
        (confirm)="unlinkKpi(item.id)"
        *appHasPermissionId="requiredPermissionForUnlinking"
        [disabled]="currentlyProcessing.has(item.id)"
        [swal]="{
            title: 'translate_delete_this_item_question_mark' | translate,
            confirmButtonText: 'translate_yes' | translate,
            cancelButtonText: 'translate_cancel' | translate,
            showCancelButton: true,
            showCloseButton: true
        }"
        class="btn btn-sm btn-danger"
    >
        <i class="fas fa-trash"></i>
    </button>
</ng-template>

<!-- Template for select button -->
<ng-template let-item="item" #selectButtonTemplate>
    <button
        (click)="linkKpi(item)"
        [disabled]="currentlyProcessing.has(item.id)"
        class="btn btn-sm btn-info"
    >
        <i class="fa fa-check"></i>
    </button>
</ng-template>
