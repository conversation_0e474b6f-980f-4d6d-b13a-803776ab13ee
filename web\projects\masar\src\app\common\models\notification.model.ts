export interface Notification {
    id: string;
    creationTime: Date;
    title: string;
    description: string;
    isRead: boolean;
    readCount: number;
    userCount: number;
    targetType?:
        | 'plan'
        | 'kpi_result_data_entry_response'
        | 'plan_subtask'
        | 'kpi_result_period'
        | 'partnership_contract'
        | 'operation_enhancement'
        | 'signup_request'
        | 'statistical_report_result'
        | 'signup_request'
        | 'improvement_opportunity';
    targetId?: string;
    targetMetadata?: string;
}
