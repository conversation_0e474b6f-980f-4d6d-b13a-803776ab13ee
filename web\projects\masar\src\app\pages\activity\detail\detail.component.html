<app-page pageTitle="{{ 'translate_activity_details' | translate }}">
    <!-- Tools -->
    <ng-container tools>
        <!-- List -->
        <a class="btn btn-sm btn-success" [routerLink]="['', 'activity']">
            <i class="fa-light fa-share"></i>
            <span class="hidden md:inline">
                {{ 'translate_activity_list' | translate }}
            </span>
        </a>

        <!-- New -->
        <a
            class="btn btn-sm btn-primary"
            [routerLink]="['', 'activity', 'new']"
        >
            <i class="fa-light fa-plus"></i>
            <span class="hidden md:inline">
                {{ 'translate_add_new' | translate }}
            </span>
        </a>

        <!-- Edit -->
        <a
            class="btn btn-sm btn-info"
            *ngIf="activity"
            [routerLink]="['', 'activity', 'edit', activity.id]"
            [appTooltip]="'translate_edit' | translate"
        >
            <i class="fa-light fa-edit"></i>
            <span class="hidden md:inline">
                {{ 'translate_edit' | translate }}
            </span>
        </a>
    </ng-container>

    <div content class="grid grid-cols-1 gap-3">
        <!-- Details -->
        <app-content>
            <table content>
                <tbody>
                    <!-- Activity name -->
                    <tr>
                        <td class="whitespace-nowrap">
                            {{ 'translate_activity_name' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="activity">
                                {{ activity.name }}
                            </ng-container>
                        </td>
                    </tr>

                    <!-- Year -->
                    <tr>
                        <td>
                            {{ 'translate_year' | translate }}
                        </td>
                        <td>
                            <ng-container *appWaitUntilLoaded="activity">
                                {{ activity.year }}
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <!-- Linked Innovations -->
        <app-innovation-linker
            class="mb-5"
            [listLinkedInnovationCallback]="listLinkedInnovations()"
            [linkingCallback]="linkInnovation()"
            [unlinkingCallback]="unlinkInnovation()"
        >
        </app-innovation-linker>
    </div>
</app-page>
