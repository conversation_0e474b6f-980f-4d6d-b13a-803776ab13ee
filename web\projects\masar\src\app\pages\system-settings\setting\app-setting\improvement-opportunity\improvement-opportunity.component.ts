import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { ImprovementOpportunitySettingService } from '../improvement-opportunity-setting.service';
import { ImprovementOpportunitySetting } from '@masar/common/models/improvement-opportunity-setting.model';

@Component({
    selector: 'app-improvement-opportunity',
    templateUrl: './improvement-opportunity.component.html',
})
export class ImprovementOpportunityComponent {
    public improvementOpportunitySetting: ImprovementOpportunitySetting;

    public isSubmitting = false;

    public constructor(
        private improvementOpportunitySettingService: ImprovementOpportunitySettingService,
        private translateService: TranslateService,
        private notificationService: NotificationService
    ) {
        improvementOpportunitySettingService
            .get()
            .subscribe(item => (this.improvementOpportunitySetting = item));
    }

    public save(): void {
        if (this.isSubmitting) return;

        this.isSubmitting = true;

        this.improvementOpportunitySettingService
            .update(this.improvementOpportunitySetting)
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(() => {
                this.notificationService.notifySuccess(
                    this.translateService.instant(
                        'translate_item_updated_successfully'
                    )
                );
            });
    }
}
