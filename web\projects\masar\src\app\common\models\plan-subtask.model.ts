import { Department } from './department.model';
import { PlanTask } from './plan-task.model';
import { Team } from './team.model';
import { User } from './user.model';
import { PlanSubsubtask } from './plan-subsubtask.model';
import { PlanUserAbility } from './plan-user-ability.model';

export interface PlanSubtask {
    id: string;
    name: string;
    // libraryFile: LibraryFile;
    assignedDepartment: Department;
    assignedTeam: Team;
    assignedUser: User;
    secondaryAssignedDepartment: Department;
    isApproved: boolean;
    weight: number;
    progress: number;
    expectedProgress: number;
    from: Date;
    to: Date;
    task: PlanTask;
    subsubtasks: PlanSubsubtask[];
    isPlanInitiallyApproved: boolean;
    isRepeated: boolean;
    planUserAbility: PlanUserAbility;
}
