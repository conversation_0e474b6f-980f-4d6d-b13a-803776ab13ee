<app-page
    [pageTitle]="
        (strategicPlan?.name || '') + ' ' + (strategicPlan?.period || '')
    "
>
    <!-- Content -->
    <ng-container content>
        <!-- Vision Section (Triangle Shape) -->
        <div class="relative mx-auto mb-2 max-w-6xl">
            <div class="relative">
                <!-- Vision Triangle -->
                <div
                    class="cursor-pointer truncate rounded-md bg-green-600 px-4 py-8 text-center text-white transition-colors hover:bg-green-700"
                    style="clip-path: polygon(0% 100%, 50% 0%, 100% 100%)"
                    (click)="strategicSectionDialog.set(visionData!)"
                >
                    <div class="pt-4">
                        <h2 class="mb-2 text-lg font-bold">
                            {{ 'translate_vision' | translate }}
                        </h2>
                        <span
                            class="inline-block w-1/2 truncate px-8 text-sm"
                            >{{ strategicPlan?.vision }}</span
                        >
                    </div>
                </div>
            </div>
        </div>

        <!-- Mission Section (Trapezoid Shape) -->
        <div class="relative mx-auto mb-2 max-w-6xl">
            <div
                class="cursor-pointer rounded-md bg-gray-600 px-4 py-6 text-center text-white transition-colors hover:bg-gray-700"
                (click)="strategicSectionDialog.set(missionData!)"
            >
                <h2 class="mb-2 text-lg font-bold">
                    {{ 'translate_mission' | translate }}
                </h2>
                <p class="px-4 text-sm">{{ strategicPlan?.mission }}</p>
            </div>
        </div>

        <!-- Strategic Objectives Container -->
        <div
            class="mx-auto max-w-6xl rounded-md border-4 border-green-600 bg-gray-100 p-6"
        >
            <!-- Title -->
            <div class="mb-6 text-center">
                <h2 class="text-xl font-bold text-black">
                    {{ 'translate_strategic_objectives' | translate }}
                </h2>
            </div>

            <div class="space-y-4">
                <!-- Dynamic Pillar Sections -->
                <div
                    *ngFor="let pillar of pillars; trackBy: trackByPillarId"
                    class="rounded-md border-2 border-green-600 bg-white p-1"
                >
                    <div class="flex">
                        <!-- Icon and Label - Fixed Width -->
                        <div
                            class="border--2 flex w-32 flex-shrink-0 flex-col items-center justify-center border-green-600 p-3"
                        >
                            <i
                                [class]="
                                    'mb-1 text-xl text-gray-600 ' +
                                    pillar.iconClass
                                "
                            ></i>
                            <div class="text-center">
                                <div
                                    class="text-xs font-bold leading-tight text-green-600"
                                >
                                    {{ pillar.displayName }}
                                </div>
                            </div>
                        </div>

                        <!-- Divider -->
                        <div class="me-3 w-px bg-green-600"></div>

                        <!-- Goals - Responsive Flex Layout -->
                        <div
                            class="flex flex-1 flex-wrap justify-center gap-2 p-1"
                        >
                            <div
                                *ngFor="
                                    let goal of pillar.goals;
                                    trackBy: trackByGoalId
                                "
                                class="flex min-h-[80px] w-32 flex-shrink-0 items-center justify-center rounded-md border border-green-700 bg-green-600 p-4 text-center text-white sm:w-36 md:w-40"
                            >
                                <p class="text-sm font-medium">
                                    {{ goal.displayName }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Standalone Goals Section (Goals without Pillars) -->
                <div
                    *ngIf="hasStandaloneGoals()"
                    class="mt-4 rounded-md border-2 border-green-600 bg-white p-1"
                >
                    <div class="flex">
                        <!-- Icon and Label - Fixed Width -->
                        <div
                            class="flex w-32 flex-shrink-0 flex-col items-center justify-center border-green-600 p-3"
                        >
                            <div class="text-center">
                                <div
                                    class="text-xs font-bold leading-tight text-green-600"
                                >
                                    {{ '' }}
                                </div>
                            </div>
                        </div>

                        <!-- Goals - Responsive Flex Layout -->
                        <div
                            class="flex flex-1 flex-wrap justify-center gap-2 p-1"
                        >
                            <div
                                *ngFor="
                                    let goal of standaloneGoals;
                                    trackBy: trackByGoalId
                                "
                                class="flex min-h-[80px] w-32 flex-shrink-0 items-center justify-center rounded-md bg-green-600 p-4 text-center text-white sm:w-36 md:w-40"
                            >
                                <p class="text-sm font-medium">
                                    {{ goal.displayName }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ng-container>
</app-page>

<!-- Values Section -->
<div class="mx-auto my-5 max-w-6xl">
    <div class="mb-4 rounded-md bg-gray-300 py-2 text-center">
        <h2 class="text-lg font-bold text-black">
            {{ 'translate_values' | translate }}
        </h2>
    </div>
    <div class="flex flex-wrap gap-1">
        <div
            *ngFor="let value of strategicPlan?.values; trackBy: trackByValue"
            class="min-w-0 flex-1 rounded-md border border-green-500 bg-gray-300 p-4 text-center"
        >
            <p class="text center text-xs font-medium">{{ value.name }}</p>
        </div>
    </div>
</div>

<!-- Strategic Section Dialog -->
<app-strategic-section-dialog
    #strategicSectionDialog
></app-strategic-section-dialog>
