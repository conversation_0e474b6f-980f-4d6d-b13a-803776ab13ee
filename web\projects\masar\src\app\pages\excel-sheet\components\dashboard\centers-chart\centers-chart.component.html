<div class="mb-4 flex items-center justify-between">
    <h1 class="text-xl font-bold">عدد المتعاملين شهريا</h1>

    <!-- Center Field -->
    <select
        name="year"
        id="year"
        class="bg-blur rounded-full text-center"
        [(ngModel)]="selectedYear"
        (change)="setBarCharData()"
    >
        <option *ngFor="let year of years" [value]="year">{{ year }}</option>
    </select>

    <!-- Center Field -->
    <select
        name="center"
        id="center"
        class="bg-blur rounded-full text-center"
        [(ngModel)]="selectedCenterId"
        (change)="setBarCharData()"
    >
        <option value="0" selected disabled>المراكز</option>
        <option *ngFor="let center of centers" [value]="center.id">
            {{ center.name }}
        </option>
    </select>
</div>

<div class="mb-4">
    <div class="bg-blur relative overflow-x-auto">
        <table
            class="w-full text-center text-sm text-gray-500"
            aria-label="عدد المتعاملين"
        >
            <thead
                class="border-b border-white text-xs uppercase text-gray-700"
            >
                <tr>
                    <th
                        scope="col"
                        class="border-e border-gray-300 bg-white px-3 py-2 last-of-type:border-e-0"
                        *ngFor="let month of months"
                    >
                        {{ month.label }}
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td
                        *ngFor="let month of monthsMistakePercentage"
                        [ngStyle]="{ backgroundColor: month.color }"
                        class="whitespace-nowrap border-s px-3 py-2 font-medium text-gray-900 last-of-type:border-s-0"
                        dir="ltr"
                    >
                        {{ month.value }}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<div style="display: block">
    <canvas
        baseChart
        [chartType]="chartType"
        [datasets]="chartData"
        [labels]="chartLabels"
        [options]="chartOptions"
        [plugins]="chartPlugins"
        [legend]="chartLegend"
    >
    </canvas>
</div>
