import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { StrategicPlanRoutingModule } from './strategic-plan-routing.module';
import { StrategicPlanComponent } from '@masar/pages/strategic-plan/strategic-plan.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { StrategicSectionDialogComponent } from './components/strategic-section-dialog/strategic-section-dialog.component';
import { SharedModule } from '@masar/shared/shared.module';
import { TranslationModule } from '@ng-omar/translation';
import { MasarModule } from '@masar/features/masar/masar.module';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';

@NgModule({
    declarations: [
        StrategicPlanComponent,
        DashboardComponent,
        StrategicSectionDialogComponent,
    ],
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        RouterModule,
        StrategicPlanRoutingModule,
        SharedModule,
        TranslationModule,
        MasarModule,
        SweetAlert2Module,
    ],
})
export class StrategicPlanModule {}
