import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MnmFormState } from '@masar/shared/components';
import { ActivatedRoute } from '@angular/router';
import { NotificationService } from 'mnm-webapp';
import { FormBuilder } from '@angular/forms';
import { fields } from './fields';
import { finalize, first } from 'rxjs/operators';
import { StrategicPillarService } from '../services/strategic-pillar.service';
import { TranslateService } from '@ngx-translate/core';
import { HelperService } from '@masar/core/services';
import { Subject } from 'rxjs';
import { StrategicPillar } from '@masar/common/models/strategic-pillar.model';

@Component({
    selector: 'app-new',
    templateUrl: './strategic-pillar-new.component.html',
})
export class StrategicPillarNewComponent implements OnInit, OnDestroy {
    public isSubmitting = false;
    public mode: 'new' | 'edit';
    public formState: MnmFormState;

    private unsubscribeAll = new Subject();
    private strategicPillarId: string;

    public constructor(
        private notificationService: NotificationService,
        private strategicPillarService: StrategicPillarService,
        private translateService: TranslateService,
        private activatedRoute: ActivatedRoute,
        private readonly helperService: HelperService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[1].path) {
                case 'new':
                    this.mode = 'new';
                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            this.strategicPillarId = params['id'];
                            this.strategicPillarService
                                .get(this.strategicPillarId, true)
                                .subscribe(item => {
                                    this.fillForm(item);
                                });
                        });
                    break;
            }
        });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const value = this.formState.group.getRawValue();

        const observable =
            this.mode === 'new'
                ? this.strategicPillarService.create(value)
                : this.strategicPillarService.update(value);

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(strategicPillar => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'system-list', 'strategic-plans', 'strategic-pillar'],
                    strategicPillar.id
                );
            });
    }

    private fillForm(item: StrategicPillar): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }
}
