import { FlowItem } from '@masar/features/flow/interfaces';

export const extractAvailableStates = (item: FlowItem): string[] => {
    if (!item?.flowActionAvailability) {
        return [];
    }

    const states = Object.keys(item.flowActionAvailability).filter(
        x => item.flowActionAvailability[x]
    );

    // Move draft state to the end if it exists
    const draftIndex = states.indexOf('draft');
    if (draftIndex !== -1) {
        states.splice(draftIndex, 1);
        states.push('draft');
    }

    return states;
};
