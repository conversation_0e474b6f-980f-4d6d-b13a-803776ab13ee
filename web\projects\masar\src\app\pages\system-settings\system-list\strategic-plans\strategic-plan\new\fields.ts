import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },

    {
        fields: [
            {
                name: 'nameAr',
                type: 'text',
                label: 'translate_strategic_plan_name_in_arabic',
                size: 6,
                validators: [Validators.required],
            },

            {
                name: 'nameEn',
                type: 'text',
                label: 'translate_strategic_plan_name_in_english',
                size: 6,
            },
        ],
    },

    {
        fields: [
            {
                name: 'startYear',
                type: 'number',
                label: 'translate_start_year',
                size: 6,
                validators: [Validators.required, Validators.min(2000)],
            },

            {
                name: 'endYear',
                type: 'number',
                label: 'translate_end_year',
                size: 6,
                validators: [Validators.required, Validators.min(2000)],
            },
        ],
    },

    {
        fields: [
            {
                name: 'visionAr',
                type: 'textarea',
                label: 'translate_vision_in_arabic',
                size: 6,
            },

            {
                name: 'visionEn',
                type: 'textarea',
                label: 'translate_vision_in_english',
                size: 6,
            },
        ],
    },

    {
        fields: [
            {
                name: 'missionAr',
                type: 'textarea',
                label: 'translate_message_in_arabic',
                size: 6,
            },

            {
                name: 'missionEn',
                type: 'textarea',
                label: 'translate_message_in_english',
                size: 6,
            },
        ],
    },

    {
        sectionTitle: 'translate_strategic_values',
        isSectionRequired: true,
        fields: [
            {
                name: 'values',
            },
        ],
    },
];
