import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { miscFunctions, Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';
import { Innovation } from '@masar/common/models';

@Injectable()
export class InnovationService {
    public constructor(private httpClient: HttpClient) {}

    public create(innovation: Innovation): Observable<Innovation> {
        return this.httpClient
            .post<Result<Innovation>>(
                environment.apiUrl + '/innovation',
                miscFunctions.objectToURLParams({
                    innovation: JSON.stringify(innovation),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(innovation: Innovation): Observable<Innovation> {
        return this.httpClient
            .put<Result<Innovation>>(
                environment.apiUrl + '/innovation',
                miscFunctions.objectToURLParams({
                    innovation: JSON.stringify(innovation),
                })
            )
            .pipe(map(result => result.extra));
    }

    public get(id: string, forEdit: boolean = false): Observable<Innovation> {
        return this.httpClient
            .get<Result<Innovation>>(environment.apiUrl + '/innovation/' + id, {
                params: new HttpParams().append('forEdit', `${forEdit}`),
            })
            .pipe(map(result => result.extra));
    }

    public downloadAttachment(id: string): Observable<Blob> {
        return this.httpClient
            .get(`${environment.apiUrl}/innovation/file/download?id=${id}`, {
                responseType: 'blob',
            })
            .pipe(map(res => res));
    }
}
