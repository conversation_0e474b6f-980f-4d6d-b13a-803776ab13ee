import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { MnmFormState } from '@masar/shared/components';
import { finalize } from 'rxjs/operators';
import { fields } from './fields';
import { AttachmentFile, AttachmentOptions } from '../interfaces';
import { SharedAttachmentService } from '@masar/features/attachment';
import { AppSettingFetcherService } from '@masar/core/services';

@Component({ templateUrl: './attachment-new.component.html' })
export class AttachmentNewComponent {
    @Input() public options: AttachmentOptions;

    @Output() public created = new EventEmitter<AttachmentFile>();

    public formState: MnmFormState;

    public isSubmitting = false;

    public constructor(
        private readonly translateService: TranslateService,
        private readonly notificationService: NotificationService,
        private readonly sharedAttachmentService: SharedAttachmentService,
        appSettingFetcherService: AppSettingFetcherService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(appSettingFetcherService), fb);
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) return;

        this.isSubmitting = true;

        this.sharedAttachmentService
            .createAttachment(
                this.options.id,
                this.formState.group.getRawValue(),
                this.options.endPoint
            )
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(item => {
                const str = this.translateService.instant(
                    'translate_item_added_successfully'
                );
                this.notificationService.notifySuccess(str);
                this.created.emit(item);
            });
    }
}
