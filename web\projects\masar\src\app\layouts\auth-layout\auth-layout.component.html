<div
    class="m-0 h-full overflow-hidden bg-cover bg-center bg-no-repeat"
    [ngStyle]="{
        backgroundImage: !loginBackgroundBlobUrl
            ? 'none'
            : 'url(\'' + loginBackgroundBlobUrl + '\')'
    }"
>
    <div
        class="relative mx-auto flex h-full w-11/12 items-center justify-center md:w-96"
    >
        <div
            [@card]="animationState"
            class="flex max-h-[80%] flex-col gap-4 overflow-y-auto rounded bg-white px-8 py-8"
            style="transform-origin: center center"
        >
            <app-image
                class="self-center"
                [imageObservable]="appSettingFetcherService.appLoginLogo()"
                style="height: 150px; width: 300px"
            ></app-image>

            <router-outlet></router-outlet>

            <div class="bottom-2 flex w-full items-center justify-between">
                <a
                    href="javascript:void(0)"
                    class="text-gray-400"
                    *ngFor="let lang of translationService.languages"
                    [class.no-underline]="
                        translationService.currentLanguage.code === lang.code
                    "
                    (click)="translationService.setLanguage(lang)"
                >
                    {{ lang.label }}
                </a>

                <small class="text-xs text-gray-500">
                    {{
                        'translate_version_number_version'
                            | translate : { version: versionNumber }
                    }}
                </small>
            </div>
        </div>
    </div>
</div>
