import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@masar/env/environment';
import { StrategicPlan } from '@masar/common/models';
import { miscFunctions, Result } from 'mnm-webapp';
import { TableResult } from '@masar/common/misc/table';

@Injectable({
    providedIn: 'root',
})
export class StrategicPlanService {
    private readonly baseUrl = `${environment.apiUrl}/strategic-plan`;

    public constructor(private http: HttpClient) {}

    public list(
        keyword: string,
        pageNumber: number,
        pageSize: number = 20
    ): Observable<TableResult<StrategicPlan>> {
        let params = new HttpParams();
        params = params.append('keyword', keyword || '');
        params = params.append('pageNumber', `${pageNumber}`);
        params = params.append('pageSize', `${pageSize}`);

        return this.http
            .get<Result<TableResult<StrategicPlan>>>(this.baseUrl, {
                params,
            })
            .pipe(map(result => result.extra));
    }

    public create(data: StrategicPlan): Observable<StrategicPlan> {
        return this.http
            .post<Result<StrategicPlan>>(
                this.baseUrl,
                miscFunctions.objectToURLParams({
                    'strategic': JSON.stringify(data),
                })
            )
            .pipe(map(result => result.extra));
    }

    public update(data: StrategicPlan): Observable<StrategicPlan> {
        return this.http
            .put<Result<StrategicPlan>>(
                this.baseUrl,
                miscFunctions.objectToURLParams({
                    'strategic': JSON.stringify(data),
                })
            )
            .pipe(map(result => result.extra));
    }

    public get(id: string, forEdit: boolean): Observable<StrategicPlan> {
        return this.http
            .get<Result<StrategicPlan>>(`${this.baseUrl}/${id}`, {
                params: new HttpParams().append('forEdit', `${forEdit}`),
            })
            .pipe(map(result => result.extra));
    }

    public delete(id: string): Observable<string> {
        return this.http
            .delete<Result<any>>(`${this.baseUrl}/${id}`)
            .pipe(map(result => result.messages[0]));
    }
}
