import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PartnerEvaluationDialogComponent } from './components/partner-evaluation-dialog/partner-evaluation-dialog.component';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '@masar/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';

const sharedComponents = [PartnerEvaluationDialogComponent];

@NgModule({
    declarations: [sharedComponents],
    imports: [CommonModule, FormsModule, SharedModule, TranslateModule],
    exports: [sharedComponents],
})
export class PartnershipSharedModule {}
