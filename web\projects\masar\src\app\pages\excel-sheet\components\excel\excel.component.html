<div class="relative mx-auto p-2">
    <div class="mb-2 flex text-slate-500">
        <button
            (click)="section.value = 'dashboard'"
            [ngClass]="
                section.value === 'dashboard'
                    ? 'with-bg-green-500 text-white'
                    : ''
            "
            class="bg-blur with-shadow me-2 px-4 py-2"
        >
            <i class="fa-solid fa-house me-2"></i>
            <span>الرئيسية</span>
        </button>
        <button
            (click)="section.value = 'departments'"
            [ngClass]="
                section.value === 'departments'
                    ? 'with-bg-green-500 text-white'
                    : ''
            "
            class="bg-blur with-shadow me-2 px-4 py-2"
        >
            <i class="fa-solid fa-building me-2"></i>
            <span>الإدارات</span>
        </button>
        <button
            (click)="section.value = 'centers'"
            [ngClass]="
                section.value === 'centers'
                    ? 'with-bg-green-500 text-white'
                    : ''
            "
            class="bg-blur with-shadow me-2 px-4 py-2"
        >
            <i class="fa-solid fa-flag me-2"></i>
            <span>قنوات تقديم الخدمة</span>
        </button>
        <button
            (click)="section.value = 'services'"
            [ngClass]="
                section.value === 'services'
                    ? 'with-bg-green-500 text-white'
                    : ''
            "
            class="bg-blur with-shadow me-2 px-4 py-2"
        >
            <i class="fa-solid fa-list me-2"></i>
            <span>الخدمات</span>
        </button>
    </div>

    <div class="bg-blur me-2 overflow-hidden rounded shadow">
        <ng-container [ngSwitch]="section.value">
            <app-dashboard *ngSwitchCase="'dashboard'"></app-dashboard>
            <app-departments *ngSwitchCase="'departments'"></app-departments>
            <app-centers *ngSwitchCase="'centers'"></app-centers>
            <app-services *ngSwitchCase="'services'"></app-services>
        </ng-container>
    </div>

    <input #section type="text" hidden value="dashboard" />
</div>

<button
    type="button"
    (click)="showDataEntryModal.value = '1'"
    class="fixed bottom-3 left-2 flex h-14 w-14 items-center justify-center overflow-hidden rounded-full bg-green-500 text-white shadow-lg hover:shadow-inner"
>
    <i class="fas fa-table-list fa-xl"></i>
</button>

<input type="text" value="0" hidden #showDataEntryModal />

<!-- Modal Overlay -->
<div
    class="fixed inset-0 z-10 flex items-center justify-center bg-black bg-opacity-20"
    *ngIf="showDataEntryModal.value === '1'"
    (click)="showDataEntryModal.value = '0'"
>
    <div class="bg-blur w-6/12" (click)="$event.stopPropagation()">
        <app-data-entry></app-data-entry>
    </div>
</div>
