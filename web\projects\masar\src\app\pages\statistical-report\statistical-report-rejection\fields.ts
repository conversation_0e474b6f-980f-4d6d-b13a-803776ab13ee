// statistical-report-rejection.fields.ts
import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';

export const rejectionFields: () => MnmFormField[] = () => [
    {
        fields: [
            {
                name: 'removeValue',
                label: 'translate_remove_value',
                type: 'checkbox',
                defaultValue: false,
                size: 6,
            },
            {
                name: 'removeAttachment',
                label: 'translate_remove_attachment',
                type: 'checkbox',
                defaultValue: false,
                size: 6,
            },
        ],
    },
    {
        name: 'notes',
        label: 'translate_notes',
        type: 'textarea',
        validators: [Validators.required],
    },
];
