<header class="relative z-30 w-full p-2 pb-0 ps-2">
    <div
        class="flex w-full items-center justify-between rounded-md border bg-white px-4 py-2 shadow-lg"
    >
        <!-- Start buttons -->
        <div class="flex items-center">
            <!-- Sidebar toggle -->
            <button
                *ngIf="
                    (defaultLayoutService.isSidebarHidden$ | async) === false
                "
                class="rounded-full px-3 py-2 text-gray-600 transition-all hover:bg-gray-200 hover:text-secondary-600"
                [appTooltip]="'translate_toggle_menu' | translate"
                (click)="toggleSidebarCollapse.emit()"
            >
                <em class="fa-light fa-bars fa-lg"></em>
            </button>

            <!-- Home link -->
            <a
                class="rounded-full px-3 py-2 text-gray-600 transition-all hover:bg-gray-200 hover:text-secondary-600"
                [appTooltip]="'translate_home_page' | translate"
                routerLink="/"
            >
                <em class="fa-light fa-house fa-lg"></em>
            </a>

            <!-- Logo -->
            <app-logo
                *ngIf="defaultLayoutService.isSidebarHidden$ | async"
            ></app-logo>
        </div>

        <!-- Year selector -->
        <div class="flex items-center" dir="ltr" *ngIf="years">
            <button
                class="rounded-full px-2.5 py-1 text-gray-600 hover:bg-gray-200 hover:text-secondary-600 disabled:opacity-40"
                (click)="changeYear(-1)"
                [appTooltip]="'translate_prev_year' | translate"
                [disabled]="!years.includes(+yearService.get() - 1)"
            >
                <em class="fa-light fa-angle-left fa-lg"></em>
            </button>
            <span
                class="p-2 font-bold text-gray-600"
                [appTooltip]="'translate_current_year' | translate"
            >
                {{ yearService.get() }}
            </span>
            <button
                class="rounded-full px-2.5 py-1 text-gray-600 hover:bg-gray-200 hover:text-secondary-600 disabled:opacity-40"
                (click)="changeYear(1)"
                [appTooltip]="'translate_next_year' | translate"
                [disabled]="!years.includes(+yearService.get() + 1)"
            >
                <em class="fa-light fa-angle-right fa-lg"></em>
            </button>
        </div>

        <!-- End buttons -->
        <div class="flex items-center">
            <!-- Search link -->
            <a
                class="rounded-full px-3 py-2 text-gray-600 transition-all hover:bg-gray-200 hover:text-secondary-600"
                [appTooltip]="'translate_search_page' | translate"
                [routerLink]="['', 'search']"
            >
                <em class="fa-light fa-lg fa-magnifying-glass"></em>
            </a>

            <!-- Help center button -->
            <button
                *appIsOrigin="[
                    organizationOrigin.localhost,
                    organizationOrigin.staging,
                    organizationOrigin.amiriGuard,
                    organizationOrigin.amiriGuard2
                ]"
                class="rounded-full px-3 py-2 text-gray-600 transition-all hover:bg-gray-200 hover:text-secondary-600"
                [appTooltip]="'translate_training_videos' | translate"
                [routerLink]="['', 'training-videos']"
            >
                <i class="fa-solid fa-question"></i>
            </button>

            <!-- Apps button -->
            <button
                class="rounded-full px-3 py-2 text-gray-600 transition-all hover:bg-gray-200 hover:text-secondary-600"
                [appTooltip]="'translate_apps' | translate"
                (click)="showAppsDialog()"
            >
                <em class="fa-light fa-grid fa-lg"></em>
            </button>

            <!-- Settings button -->
            <a
                *appHasAnyPermissionId="[
                    permissionList.fullAccess,
                    permissionList.departmentRead,
                    permissionList.userRead,
                    permissionList.kpiResultCapabilityType,
                    permissionList.successFactor,
                    permissionList.capabilityType,
                    permissionList.policy,
                    permissionList.operationRuleAndRegulation,
                    permissionList.operationSpecification,
                    permissionList.operationEnhancementType,
                    permissionList.kpiType,
                    permissionList.partner,
                    permissionList.planInput,
                    permissionList.kpiTag,
                    permissionList.governmentStrategicGoal,
                    permissionList.improvementOpportunityInputSource,
                    permissionList.improvementOpportunityInputCategory,
                    permissionList.kpiResultTargetSettingMethod,
                    permissionList.kpiBalancedBehaviorCard,
                    permissionList.team,
                    permissionList.strategicGoal,
                    permissionList.planCategory,
                    permissionList.serviceCategory,
                    permissionList.kpiResultCategory
                ]"
                class="relative rounded-full px-3 py-2 text-gray-600 transition-all hover:bg-gray-200 hover:text-secondary-600"
                [appTooltip]="'translate_app_settings' | translate"
                [routerLink]="['', 'system-setting']"
            >
                <p
                    *ngIf="newUserRequestCount > 0"
                    dir="ltr"
                    class="absolute end-[-1px] top-[-8px] flex h-[25px] w-[25px] items-center justify-center rounded-full border-2 border-white bg-red-500 text-[11px] text-white shadow"
                >
                    {{ newUserRequestCount > 99 ? '99+' : newUserRequestCount }}
                </p>
                <em class="fa-light fa-gear fa-lg"></em>
            </a>

            <!-- Notification button -->
            <app-notification-list
                [notificationBadgeCount]="notificationBadgeCount"
            ></app-notification-list>

            <!-- Language toggle -->
            <div appEvader class="relative mx-1" #languageContainer>
                <button
                    class="rounded-md bg-gray-100 px-4 py-2 text-gray-500"
                    type="button"
                    id="language-dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                    (click)="dropdownVisible = !dropdownVisible"
                >
                    {{ translationService.currentLanguage.label }}
                </button>

                <ul
                    class="absolute right-0 top-10 z-10 overflow-hidden rounded-md bg-white shadow-lg"
                    [appClickOutside]="languageContainer"
                    (clickOutside)="dropdownVisible = false"
                    *ngIf="dropdownVisible"
                >
                    <li *ngFor="let lang of translationService.languages">
                        <button
                            class="block w-full px-4 py-2 text-gray-800 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                            type="button"
                            (click)="
                                translationService.setLanguage(lang);
                                dropdownVisible = !dropdownVisible
                            "
                        >
                            {{ lang.label }}
                        </button>
                    </li>
                </ul>
            </div>

            <!-- Fullscreen toggle -->
            <button
                class="rounded-full px-3 py-2 text-gray-600 transition-all hover:bg-gray-200 hover:text-secondary-600"
                (click)="onSwitchFullScreen()"
                [appTooltip]="'translate_toggle_fullscreen' | translate"
            >
                <em
                    class="fa-light fa-lg"
                    [ngClass]="isFullScreen ? 'fa-compress' : 'fa-expand'"
                ></em>
            </button>

            <!-- User card -->
            <app-user-card></app-user-card>
        </div>
    </div>
</header>
