import { Component, Ng<PERSON><PERSON>ule<PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, filter } from 'rxjs/operators';
import { permissionList } from '@masar/common/constants';
import { TableController, TableResult } from '@masar/common/misc/table';
import { Capability, Kpi, LibraryFile, Standard } from '@masar/common/models';
import { ImageApiService } from '@masar/core/services';
import { ExcellenceService } from '../excellence.service';
import { LibraryFileLinkerComponent } from '@masar/features/masar/components';
import { ModalService } from 'mnm-webapp';
import { KpiLinkerComponent } from '@masar/features/kpi-shared/components/kpi-linker/kpi-linker.component';
import { SharedCapabilityService } from '@masar/shared/services';

@Component({
    selector: 'app-standard-detail',
    templateUrl: './standard-detail.component.html',
})
export class StandardDetailComponent implements OnD<PERSON>roy {
    public standard: Standard;
    public standardId: string;

    public permissionList = permissionList;

    public capabilityTableController: TableController<
        Capability,
        { keyword: string; principleId: string }
    >;
    public kpiTableController: TableController<
        Kpi,
        { keyword: string; principleId: string }
    >;
    public libraryTableController: TableController<
        LibraryFile,
        { keyword: string; principleId: string }
    >;

    public constructor(
        public imageApiService: ImageApiService,
        private excellenceService: ExcellenceService,
        private capabilityService: SharedCapabilityService,
        private modalService: ModalService,
        private moduleRef: NgModuleRef<any>,
        activatedRoute: ActivatedRoute,
        router: Router
    ) {
        this.standardId = activatedRoute.snapshot.paramMap.get('standardId');
        if (!this.standardId) {
            router.navigate(['']);
            return;
        }

        this.initTableControllers();

        excellenceService
            .getStandard(this.standardId)
            .pipe(
                catchError(() => {
                    router.navigate(['']);
                    return of(null);
                }),
                filter(x => x !== null)
            )
            .subscribe(item => (this.standard = item));
    }

    public ngOnDestroy(): void {
        this.capabilityTableController?.stop();
        this.kpiTableController?.stop();
        this.libraryTableController?.stop();
    }

    public linkedCapabilitiesCallback(): (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Capability>> {
        return (keyword, pageNumber, pageSize) =>
            this.excellenceService.capabilities(
                { standardId: this.standardId },
                keyword,
                pageNumber,
                pageSize
            );
    }

    public linkedKpisCallback(): (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Kpi>> {
        return (keyword, pageNumber, pageSize) =>
            this.excellenceService.kpis(
                { standardId: this.standardId },
                keyword,
                pageNumber,
                pageSize
            );
    }

    public linkedLibraryFilesCallback(): (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<LibraryFile>> {
        return (keyword, pageNumber, pageSize) =>
            this.excellenceService.libraryFiles(
                { standardId: this.standardId },
                keyword,
                pageNumber,
                pageSize
            );
    }

    public refreshTables(principleId: string): void {
        this.capabilityTableController.filter.data.principleId = principleId;
        this.kpiTableController.filter.data.principleId = principleId;
        this.libraryTableController.filter.data.principleId = principleId;

        this.capabilityTableController.filter$.next(true);
        this.kpiTableController.filter$.next(true);
        this.libraryTableController.filter$.next(true);
    }

    public async showKpiListModal(item: Capability): Promise<void> {
        await this.modalService.show(KpiLinkerComponent, {
            size: { width: '70%' },
            beforeInit: c => {
                c.mode = 'view';
                c.listLinkedKpisCallback = (
                    keyword: string,
                    pageNumber: number,
                    pageSize: number
                ) =>
                    this.capabilityService.listLinkedKpis(
                        item.id,
                        keyword,
                        Math.min(...this.standard.tournament.years),
                        Math.max(...this.standard.tournament.years) + 1,
                        pageNumber,
                        pageSize
                    );
            },
            moduleRef: this.moduleRef,
        });
    }

    public async showLibraryFileListModal(item: Capability): Promise<void> {
        await this.modalService.show(LibraryFileLinkerComponent, {
            beforeInit: c => {
                c.mode = 'view';
                c.listLinkedFilesCallback = (
                    keyword: string,
                    pageNumber: number,
                    pageSize: number
                ) =>
                    this.capabilityService.listLinkedLibraryFiles(
                        item.id,
                        keyword,
                        pageNumber,
                        pageSize
                    );
            },
            moduleRef: this.moduleRef,
        });
    }

    private initTableControllers(): void {
        // Capability table controller.
        this.capabilityTableController = new TableController<
            Capability,
            { keyword: string; principleId: string }
        >(filter =>
            this.excellenceService.capabilities(
                {
                    standardId: this.standardId,
                },
                filter.data.keyword,
                filter.pageNumber,
                filter.pageSize
            )
        );
        this.capabilityTableController.start();

        // Kpis table controller.
        this.kpiTableController = new TableController<
            Kpi,
            { keyword: string; principleId: string }
        >(filter =>
            this.excellenceService.kpis(
                {
                    standardId: this.standardId,
                },
                filter.data.keyword,
                filter.pageNumber,
                filter.pageSize
            )
        );
        this.kpiTableController.start();

        // Library file table controller.
        this.libraryTableController = new TableController<
            LibraryFile,
            { keyword: string; principleId: string }
        >(filter =>
            this.excellenceService.libraryFiles(
                {
                    standardId: this.standardId,
                },
                filter.data.keyword,
                filter.pageNumber,
                filter.pageSize
            )
        );
        this.libraryTableController.start();
    }
}
