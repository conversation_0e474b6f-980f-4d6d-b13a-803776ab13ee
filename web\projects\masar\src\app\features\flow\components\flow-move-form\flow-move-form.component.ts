import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MnmFormState } from '@masar/shared/components';
import { fields } from './fields';
import { FormBuilder } from '@angular/forms';
import { extractAvailableStates } from '@masar/features/flow/utils/extract-available-states';
import { TranslateService } from '@ngx-translate/core';
import { FlowItemType } from '@masar/features/flow/types/flow-item-type.type';
import { FlowService } from '@masar/features/flow/services/flow.service';
import { finalize } from 'rxjs/operators';
import { FlowItem } from '@masar/features/flow/interfaces';

@Component({
    selector: 'app-flow-form',
    templateUrl: './flow-move-form.component.html',
})
export class FlowMoveFormComponent implements OnInit {
    @Input() public item: FlowItem;
    @Input() public itemType: FlowItemType;
    @Output() public transfer = new EventEmitter<string>();

    public formState: MnmFormState;
    public isSubmitting = false;

    public constructor(
        private flowService: FlowService,
        private translateService: TranslateService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);
    }

    public ngOnInit(): void {
        this.formState.get('state').items = extractAvailableStates(
            this.item
        ).map(x => ({
            id: x,
            name: this.translateService.instant(`translate_${x}`),
        }));
    }

    public submit(): void {
        this.formState.setTriedToSubmit();
        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const values = this.formState.group.getRawValue();

        this.flowService
            .move(this.item.id, values['state'], values['note'], this.itemType)
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(() => this.transfer.emit(values['state']));
    }
}
