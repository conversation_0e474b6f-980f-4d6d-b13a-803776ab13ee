<app-content contentTitle="{{ 'translate_linked_attachments' | translate }}">
    <!-- Content -->
    <div content>
        <!-- Filter -->
        <app-filter-result-box>
            <!-- Keyword field -->
            <input
                type="text"
                placeholder="{{ 'translate_search_by_name' | translate }}"
                [(ngModel)]="tableController.filter.data.keyword"
                (keyup)="tableController.filter$.next(true)"
            />

            <!-- Years -->
            <ng-select
                [items]="years"
                [multiple]="true"
                placeholder="{{ 'translate_years' | translate }}"
                [(ngModel)]="tableController.filter.data.years"
                (change)="tableController.filter$.next(true)"
            >
            </ng-select>

            <!-- Departments -->
            <ng-select
                [items]="departments"
                bindValue="id"
                bindLabel="name"
                [multiple]="true"
                placeholder="{{ 'translate_departments' | translate }}"
                [(ngModel)]="tableController.filter.data.departmentIds"
                (change)="tableController.filter$.next(true)"
            >
            </ng-select>
        </app-filter-result-box>

        <!-- Table -->
        <app-list-loading [items]="tableController.items">
            <table class="mb-5">
                <thead>
                    <tr>
                        <th>{{ 'translate_type' | translate }}</th>
                        <th>{{ 'translate_name' | translate }}</th>
                        <th>{{ 'translate_department' | translate }}</th>
                        <th>{{ 'translate_year' | translate }}</th>
                        <th>{{ 'translate_period' | translate }}</th>
                    </tr>
                </thead>

                <tbody>
                    <tr
                        *ngFor="
                            let item of tableController.items;
                            let idx = index
                        "
                    >
                        <!-- Icon -->
                        <td>
                            <app-file-icon
                                [contentType]="item.contentType"
                            ></app-file-icon>
                        </td>

                        <!-- File name -->
                        <td>
                            <a
                                href="#"
                                onclick="return false"
                                (click)="download(item)"
                            >
                                {{ item.name }}
                            </a>
                        </td>

                        <!-- Department -->
                        <td>
                            <a
                                *appHasPermissionId="
                                    permissionList.departmentRead;
                                    else noDepartmentReadPermissionTemplate
                                "
                                [routerLink]="[
                                    '',
                                    'department',
                                    'detail',
                                    item.department.id
                                ]"
                            >
                                {{ item.department.name }}
                            </a>

                            <ng-template #noDepartmentReadPermissionTemplate>
                                {{ item.department.name }}
                            </ng-template>
                        </td>

                        <!-- Year -->
                        <td>
                            {{ item.year }}
                        </td>

                        <!-- Period -->
                        <td class="text-center">
                            <app-kpi-result-attachment-period-label
                                [period]="item.period"
                                [periodType]="item.periodType"
                            ></app-kpi-result-attachment-period-label>
                        </td>
                    </tr>
                </tbody>
            </table>
            <app-table-pagination
                [tableController]="tableController"
            ></app-table-pagination>
        </app-list-loading>
    </div>
</app-content>
