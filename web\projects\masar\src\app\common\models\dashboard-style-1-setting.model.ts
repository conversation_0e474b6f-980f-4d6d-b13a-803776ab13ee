export interface DashboardStyle1Setting {
    bannerFile: File;
    sections: DashboardStyle1SettingSection[];
}

export interface DashboardStyle1SettingSectionItem {
    nameAr: string;
    nameEn: string;
    link: string;
    isHidden: boolean;
    iconFile?: File;
}

export interface DashboardStyle1SettingSection {
    nameAr: string;
    nameEn: string;
    style: 'style_1' | 'style_2';
    isHidden: boolean;
    items: DashboardStyle1SettingSectionItem[];
}

interface File {
    name: string;
    hash: string;
    bytes: Uint8Array;
}
