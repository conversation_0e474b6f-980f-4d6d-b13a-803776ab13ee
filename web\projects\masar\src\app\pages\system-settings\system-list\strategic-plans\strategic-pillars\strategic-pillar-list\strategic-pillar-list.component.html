<app-page pageTitle="{{ 'translate_strategic_pillars' | translate }}">
    <!-- Tools -->
    <ng-container tools>
        <!-- Add New -->
        <a
            [routerLink]="[
                '',
                'system-list',
                'strategic-plans',
                'strategic-pillar',
                'new'
            ]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-plus me-2"></i>
            <span>{{ 'translate_add_new_strategic_pillar' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <ng-container content>
        <!-- Filter -->
        <app-filter-result-box>
            <!-- Keyword -->
            <app-search-input
                [placeholder]="'translate_search_by_keyword'"
                [(ngModel)]="tableController.filter.data.keyword"
                [tableController]="tableController"
            ></app-search-input>
        </app-filter-result-box>

        <!-- Table -->
        <app-list-loading [items]="tableController.items">
            <table>
                <thead>
                    <tr>
                        <th>{{ 'translate_name' | translate }}</th>
                        <th style="width: 0">
                            <i class="fa-light fa-gear"></i>
                        </th>
                    </tr>
                </thead>

                <tbody>
                    <tr
                        *ngFor="
                            let item of tableController.items;
                            let idx = index
                        "
                    >
                        <!-- Name -->
                        <td>
                            <div class="text-center">
                                {{
                                    item.name ||
                                        ('translate_unnamed_strategic_pillar'
                                            | translate)
                                }}
                            </div>
                        </td>

                        <!-- Actions -->
                        <td>
                            <app-dropdown>
                                <a
                                    [routerLink]="[
                                        '',
                                        'system-list',
                                        'strategic-plans',
                                        'strategic-pillar',
                                        'detail',
                                        item.id
                                    ]"
                                    class="btn btn-sm btn-success"
                                    [appTooltip]="'translate_view' | translate"
                                >
                                    <i class="fa-light fa-eye fa-fw"></i>
                                </a>
                                <a
                                    [routerLink]="[
                                        '',
                                        'system-list',
                                        'strategic-plans',
                                        'strategic-pillar',
                                        'edit',
                                        item.id
                                    ]"
                                    class="btn btn-sm btn-info"
                                    [appTooltip]="'translate_edit' | translate"
                                >
                                    <i class="fa-light fa-edit fa-fw"></i>
                                </a>
                                <button
                                    [disabled]="
                                        currentlyDeleting.includes(item.id)
                                    "
                                    (confirm)="deleteStrategicPillar(item)"
                                    [swal]="{
                                        title:
                                            'translate_delete_this_item_question_mark'
                                            | translate,
                                        confirmButtonText:
                                            'translate_yes' | translate,
                                        cancelButtonText:
                                            'translate_cancel' | translate,
                                        showCancelButton: true,
                                        showCloseButton: true
                                    }"
                                    class="btn btn-sm btn-danger"
                                    [appTooltip]="
                                        'translate_delete' | translate
                                    "
                                >
                                    <i class="fas fa-trash fa-fw"></i>
                                </button>
                            </app-dropdown>
                        </td>
                    </tr>
                </tbody>
            </table>
            <app-table-pagination
                [tableController]="tableController"
            ></app-table-pagination>
        </app-list-loading>
    </ng-container>
</app-page>
