<!-- Filter -->
<app-filter-result-box>
    <!-- Years -->
    <ng-select
        [items]="years"
        [multiple]="true"
        placeholder="{{ 'translate_years' | translate }}"
        [(ngModel)]="tableController.filter.data.years"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Start time -->
    <div class="flex flex-row items-center">
        <input
            class="flex-grow"
            #startTime="appFlatpickr"
            type="date"
            appFlatpickr
            [(ngModel)]="tableController.filter.data.startTime"
            (flatpickrOpen)="$event.instance.calendarContainer.lang = 'en'"
            (flatPickrChange)="tableController.filter$.next(true)"
            placeholder="{{ 'translate_start_time' | translate }}"
            [config]="{ mode: 'range' }"
        />
        <button (click)="startTime.clear()" class="btn btn-sm btn-primary ms-1">
            <i class="fa-light fa-times"></i>
        </button>
    </div>

    <!-- End time -->
    <div class="flex flex-row items-center">
        <input
            class="flex-grow"
            #endTime="appFlatpickr"
            type="date"
            appFlatpickr
            [(ngModel)]="tableController.filter.data.endTime"
            (flatpickrOpen)="$event.instance.calendarContainer.lang = 'en'"
            (flatPickrChange)="tableController.filter$.next(true)"
            placeholder="{{ 'translate_end_time' | translate }}"
            [config]="{ mode: 'range' }"
        />
        <button (click)="endTime.clear()" class="btn btn-sm btn-primary ms-1">
            <i class="fa-light fa-times"></i>
        </button>
    </div>

    <!-- Measurement cycles -->
    <ng-select
        [items]="measurementCycles"
        [multiple]="true"
        placeholder="{{ 'translate_measurement_cycles' | translate }}"
        bindLabel="name"
        bindValue="id"
        [(ngModel)]="tableController.filter.data.measurementCycles"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Paths -->
    <ng-select
        [items]="paths"
        [multiple]="true"
        placeholder="{{ 'translate_paths' | translate }}"
        bindLabel="name"
        bindValue="id"
        [(ngModel)]="tableController.filter.data.paths"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- Departments -->
    <ng-select
        [items]="departments"
        [multiple]="true"
        placeholder="{{ 'translate_departments' | translate }}"
        bindLabel="name"
        bindValue="id"
        [(ngModel)]="tableController.filter.data.departmentIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>

    <!-- KpiTypes -->
    <ng-select
        *ngIf="!hiddenFilter.kpiType"
        [items]="kpiTypes"
        [multiple]="true"
        placeholder="{{ 'translate_kpi_type' | translate }}"
        bindLabel="name"
        bindValue="id"
        [(ngModel)]="tableController.filter.data.kpiTypeIds"
        (change)="tableController.filter$.next(true)"
    >
    </ng-select>
</app-filter-result-box>

<!-- Table -->
<app-list-loading [items]="tableController.items">
    <table class="mb-5 text-center">
        <thead>
            <tr>
                <th>
                    {{ 'translate_completed_responses_ratio' | translate }}
                </th>
                <th>{{ 'translate_year' | translate }}</th>
                <th>{{ 'translate_departments_count' | translate }}</th>
                <th>{{ 'translate_kpis_count' | translate }}</th>
                <th>{{ 'translate_measurement_cycle' | translate }}</th>
                <th>{{ 'translate_periods' | translate }}</th>
                <th>{{ 'translate_path' | translate }}</th>
                <th>{{ 'translate_status' | translate }}</th>
                <th style="width: 0">
                    <i class="fa-light fa-gear"></i>
                </th>
            </tr>
        </thead>

        <tbody>
            <tr *ngFor="let item of tableController.items">
                <td class="text-center">
                    <app-progress-ring
                        [value]="item.completionRate"
                        [radius]="30"
                        [strokeWidth]="6"
                    ></app-progress-ring>
                </td>

                <!-- Year -->
                <td>{{ item.year }}</td>

                <!-- Departments -->
                <td>
                    <span
                        *ngIf="
                            !item.departments.length;
                            else departmentCountTemplateRef
                        "
                    >
                        {{ 'translate_all_departments' | translate }}
                    </span>
                    <ng-template #departmentCountTemplateRef>
                        <div
                            *ngIf="
                                item.departments.length === 1;
                                else multipleDepartmentsTemplate
                            "
                            class="mx-1 px-2 text-base"
                        >
                            {{ item.departments[0].name }}
                        </div>
                        <ng-template #multipleDepartmentsTemplate>
                            <div class="badge badge-info mx-1 px-2 text-base">
                                {{ item.departments.length }}
                            </div>
                        </ng-template>
                    </ng-template>
                </td>

                <!-- Kpis -->
                <td>
                    <span *ngIf="!item.kpis.length; else kpiCountTemplateRef">
                        {{ 'translate_all_kpis' | translate }}
                    </span>
                    <ng-template #kpiCountTemplateRef>
                        <div
                            *ngIf="
                                item.kpis.length === 1;
                                else multipleKpisTemplate
                            "
                            class="mx-1 px-2 text-base"
                        >
                            {{ item.kpis[0].name }}
                        </div>
                        <ng-template #multipleKpisTemplate>
                            <div
                                class="badge badge-warning mx-1 px-2 text-base"
                            >
                                {{ item.kpis.length }}
                            </div>
                        </ng-template>
                    </ng-template>
                </td>

                <td>
                    {{
                        item.measurementCycle
                            | translateItem : 'kpi-cycle'
                            | async
                    }}
                </td>

                <!-- Periods -->
                <td>
                    <div *ngIf="item?.periods">
                        <div
                            *ngFor="let period of item.periods"
                            class="badge badge-primary mx-1 px-2 text-base"
                        >
                            {{ period + 1 }}
                        </div>
                    </div>
                </td>

                <td>
                    {{
                        item.path
                            | translateItem
                                : 'kpi-result-data-entry-request-path'
                            | async
                    }}
                </td>

                <!-- Status -->
                <td>
                    <ng-container
                        *ngIf="
                            item.startTime | daysLeft : item.endTime as status
                        "
                    >
                        <div class="mb-2 flex justify-center gap-2 text-sm">
                            <span>
                                <span class="font-semibold text-gray-400">
                                    {{ 'translate_from' | translate }}:
                                </span>
                                <span class="text-gray-500">
                                    {{ item.startTime | date : 'mediumDate' }}
                                </span>
                            </span>
                            <span>
                                <span class="font-semibold text-gray-400">
                                    {{ 'translate_to' | translate }}:
                                </span>
                                <span class="text-gray-500">
                                    {{ item.endTime | date : 'mediumDate' }}
                                </span>
                            </span>
                        </div>
                        <p [class]="status.class" class="text-sm">
                            {{ status.label }}
                        </p>
                    </ng-container>
                </td>

                <!-- Control buttons -->
                <td>
                    <app-dropdown>
                        <!-- View link -->
                        <a
                            [routerLink]="[
                                '',
                                'kpi',
                                'data-entry-request',
                                'detail',
                                item.id
                            ]"
                            class="btn btn-sm btn-success"
                            [appTooltip]="'translate_show' | translate"
                            *appHasPermissionId="
                                permissionList.kpiResultEntryRequestWrite
                            "
                        >
                            <i class="fa-light fa-eye fa-fw"></i>
                        </a>

                        <!-- Edit link -->
                        <a
                            [routerLink]="[
                                '',
                                'kpi',
                                'data-entry-request',
                                'edit',
                                item.id
                            ]"
                            class="btn btn-sm btn-info"
                            [appTooltip]="'translate_edit' | translate"
                            *appHasPermissionId="
                                permissionList.kpiResultEntryRequestWrite
                            "
                        >
                            <i class="fa-light fa-edit fa-fw"></i>
                        </a>

                        <!-- Delete button -->
                        <button
                            [disabled]="currentlyDeleting.includes(item.id)"
                            *appHasPermissionId="
                                permissionList.kpiResultEntryRequestDelete
                            "
                            (confirm)="delete(item)"
                            [swal]="{
                                title:
                                    'translate_delete_this_item_question_mark'
                                    | translate,
                                text:
                                    'translate_delete_this_item_description'
                                    | translate,
                                confirmButtonText: 'translate_yes' | translate,
                                cancelButtonText:
                                    'translate_cancel' | translate,
                                showCancelButton: true,
                                showCloseButton: true
                            }"
                            class="btn btn-sm btn-danger"
                            [appTooltip]="'translate_delete' | translate"
                        >
                            <i class="fas fa-trash fa-fw"></i>
                        </button>
                    </app-dropdown>
                </td>
            </tr>
        </tbody>
    </table>
    <app-table-pagination
        [tableController]="tableController"
    ></app-table-pagination>
</app-list-loading>
