import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoadTranslationsResolver } from '@ng-omar/translation';
import { DefaultLayoutComponent } from './default-layout.component';
import { permissionList } from '@masar/common/constants';
import { UnderConstructionComponent } from '@masar/layouts/default-layout/components/under-construction/under-construction.component';

const routes: Routes = [
    {
        path: '',
        component: DefaultLayoutComponent,
        children: [
            {
                path: '',
                redirectTo: 'dashboard',
                pathMatch: 'full',
            },

            {
                path: 'dashboard',
                loadChildren: () =>
                    import('@masar/pages/dashboard/dashboard.module').then(
                        x => x.DashboardModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'excellence',
                loadChildren: () =>
                    import('@masar/pages/excellence/excellence.module').then(
                        x => x.ExcellenceModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'training-videos',
                loadChildren: () =>
                    import(
                        '@masar/pages/training-videos/training-videos.module'
                    ).then(x => x.TrainingVideosModule),
                resolve: {
                    translations: LoadTranslationsResolver,
                },
            },

            {
                path: 'user-request',
                loadChildren: () =>
                    import(
                        '@masar/pages/user-request/user-request.module'
                    ).then(x => x.UsersRequestsModule),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'excel-sheet',
                loadChildren: () =>
                    import('@masar/pages/excel-sheet/excel-sheet.module').then(
                        x => x.ExcelSheetModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'kpi',
                loadChildren: () =>
                    import('@masar/pages/kpi/kpi.module').then(
                        x => x.KpiModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.kpiRead },
            },

            {
                path: 'plan',
                loadChildren: () =>
                    import('@masar/pages/plan/plan.module').then(
                        x => x.PlanModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.planRead },
            },

            {
                path: 'plan-task',
                loadChildren: () =>
                    import('@masar/pages/plan-task/plan-task.module').then(
                        x => x.PlanTaskModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'plan-subtask',
                loadChildren: () =>
                    import(
                        '@masar/pages/plan-subtask/plan-subtask.module'
                    ).then(x => x.PlanSubtaskModule),
                resolve: { translations: LoadTranslationsResolver },
            },
            {
                path: 'operation',
                loadChildren: () =>
                    import('@masar/pages/operation/operation.module').then(
                        x => x.OperationModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.operationRead },
            },

            {
                path: 'plan',
                loadChildren: () =>
                    import('@masar/pages/plan/plan.module').then(
                        x => x.PlanModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.planRead },
            },

            {
                path: 'benchmark',
                loadChildren: () =>
                    import('@masar/pages/benchmark/benchmark.module').then(
                        x => x.BenchmarkModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.benchmarkRead },
            },

            {
                path: 'capability',
                loadChildren: () =>
                    import('@masar/pages/capability/capability.module').then(
                        x => x.CapabilityModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.capability },
            },

            {
                path: 'tournament',
                loadChildren: () =>
                    import('@masar/pages/tournament/tournament.module').then(
                        x => x.TournamentModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'report',
                loadChildren: () =>
                    import('@masar/pages/report/report.module').then(
                        x => x.ReportModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'library',
                loadChildren: () =>
                    import('@masar/pages/library/library.module').then(
                        x => x.LibraryModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'my-notification',
                loadChildren: () =>
                    import(
                        '@masar/pages/my-notification/my-notification.module'
                    ).then(x => x.MyNotificationModule),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'partner',
                loadChildren: () =>
                    import('@masar/pages/partner/partner.module').then(
                        x => x.PartnerModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.partner },
            },

            {
                path: 'service',
                loadChildren: () =>
                    import('@masar/pages/service/service.module').then(
                        x => x.ServiceModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.service },
            },

            {
                path: 'opportunity',
                loadChildren: () =>
                    import('@masar/pages/opportunity/opportunity.module').then(
                        x => x.OpportunityModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: {
                    permissionId: permissionList.improvementOpportunityRead,
                },
            },

            {
                path: 'innovation',
                loadChildren: () =>
                    import('@masar/pages/innovation/innovation.module').then(
                        x => x.InnovationModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.innovator },
            },

            {
                path: 'innovator',
                loadChildren: () =>
                    import('@masar/pages/innovator/innovator.module').then(
                        x => x.InnovatorModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.innovator },
            },

            {
                path: 'notification',
                loadChildren: () =>
                    import(
                        '@masar/pages/notification/notification.module'
                    ).then(x => x.NotificationModule),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.notification },
            },

            {
                path: 'activity',
                loadChildren: () =>
                    import('@masar/pages/activity/activity.module').then(
                        x => x.ActivityModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.innovation },
            },

            {
                path: 'statistical-report',
                loadChildren: () =>
                    import(
                        '@masar/pages/statistical-report/statistical-report.module'
                    ).then(x => x.StatisticalReportModule),
                resolve: { translations: LoadTranslationsResolver },
                data: {
                    permissionId: permissionList.statisticalReportRead,
                },
            },

            {
                path: 'search',
                loadChildren: () =>
                    import('@masar/pages/search/search.module').then(
                        x => x.SearchModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'profile',
                loadChildren: () =>
                    import('@masar/pages/profile/profile.module').then(
                        x => x.ProfileModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: 'partnership-contract',
                loadChildren: () =>
                    import(
                        '@masar/pages/partnership-contract/partnership-contract.module'
                    ).then(x => x.PartnershipContractModule),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.partnershipRead },
            },

            {
                path: 'risk',
                loadChildren: () =>
                    import('@masar/pages/risk/risk.module').then(
                        x => x.RiskModule
                    ),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.riskRead },
            },

            {
                path: 'partnership-termination-request',
                loadChildren: () =>
                    import(
                        '@masar/pages/partnership-termination-request/partnership-termination-request.module'
                    ).then(x => x.PartnershipTerminationRequestModule),
                resolve: { translations: LoadTranslationsResolver },
                data: { permissionId: permissionList.partnershipRead },
            },

            {
                path: 'kpi-evaluation',
                loadChildren: () =>
                    import(
                        '@masar/pages/kpi-evaluation/kpi-evaluation.module'
                    ).then(x => x.KpiEvaluationModule),
                resolve: { translations: LoadTranslationsResolver },
                data: {
                    permissionId:
                        permissionList.kpiResultPeriodExportEvaluation,
                },
            },
            {
                path: 'strategic-plan',
                loadChildren: () =>
                    import(
                        '@masar/pages/strategic-plan/strategic-plan.module'
                    ).then(x => x.StrategicPlanModule),
                resolve: { translations: LoadTranslationsResolver },
            },

            {
                path: '',
                loadChildren: () =>
                    import(
                        '@masar/pages/system-settings/system-setting.module'
                    ).then(x => x.SystemSettingModule),
            },

            {
                path: '**',
                component: UnderConstructionComponent,
                data: {
                    title: 'translate_under_construction',
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class DefaultLayoutRouting {}
