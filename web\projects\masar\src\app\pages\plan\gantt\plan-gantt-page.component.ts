import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnInit,
    ViewChild,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { finalize } from 'rxjs/operators';
import { forkJoin } from 'rxjs';
import { PlanService } from '@masar/pages/plan/plan.service';
import { PlanTaskListService } from '@masar/features/plan-shared/components/plan-task-list/plan-task-list.service';
import { PlanSubtaskListService } from '@masar/features/plan-shared/components/plan-subtask-list/plan-subtask-list.service';
import {
    Plan,
    PlanTask,
    PlanSubtask,
    PlanSubsubtask,
} from '@masar/common/models';
import {
    GanttChartComponent,
    GanttDataMapper,
    GanttConfig,
} from '@masar/shared/components';

// Union type for all plan-related data
type PlanItemData = PlanTask | PlanSubtask | PlanSubsubtask;

// Extended type to handle the mixed hierarchy
interface PlanHierarchyItem {
    id: string;
    name?: string;
    from: Date | string;
    to: Date | string;
    progress?: number;
    type: 'task' | 'subtask' | 'subsubtask';
    parentId?: string;
    originalData: PlanItemData;
    children?: PlanHierarchyItem[];
}

@Component({
    selector: 'app-plan-gantt-page',
    templateUrl: './plan-gantt-page.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [PlanTaskListService, PlanSubtaskListService],
})
export class PlanGanttPageComponent implements OnInit {
    @ViewChild(GanttChartComponent, { static: false })
    public ganttChart: GanttChartComponent<PlanHierarchyItem>;

    public plan: Plan;
    public isLoading = true;
    public error: string | null = null;
    public hierarchicalData: PlanHierarchyItem[] = [];

    // Computed properties for template
    public get planStartDate(): Date | undefined {
        return this.plan?.from ? new Date(this.plan.from) : undefined;
    }

    public get planEndDate(): Date | undefined {
        return this.plan?.to ? new Date(this.plan.to) : undefined;
    }

    // Data mapper for the hierarchical plan structure
    public planDataMapper: GanttDataMapper<PlanHierarchyItem> = {
        getId: (item: PlanHierarchyItem) => item.id,
        getName: (item: PlanHierarchyItem) => {
            if (item.name) {
                return item.name;
            }
            // Fallback for items without names (like PlanSubsubtask)
            return `${item.type} ${item.id}`;
        },
        getFromDate: (item: PlanHierarchyItem) => item.from,
        getToDate: (item: PlanHierarchyItem) => item.to,
        getProgress: (item: PlanHierarchyItem) => item.progress || 0,
        getChildren: (item: PlanHierarchyItem) => item.children || [],
    };

    // Gantt configuration - tasks will be initially expanded
    public ganttConfig: GanttConfig = {
        initiallyExpanded: true,
        showProgress: true,
        allowExpansion: true,
    };

    private readonly largePageSize = 1000;

    public constructor(
        private route: ActivatedRoute,
        private planService: PlanService,
        private planTaskListService: PlanTaskListService,
        private planSubtaskListService: PlanSubtaskListService,
        private cdr: ChangeDetectorRef
    ) {}

    public ngOnInit(): void {
        this.loadPlan();
    }

    public onTaskClick(_item: PlanHierarchyItem): void {
        // Handle task click - navigate to task details, etc.
    }

    public onTaskToggle(_taskId: string): void {
        // Handle task expansion/collapse
    }

    // Method to get Gantt container element for PDF export
    public getGanttElementForExport(): HTMLElement[] {
        return this.ganttChart?.getGanttElementForExport() || [];
    }

    private loadPlan(): void {
        const planId = this.route.snapshot.paramMap.get('id');
        if (!planId) {
            this.error = 'Plan ID not found';
            this.isLoading = false;
            this.cdr.markForCheck();
            return;
        }

        this.isLoading = true;
        this.error = null;

        const taskListParams = {
            keyword: '',
            years: [],
            assigneeType: null,
            departmentIds: [],
            includeChildDepartments: false,
            teamIds: [],
            userIds: [],
            planIds: [planId],
            categoryIds: [],
            from: null,
            to: null,
            progressStatus: '' as '' | 'in_progress' | 'completed',
            orderBy: '',
            pageNumber: 0,
            pageSize: this.largePageSize,
            considerPlanFilterAsBase: true,
        };

        const subtaskListParams = {
            keyword: '',
            assigneeType: null,
            departmentIds: [],
            includeChildDepartments: false,
            teamIds: [],
            userIds: [],
            planIds: [planId],
            taskIds: [],
            from: null,
            to: null,
            status: null,
            orderBy: '',
            pageNumber: 0,
            pageSize: this.largePageSize,
            considerPlanFilterAsBase: true,
        };

        forkJoin({
            plan: this.planService.get(planId),
            tasks: this.planTaskListService.list(
                taskListParams.keyword,
                taskListParams.years,
                taskListParams.assigneeType,
                taskListParams.departmentIds,
                taskListParams.includeChildDepartments,
                taskListParams.teamIds,
                taskListParams.userIds,
                taskListParams.planIds,
                taskListParams.categoryIds,
                taskListParams.from,
                taskListParams.to,
                taskListParams.progressStatus,
                taskListParams.orderBy,
                taskListParams.pageNumber,
                taskListParams.pageSize,
                taskListParams.considerPlanFilterAsBase
            ),
            subtasks: this.planSubtaskListService.list(
                subtaskListParams.keyword,
                subtaskListParams.assigneeType,
                subtaskListParams.departmentIds,
                subtaskListParams.includeChildDepartments,
                subtaskListParams.teamIds,
                subtaskListParams.userIds,
                subtaskListParams.planIds,
                subtaskListParams.taskIds,
                subtaskListParams.from,
                subtaskListParams.to,
                subtaskListParams.status,
                subtaskListParams.orderBy,
                subtaskListParams.pageNumber,
                subtaskListParams.pageSize,
                subtaskListParams.considerPlanFilterAsBase
            ),
        })
            .pipe(
                finalize(() => {
                    this.isLoading = false;
                    this.cdr.markForCheck();
                })
            )
            .subscribe({
                next: result => {
                    this.plan = result.plan;
                    this.plan.tasks = result.tasks.items;

                    const subtasksByTask = new Map<string, PlanSubtask[]>();
                    result.subtasks.items.forEach(subtask => {
                        if (!subtasksByTask.has(subtask.task.id)) {
                            subtasksByTask.set(subtask.task.id, []);
                        }
                        subtasksByTask.get(subtask.task.id)!.push(subtask);
                    });

                    this.plan.tasks.forEach(task => {
                        task.subtasks = subtasksByTask.get(task.id) || [];
                    });

                    this.transformPlanToHierarchicalData();
                    this.cdr.markForCheck();
                },
                error: _error => {
                    this.error = 'Failed to load plan data';
                    this.cdr.markForCheck();
                },
            });
    }

    private transformPlanToHierarchicalData(): void {
        if (!this.plan || !this.plan.tasks || this.plan.tasks.length === 0) {
            this.hierarchicalData = [];
            return;
        }

        this.hierarchicalData = this.plan.tasks.map(task =>
            this.convertTaskToHierarchyItem(task)
        );
    }

    private convertTaskToHierarchyItem(task: PlanTask): PlanHierarchyItem {
        const hierarchyItem: PlanHierarchyItem = {
            id: task.id || '',
            name: task.name || '',
            from: task.from,
            to: task.to,
            progress: task.progress || 0,
            type: 'task',
            originalData: task,
        };

        // Convert subtasks to children
        if (task.subtasks && task.subtasks.length > 0) {
            hierarchyItem.children = task.subtasks.map(subtask =>
                this.convertSubtaskToHierarchyItem(subtask, task.id)
            );
        }

        return hierarchyItem;
    }

    private convertSubtaskToHierarchyItem(
        subtask: PlanSubtask,
        parentId: string
    ): PlanHierarchyItem {
        const hierarchyItem: PlanHierarchyItem = {
            id: subtask.id || '',
            name: subtask.name || '',
            from: subtask.from,
            to: subtask.to,
            progress: subtask.progress || 0,
            type: 'subtask',
            parentId,
            originalData: subtask,
        };

        // Convert subsubtasks to children
        if (subtask.subsubtasks && subtask.subsubtasks.length > 0) {
            hierarchyItem.children = subtask.subsubtasks.map(subsubtask =>
                this.convertSubsubtaskToHierarchyItem(subsubtask, subtask.id)
            );
        }

        return hierarchyItem;
    }

    private convertSubsubtaskToHierarchyItem(
        subsubtask: PlanSubsubtask,
        parentId: string
    ): PlanHierarchyItem {
        return {
            id: subsubtask.id || '',
            // PlanSubsubtask doesn't have a name, so we create one
            name: `Subsubtask ${subsubtask.id}`,
            from: subsubtask.from,
            to: subsubtask.to,
            progress: subsubtask.progress || 0,
            type: 'subsubtask',
            parentId,
            originalData: subsubtask,
        };
    }
}
