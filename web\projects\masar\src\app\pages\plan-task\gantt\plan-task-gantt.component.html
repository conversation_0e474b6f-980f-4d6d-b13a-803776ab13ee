<app-page pageTitle="{{ task ? task.plan.name : '' }}">
    <!-- Tools -->
    <ng-container tools>
        <!-- Export PDF -->
        <app-export-pdf
            *ngIf="hierarchicalData && hierarchicalData.length > 0"
            [getElementReferences]="getGanttElementForExport.bind(this)"
            [pdfName]="'task-gantt-chart-' + (task?.name || 'export')"
        ></app-export-pdf>

        <!-- Back to Task Details -->
        <a
            *ngIf="hierarchicalData && hierarchicalData.length > 0"
            [routerLink]="['', 'plan-task', 'detail', task?.id]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-eye me-2"></i>
            <span>{{ 'translate_task_details' | translate }}</span>
        </a>

        <!-- Back to Plan Details -->
        <a
            *ngIf="task?.plan"
            [routerLink]="['', 'plan', 'detail', task?.plan.id]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-share me-2"></i>
            <span>{{ 'translate_plan' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <div content>
        <div class="p-4">
            <!-- Loading state -->
            <div *ngIf="isLoading" class="py-8 text-center">
                <i class="fa-light fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">{{ 'translate_loading' | translate }}...</p>
            </div>

            <!-- Error state -->
            <div *ngIf="error && !isLoading" class="py-8 text-center">
                <i
                    class="fa-light fa-exclamation-triangle fa-2x text-red-500"
                ></i>
                <p class="mt-2 text-red-600">{{ error }}</p>
            </div>

            <!-- Gantt Chart -->
            <app-gantt-chart
                #ganttChart
                *ngIf="
                    !isLoading &&
                    !error &&
                    hierarchicalData &&
                    hierarchicalData.length > 0
                "
                [rawData]="hierarchicalData"
                [dataMapper]="taskDataMapper"
                [config]="ganttConfig"
                [isLoading]="isLoading"
                [title]="task?.plan.name"
                [startDate]="taskStartDate"
                [endDate]="taskEndDate"
                [progress]="task?.plan.progress"
                (taskClick)="onTaskClick($event)"
                (taskToggle)="onTaskToggle($event)"
            ></app-gantt-chart>

            <!-- No data state -->
            <div
                *ngIf="
                    !isLoading &&
                    !error &&
                    (!hierarchicalData || hierarchicalData.length === 0)
                "
                class="py-8 text-center"
            >
                <i class="fa-light fa-calendar-xmark fa-2x text-gray-400"></i>
                <p class="mt-2 text-gray-600">
                    {{ 'translate_no_data_available' | translate }}
                </p>
            </div>
        </div>
    </div>
</app-page>
