import { Validators } from '@angular/forms';
import { MnmFormField } from '@masar/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },

    {
        name: 'nameAr',
        type: 'text',
        label: 'translate_training_program_name_in_arabic',
        validators: [Validators.required],
    },
    {
        name: 'nameEn',
        type: 'text',
        label: 'translate_training_program_name_in_english',
    },
    {
        name: 'year',
        type: 'number',
        label: 'translate_year',
    },
    {
        name: 'hours',
        type: 'number',
        label: 'translate_training_hours',
    },
];
