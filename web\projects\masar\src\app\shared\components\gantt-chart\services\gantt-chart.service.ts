import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import {
    GanttItem,
    GanttDataMapper,
    GanttConfig,
} from '../interfaces/gantt.interfaces';
import { GanttDateService } from './gantt-date.service';
import { GanttDataProcessorService } from './gantt-data-processor.service';
import { GanttTimelineService, TimelineData } from './gantt-timeline.service';
import {
    GanttPositionCalculatorService,
    TimelineBounds,
    TaskPosition,
} from './gantt-position-calculator.service';

export interface GanttState<T> {
    processedData: GanttItem<T>[];
    expandedTasks: Set<string>;
    adjustedStartDate: Date | null;
    adjustedEndDate: Date | null;
    timelineData: TimelineData | null;
    timelineBounds: TimelineBounds | null;
    mergedConfig: GanttConfig;
    isLoading: boolean;
    hasInitialExpansionBeenDone: boolean;
}

@Injectable()
export class GanttChartService<T = unknown> {
    private defaultConfig: GanttConfig = {
        taskHeight: 60,
        subtaskHeight: 50,
        taskColors: {
            background: 'bg-blue-400',
            progress: 'bg-blue-600',
            progressBadge: 'bg-blue-100 text-blue-800',
        },
        subtaskColors: {
            background: 'bg-green-300',
            progress: 'bg-green-500',
            progressBadge: 'bg-green-100 text-green-800',
        },
        showProgress: true,
        allowExpansion: true,
        dateFormat: 'dd/MM/yyyy',
        initiallyExpanded: false,
    };

    private stateSubject = new BehaviorSubject<GanttState<T>>({
        processedData: [],
        expandedTasks: new Set<string>(),
        adjustedStartDate: null,
        adjustedEndDate: null,
        timelineData: null,
        timelineBounds: null,
        mergedConfig: this.defaultConfig,
        isLoading: false,
        hasInitialExpansionBeenDone: false,
    });

    public get state$(): Observable<GanttState<T>> {
        return this.stateSubject.asObservable();
    }

    public constructor(
        private dateService: GanttDateService,
        private dataProcessor: GanttDataProcessorService,
        private timelineService: GanttTimelineService,
        private positionCalculator: GanttPositionCalculatorService
    ) {}

    /**
     * Initializes or updates the Gantt chart with new data
     */
    public initializeChart(
        rawData: T[] | null,
        dataMapper: GanttDataMapper<T> | null,
        existingData: GanttItem<T>[] | null,
        config: Partial<GanttConfig>,
        startDate?: Date,
        endDate?: Date,
        adjustDatesBy?: number
    ): void {
        const currentState = this.stateSubject.value;

        // Update loading state
        this.updateState({ isLoading: true });

        try {
            // Merge configuration
            const mergedConfig = { ...this.defaultConfig, ...config };

            // Clear caches
            this.clearAllCaches();

            // Process data
            let result;
            if (rawData?.length && dataMapper) {
                result = this.dataProcessor.processRawData(
                    rawData,
                    dataMapper,
                    adjustDatesBy
                );
            } else if (existingData?.length) {
                result = this.dataProcessor.processGanttData(
                    existingData,
                    adjustDatesBy
                );
            } else {
                this.updateState({
                    processedData: [],
                    adjustedStartDate: null,
                    adjustedEndDate: null,
                    timelineData: null,
                    timelineBounds: null,
                    isLoading: false,
                });
                return;
            }

            // Calculate date range
            let finalStartDate: Date;
            let finalEndDate: Date;

            if (startDate && endDate) {
                finalStartDate = new Date(startDate.getTime());
                finalEndDate = new Date(endDate.getTime());

                // Adjust provided dates if needed
                if (adjustDatesBy && adjustDatesBy !== 0) {
                    const adjustedStart = this.dateService.parseUtcDate(
                        this.dateService.addDays(finalStartDate, adjustDatesBy)
                    );
                    if (adjustedStart) {
                        finalStartDate =
                            this.dateService.getFirstDayOfMonth(adjustedStart);
                    }
                }
            } else if (result.dateRange) {
                finalStartDate = result.dateRange.start;
                finalEndDate = result.dateRange.end;
            } else {
                // Fallback to current date
                const now = new Date();
                finalStartDate = this.dateService.getFirstDayOfMonth(now);
                finalEndDate = this.dateService.getLastDayOfMonth(now);
            }

            // Generate timeline
            const timelineData = this.timelineService.generateTimeline(
                finalStartDate,
                finalEndDate
            );
            const timelineBounds = this.positionCalculator.createTimelineBounds(
                finalStartDate,
                finalEndDate
            );

            // Handle initial expansion
            const expandedTasks = this.shouldDoInitialExpansion(
                mergedConfig,
                currentState.hasInitialExpansionBeenDone
            )
                ? this.getInitiallyExpandedTasks(result.processedData)
                : currentState.expandedTasks;

            // Update state
            this.updateState({
                processedData: result.processedData,
                adjustedStartDate: finalStartDate,
                adjustedEndDate: finalEndDate,
                timelineData,
                timelineBounds,
                mergedConfig,
                expandedTasks,
                hasInitialExpansionBeenDone: true,
                isLoading: false,
            });
        } catch (error) {
            this.updateState({ isLoading: false });
        }
    }

    /**
     * Gets the current state snapshot
     */
    public getCurrentState(): GanttState<T> {
        return this.stateSubject.value;
    }

    /**
     * Toggles the expansion state of a task
     */
    public toggleTask(taskId: string): void {
        const currentState = this.stateSubject.value;
        const newExpandedTasks = new Set(currentState.expandedTasks);

        if (newExpandedTasks.has(taskId)) {
            newExpandedTasks.delete(taskId);
        } else {
            newExpandedTasks.add(taskId);
        }

        this.updateState({ expandedTasks: newExpandedTasks });
    }

    /**
     * Checks if a task is expanded
     */
    public isTaskExpanded(taskId: string): boolean {
        return this.stateSubject.value.expandedTasks.has(taskId);
    }

    /**
     * Checks if an item has children
     */
    public hasChildren(item: GanttItem<T>): boolean {
        return this.dataProcessor.hasChildren(item);
    }

    /**
     * Calculates task position
     */
    public getTaskPosition(item: GanttItem<T>): TaskPosition {
        const state = this.stateSubject.value;
        if (!state.timelineBounds) {
            return { left: '0%', width: '0%' };
        }

        return this.positionCalculator.calculateTaskPosition(
            item,
            state.timelineBounds
        );
    }

    /**
     * Formats a date for display
     */
    public formatDate(dateInput: string | Date): string {
        return this.dateService.formatDate(dateInput);
    }

    /**
     * Formats tooltip text for an item
     */
    public formatTooltip(item: GanttItem<T>): string {
        if (!item) return '';

        const fromDate = item.from ? this.formatDate(item.from) : '';
        const toDate = item.to ? this.formatDate(item.to) : '';

        if (fromDate && toDate) {
            return `${fromDate} - ${toDate}`;
        } else if (fromDate) {
            return `Start: ${fromDate}`;
        } else if (toDate) {
            return `End: ${toDate}`;
        }

        return '';
    }

    /**
     * Clears all internal caches
     */
    public clearAllCaches(): void {
        this.dataProcessor.clearCaches();
        this.positionCalculator.clearCache();
    }

    /**
     * Resets the chart state
     */
    public resetState(): void {
        this.stateSubject.next({
            processedData: [],
            expandedTasks: new Set<string>(),
            adjustedStartDate: null,
            adjustedEndDate: null,
            timelineData: null,
            timelineBounds: null,
            mergedConfig: this.defaultConfig,
            isLoading: false,
            hasInitialExpansionBeenDone: false,
        });
        this.clearAllCaches();
    }

    private updateState(partialState: Partial<GanttState<T>>): void {
        const currentState = this.stateSubject.value;
        this.stateSubject.next({ ...currentState, ...partialState });
    }

    private shouldDoInitialExpansion(
        config: GanttConfig,
        hasBeenDone: boolean
    ): boolean {
        return !!config.initiallyExpanded && !hasBeenDone;
    }

    private getInitiallyExpandedTasks(items: GanttItem<T>[]): Set<string> {
        const expandedTasks = new Set<string>();

        const expandTasksWithChildren = (itemList: GanttItem<T>[]): void => {
            itemList.forEach(item => {
                if (this.hasChildren(item)) {
                    expandedTasks.add(item.id);
                    expandTasksWithChildren(item.children!);
                }
            });
        };

        expandTasksWithChildren(items);
        return expandedTasks;
    }
}
