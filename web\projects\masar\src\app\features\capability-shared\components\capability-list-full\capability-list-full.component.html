<!-- Filter box-->
<app-filter-result-box>
    <!-- Name-->
    <app-search-input
        *ngIf="shownFilters.includes('keyword')"
        [(ngModel)]="tableController.filter.data.keyword"
        [tableController]="tableController"
    ></app-search-input>

    <!-- Tournaments -->
    <ng-select
        *ngIf="shownFilters.includes('tournament')"
        [items]="tournamentLoader.items$ | async"
        [typeahead]="tournamentLoader.itemInput$"
        [loading]="tournamentLoader.itemsLoading"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_excellence_tournaments' | translate }}"
        [(ngModel)]="tableController.filter.data.tournamentIds"
        (change)="tableController.filter$.next(true)"
        (open)="tournamentLoader?.loadInitialList()"
    >
    </ng-select>

    <!-- Pillars -->
    <ng-select
        *ngIf="shownFilters.includes('pillar')"
        [items]="pillarLoader.items$ | async"
        [typeahead]="pillarLoader.itemInput$"
        [loading]="pillarLoader.itemsLoading"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_pillars' | translate }}"
        [(ngModel)]="tableController.filter.data.pillarIds"
        (change)="tableController.filter$.next(true)"
        (open)="pillarLoader?.loadInitialList()"
    >
    </ng-select>

    <!-- Standards -->
    <ng-select
        *ngIf="shownFilters.includes('standard')"
        [items]="standardLoader.items$ | async"
        [typeahead]="standardLoader.itemInput$"
        [loading]="standardLoader.itemsLoading"
        bindValue="id"
        bindLabel="name"
        [multiple]="true"
        placeholder="{{ 'translate_excellence_standards' | translate }}"
        [(ngModel)]="tableController.filter.data.standardIds"
        (change)="tableController.filter$.next(true)"
        (open)="standardLoader?.loadInitialList()"
    >
    </ng-select>
</app-filter-result-box>

<!-- Table -->
<app-list-loading [items]="tableController.items">
    <app-capability-list
        #list
        [items]="tableController.items"
        (order)="order($event)"
        [enableDrag]="!!orderCallback && !tableController.isLoading"
    ></app-capability-list>
    <app-table-pagination
        [tableController]="tableController"
    ></app-table-pagination>
</app-list-loading>
