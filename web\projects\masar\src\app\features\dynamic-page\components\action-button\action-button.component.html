<button
    class="btn btn-sm relative flex items-center gap-1 px-3 py-3"
    [ngClass]="'btn-' + (type ?? 'info')"
    (click)="onClick()"
    [disabled]="isDisabled"
    [appTooltip]="tooltip ?? '' | translate"
>
    <i
        *ngIf="icon"
        class="fas fa-fw"
        [ngClass]="'fa-' + icon"
        [class.text-green-600]="isDone !== undefined && isDone"
        [class.text-yellow-600]="isDone !== undefined && !isDone"
    ></i>

    <span *ngIf="label" class="text-xs">{{ label | translate }}</span>

    <span
        *ngIf="progress !== undefined && progress !== null"
        class="absolute bottom-full left-1/2 -translate-x-1/2 translate-y-1/2 transform whitespace-nowrap rounded-md bg-green-500 px-1 py-0.5 text-xs font-bold shadow"
    >
        {{ progress }}%
    </span>

    <span
        *ngIf="count !== undefined && count !== null && count > 0"
        class="absolute -end-1.5 -top-1.5 flex h-4 w-4 items-center justify-center whitespace-nowrap rounded-full bg-green-500 text-xs font-bold shadow"
    >
        {{ count }}
    </span>
</button>
