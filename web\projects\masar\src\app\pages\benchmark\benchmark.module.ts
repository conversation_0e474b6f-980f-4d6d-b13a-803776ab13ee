import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NewComponent } from './new/new.component';
import { ListComponent } from './list/list.component';
import { BenchmarkRoutingModule } from './benchmark.routing';
import { SharedModule } from '@masar/shared/shared.module';
import { BenchmarkComponent } from './benchmark.component';
import { BenchmarkService } from './benchmark.service';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { NgSelectModule } from '@ng-select/ng-select';
import { MasarModule } from '@masar/features/masar/masar.module';
import { OwningKpiResultListComponent } from './new/helpers/components/owning-kpi-result-list/owning-kpi-result-list.component';
import { BenchmarkKpiResultFieldComponent } from './new/helpers/components/benchmark-kpi-result-field/benchmark-kpi-result-field.component';
import { DetailComponent } from './detail/detail.component';
import { SubmitReportComponent } from './list/helpers/components/submit-report/submit-report.component';
import { ShowReportComponent } from './list/helpers/components/show-report/show-report.component';
import { TranslationModule } from '@ng-omar/translation';
import { DashboardComponent } from '@masar/pages/benchmark/dashboard/dashboard.component';
import { DynamicPageModule } from '@masar/features/dynamic-page/dynamic-page.module';

@NgModule({
    declarations: [
        BenchmarkComponent,
        NewComponent,
        ListComponent,
        DetailComponent,
        DashboardComponent,

        // Other
        BenchmarkKpiResultFieldComponent,
        OwningKpiResultListComponent,
        SubmitReportComponent,
        ShowReportComponent,
    ],
    imports: [
        CommonModule,
        BenchmarkRoutingModule,
        TranslationModule,
        SharedModule,
        FormsModule,
        ReactiveFormsModule,
        SweetAlert2Module,
        NgSelectModule,
        MasarModule,
        DynamicPageModule,
    ],
    providers: [BenchmarkService],
})
export class BenchmarkModule {}
