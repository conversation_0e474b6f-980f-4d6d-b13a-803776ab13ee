import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { marked } from 'marked';

@Component({
    selector: 'app-ai-analysis-result',
    templateUrl: './ai-analysis-result.component.html',
})
export class AiAnalysisResultComponent implements OnInit, OnDestroy {
    @Input() public result$: Observable<string>;

    public result: string = '';

    private unsubscribeAll = new Subject();

    public ngOnInit(): void {
        let result = '';
        this.result$.pipe(takeUntil(this.unsubscribeAll)).subscribe(r => {
            result += r;
            this.result = marked.parse(result) as string;
        });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }
}
