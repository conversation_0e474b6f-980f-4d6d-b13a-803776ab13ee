import {
    Item,
    KpiType,
    OperationProcedure,
    StrategicGoal,
    User,
} from './index';
import { Department } from './department.model';
import { KpiResult } from './kpi-result.model';
import { Operation } from './operation.model';
import { KpiMeasurementCycleType } from '../types';
import { EvaluateItem } from '@masar/features/evaluate/interfaces';

export interface K<PERSON> extends EvaluateItem {
    id: string;
    code: string;
    type: KpiType;
    name: string;
    units: string;
    dataEntryMethod: 'upon_request' | 'dynamic';
    unitsDescription: string;
    calculateAbType: string;
    formula: string;
    formulaDescriptionA: string;
    formulaDescriptionB: string;
    direction: string;
    source: string;
    creationYear: number;
    description: string;
    measurementCycle: KpiMeasurementCycleType;
    balancedBehaviorCard?: Item;
    isSpecial: boolean;
    isTrend: boolean;
    benchmarkCount: number;
    isExemptFromEvaluation: boolean;
    strategicGoals: StrategicGoal[];
    achieved: number;
    latestResultModification: Date;
    resultModifiedBy: User;

    nameAr: string;
    nameEn: string;
    descriptionAr: string;
    descriptionEn: string;

    owningDepartment: Department;

    measuringDepartment: Department;

    level1Department: Department;
    level2Department: Department;

    departments: Department[];
    operations: Operation[];
    operationProcedures: OperationProcedure[];

    // Cycle results.
    resultsFromYear: number;
    resultsToYear: number;
    results: KpiResult[];

    // Used for creation and editing.
    decreaseIsBest: boolean;

    initialResultSource: string;
    initialResult: number;
    initialResultDetails: string;

    yearsInfo: {
        year: number;
        departmentsInfo: {
            hasResult: boolean;
            department: Department;
        }[];
    }[];

    evaluateAverageScores?: number;
    latestEvaluateModification?: Date;
    evaluateModifiedBy: User;
    weight: number;
}
