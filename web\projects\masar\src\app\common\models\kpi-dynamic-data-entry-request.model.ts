import { KpiResultPeriod } from '@masar/common/models/kpi-result-period.model';
import { FlowItem } from '@masar/features/flow/interfaces';

type KpiDynamicDataEntryRequestFlowState =
    | 'submitted'
    | 'rejected'
    | 'approved'
    | 'approved:final';

export interface KpiDynamicDataEntryRequest
    extends FlowItem<KpiDynamicDataEntryRequestFlowState> {
    id: string;
    creationTime: Date;
    period: KpiResultPeriod;
    a?: number;
    b?: number;
    result?: number;
    achieved?: number;
}
