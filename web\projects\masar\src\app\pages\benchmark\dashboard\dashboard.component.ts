import { Component, OnDestroy } from '@angular/core';
import { BenchmarkService } from '@masar/pages/benchmark/benchmark.service';
import {
    StatisticsGroup,
    StatisticsGroupLinkMap,
} from '@masar/features/dynamic-page/components/statistics-section/interfaces';
import { YearService } from '@masar/core/services/year.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
})
export class DashboardComponent implements OnDestroy {
    public statisticsGroups?: StatisticsGroup[];

    public statisticsGroupLinkMap: StatisticsGroupLinkMap = {
        'benchmark-all': () => ({ url: ['', 'benchmark'] }),
        'benchmark-first-approval': () => ({
            url: ['', 'benchmark'],
            queryParams: {
                filter: JSON.stringify({
                    data: { approvalStatuses: ['first'] },
                }),
            },
        }),
        'benchmark-second-approval': () => ({
            url: ['', 'benchmark'],
            queryParams: {
                filter: JSON.stringify({
                    data: { approvalStatuses: ['second'] },
                }),
            },
        }),
        'benchmark-strategic-goal': item => ({
            url: ['', 'benchmark'],
            queryParams: {
                filter: JSON.stringify({
                    data: { goalIds: [item.id] },
                }),
            },
        }),
    };

    private unsubscribeAll = new Subject();

    public constructor(
        private readonly benchmarkService: BenchmarkService,
        private readonly yearService: YearService
    ) {
        this.refreshData();

        // Subscribe to year changes
        this.yearService.changes$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(() => {
                this.refreshData();
            });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public yearChanged(): void {
        this.refreshData();
    }

    private refreshData(): void {
        const selectedYear = this.yearService.get();
        this.benchmarkService
            .generalStatistics(selectedYear)
            .subscribe(data => {
                this.statisticsGroups = data;
            });
    }
}
