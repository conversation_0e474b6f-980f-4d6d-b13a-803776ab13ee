import {
    Component,
    Input,
    On<PERSON>hang<PERSON>,
    OnDestroy,
    OnInit,
    SimpleChanges,
    TemplateRef,
    ViewChild,
} from '@angular/core';
import { Item, Kpi, KpiStatus } from '@masar/common/models';
import { first, takeUntil } from 'rxjs/operators';
import { TableController } from '@masar/common/misc/table/table-controller';
import {
    AppSettingFetcherService,
    MiscApiService,
    YearService,
} from '@masar/core/services';
import { ActivatedRoute, Router } from '@angular/router';
import { forkJoin, Observable, Subject } from 'rxjs';
import { permissionList } from '@masar/common/constants';
import { TranslateService } from '@ngx-translate/core';
import { Field, KpiListComponent } from '../kpi-list/kpi-list.component';
import { Filter, TableResult } from '@masar/common/misc/table';
import { KpiListWithResultComponent } from '../kpi-list-with-result/kpi-list-with-result.component';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { SharedKpiService } from '@masar/shared/services';
import { EvaluateService } from '@masar/features/evaluate/services/evaluate.service';
import { OrganizationOrigin } from '@masar/common/enums';

interface FilterData {
    keyword?: string;
    tagIds?: string[];
    departmentIds?: string[];
    measuringDepartmentIds?: any[];
    goalIds?: string[];
    cycles?: string[];
    typeIds?: string[];
    subset?: number;
    progresses?: string[];
    kpiNumber: string;
    hasBenchmarks: -1 | 0 | 1;
    standardIds: string[];
    evaluationStatus: -1 | 0 | 1;
    evaluationScoreBandIds: string[];
    isSharedKpi: boolean;
    excludeChildDepartments: boolean;
}

type ShownFilter =
    | 'name'
    | 'number'
    | 'tag'
    | 'cycle'
    | 'type'
    | 'state'
    | 'department'
    | 'measuring_department'
    | 'strategic_goal'
    | 'has_benchmark'
    | 'standard'
    | 'evaluation_status'
    | 'evaluation_score_band'
    | 'show_shared_kpis'
    | 'exclude_child_departments';

@Component({
    selector: 'app-kpi-list-full',
    templateUrl: './kpi-list-full.component.html',
    styles: [
        `
            .benchmark-badge > span {
                max-width: 0;
            }

            .benchmark-badge:hover > span {
                max-width: 120px;
            }
        `,
    ],
})
export class KpiListFullComponent implements OnInit, OnDestroy, OnChanges {
    @ViewChild('list') public list:
        | KpiListComponent
        | KpiListWithResultComponent;

    @Input() public view: 'default' | 'with_result' = 'default';
    @Input() public display: KpiStatus = 'active'; // Tells us which kpis are currently listed.
    @Input() public isFilterInUrl = false;
    @Input() public shownFilters: ShownFilter[] = [
        'name',
        'number',
        'tag',
        'cycle',
        'type',
        'state',
        'department',
        'measuring_department',
        'strategic_goal',
        'has_benchmark',
        'standard',
    ];

    @Input() public orderCallback: (
        orderedItems: Kpi[],
        filter: Filter<FilterData>
    ) => Observable<any>;

    @Input() public alternateListCallback: (
        filter: Filter<FilterData>,
        status: KpiStatus
    ) => Observable<TableResult<Kpi>>;

    @Input() public shownFields: Field[] = ['name', 'cycle', 'last_modified'];
    @Input() public customFields: {
        name: string;
        template: TemplateRef<any>;
    }[];

    public measuringDepartmentParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;

    public measuringDepartmentChildrenFetcher: (
        parentId: string
    ) => Observable<Item[]>;

    public measuringCycles: Item[];
    public permissionList = permissionList;

    public tableController: TableController<Kpi, FilterData>;
    public tags: Item[] = [];
    public cycles: Item[] = [];
    public types: Item[] = [];
    public departments: Item[] = [];
    public goals: Item[] = [];
    public kpiProgressStates: Item[] = [];
    public hasBenchmarksItems: { id: number; name: string }[] = [];
    public standards: Item[] = [];
    public evaluationStatuses: { id: number; name: string }[] = [];
    public evaluationScoreBands: Item[] = [];
    public isMeasuringDepartment: boolean = false;
    public readonly organizationOrigin = OrganizationOrigin;

    private fieldControlRefresher = new Subject();
    private unsubscribeAll = new Subject();
    public constructor(
        private activatedRoute: ActivatedRoute,
        private sharedKpiService: SharedKpiService,
        private router: Router,
        private yearService: YearService,
        private miscApiService: MiscApiService,
        private appSettingFetcherService: AppSettingFetcherService,
        private translateService: TranslateService,
        private evaluateService: EvaluateService
    ) {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(data => {
                data.kpiSetting.optionalFields[0].name ===
                'measuring_department'
                    ? (this.isMeasuringDepartment =
                          data.kpiSetting.optionalFields[0].isEnabled)
                    : null;
            });
        this.initItems();
    }

    public ngOnInit(): void {
        this.initTableController();
        this.initFieldControlRefresher();
        this.fieldControlRefresher.next();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
        this.fieldControlRefresher.complete();
        this.tableController.stop();
    }

    public ngOnChanges(changes: SimpleChanges): void {
        const displayChange = changes['display'];
        const alternateListCallbackChange = changes['alternateListCallback'];
        if (!displayChange && !alternateListCallbackChange) return;

        this.reload();
    }

    public refreshItems(resetPageNumber: boolean = false): void {
        this.tableController.filter$.next(resetPageNumber);
    }
    public onMeasurementDepartmentChange(value: any[]): void {
        if (value) {
            this.tableController.filter$.next(true);
        }
    }

    public order(event: CdkDragDrop<Kpi>): void {
        if (!this.orderCallback) return;

        this.tableController.order(event.previousIndex, event.currentIndex);
    }

    public loadEvaluationScoreBands(evaluationId: string): void {
        this.evaluateService
            .listScoreBands(evaluationId, 'kpi')
            .subscribe(items => (this.evaluationScoreBands = items));
    }
    public get showMeasuringDepartment(): boolean {
        return (
            this.isMeasuringDepartment &&
            !this.router.url.includes('kpi-evaluation')
        );
    }
    private reload(): void {
        this.initTableController();
        this.fieldControlRefresher.next();
    }

    private initTableController(): void {
        this.tableController?.stop();
        this.tableController = null;

        const callback: (
            filter: Filter<FilterData>,
            status: KpiStatus
        ) => Observable<TableResult<Kpi>> =
            this.alternateListCallback ??
            ((filter, status) => {
                return this.sharedKpiService.list(
                    filter.data.keyword,
                    filter.data.tagIds,
                    filter.data.departmentIds,
                    filter.data.measuringDepartmentIds.map(val => val.id),
                    filter.data.goalIds,
                    filter.data.cycles,
                    filter.data.typeIds,
                    filter.data.progresses,
                    filter.data.kpiNumber,
                    status,
                    filter.data.hasBenchmarks,
                    filter.data.standardIds,
                    filter.data.evaluationStatus,
                    filter.data.evaluationScoreBandIds,
                    filter.data.isSharedKpi,
                    filter.data.excludeChildDepartments,
                    filter.pageNumber,
                    filter.pageSize
                );
            });

        const setupController = (
            // year: string,
            departmentIds: string[],
            measuringDepartmentIds: string[],
            progresses: string[],
            goalIds: string[],
            typeIds: string[]
        ): void => {
            this.tableController = new TableController<Kpi, FilterData>(
                filter => {
                    return callback(filter, this.display);
                },
                {
                    data: {
                        keyword: '',
                        tagIds: [],
                        departmentIds: departmentIds || [],
                        measuringDepartmentIds: measuringDepartmentIds || [],
                        cycles: [],
                        typeIds: typeIds || [],
                        subset: -1,
                        // year: parseInt(year) || new Date().getFullYear(),
                        progresses: progresses || [],
                        goalIds: goalIds || [],
                        kpiNumber: '',
                        hasBenchmarks: 0,
                        standardIds: [],
                        evaluationStatus: null,
                        evaluationScoreBandIds: [],
                        isSharedKpi: false,
                        excludeChildDepartments: false,
                    },
                    pageSize: 50,
                },
                this.isFilterInUrl
                    ? {
                          routingControls: {
                              router: this.router,
                              activatedRoute: this.activatedRoute,
                          },
                          orderCallback: this.orderCallback,
                      }
                    : {
                          orderCallback: this.orderCallback,
                      }
            );
            this.tableController.start();

            // Refresh table whenever year changes.
            this.yearService.changes$
                .pipe(takeUntil(this.unsubscribeAll))
                .subscribe(() => this.tableController.filter$.next());
        };

        this.activatedRoute.queryParamMap.pipe(first()).subscribe(params => {
            // const year = params.get('year');
            const departmentIds = params.getAll('departmentIds');
            const measuringDepartmentIds = params.getAll(
                'measuringDepartmentIds'
            );
            const progresses = params.getAll('progresses');
            const goalIds = params.getAll('goalIds');
            const typeIds = params.getAll('typeIds');

            setupController(
                /*year, */
                departmentIds,
                measuringDepartmentIds,
                progresses,
                goalIds,
                typeIds
            );
        });
    }

    private initItems(): void {
        forkJoin([
            this.translateService.get('translate_no_benchmarks'),
            this.translateService.get('translate_all'),
            this.translateService.get('translate_with_benchmarks'),
        ])
            .pipe(first())
            .subscribe(translations => {
                translations.forEach((str, i) => {
                    this.hasBenchmarksItems.push({
                        id: i - 1,
                        name: str,
                    });
                });
            });

        forkJoin([
            this.translateService.get('translate_evaluated'),
            this.translateService.get('translate_no_evaluations'),
        ])
            .pipe(first())
            .subscribe(translations => {
                this.evaluationStatuses.push({
                    id: 1,
                    name: translations[0], // translate_evaluated
                });
                this.evaluationStatuses.push({
                    id: -1,
                    name: translations[1], // translate_no_evaluations
                });
            });

        this.miscApiService.kpiTags().subscribe(items => (this.tags = items));

        this.miscApiService
            .departments()
            .subscribe(items => (this.departments = items));

        this.miscApiService
            .getList('strategic-goal', undefined, true)
            .subscribe(items => (this.goals = items));

        this.miscApiService
            .getList('kpi-progress-state')
            .subscribe(items => (this.kpiProgressStates = items));

        this.miscApiService.kpiTypes().subscribe(items => (this.types = items));

        this.miscApiService.getList('kpi-cycle').subscribe(items => {
            this.measuringCycles = items;
            this.cycles = items;
        });

        this.miscApiService
            .standards()
            .subscribe(items => (this.standards = items));
        this.measuringDepartmentParentFetcher = (childId: string) =>
            this.miscApiService.parentDepartment(childId);

        this.measuringDepartmentChildrenFetcher = (parentId: string) =>
            this.miscApiService.departments({
                parentDepartmentId: parentId,
                respectHierarchy: true,
                scope: 'all',
            });
    }

    private initFieldControlRefresher(): void {
        this.fieldControlRefresher
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(() => {
                if (
                    ['active', 'hidden'].includes(this.display) &&
                    !this.shownFields.includes('evaluation')
                ) {
                    this.shownFields.push('achieved');
                }
            });
    }
}
