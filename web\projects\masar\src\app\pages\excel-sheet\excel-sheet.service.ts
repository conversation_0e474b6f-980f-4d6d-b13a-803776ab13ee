import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Department } from './components/departments/departments.component';
import { Observable } from 'rxjs';
import { Center } from './components/centers/centers.component';
import { Service } from './components/services/services.component';
import { DataEntry } from './components/data-entry/data-entry.component';

@Injectable()
export class ExcelSheetService {
    private readonly api = 'https://db-ajman.syscodeia.ae';

    public constructor(private readonly http: HttpClient) {}

    public createDepartment(name: string): Observable<Department> {
        return this.http.post<Department>(`${this.api}/departments`, {
            name,
        });
    }

    public getDepartments(): Observable<Department[]> {
        return this.http.get<Department[]>(`${this.api}/departments`);
    }

    public deleteDepartment(id: number): Observable<void> {
        return this.http.delete<void>(`${this.api}/departments/${id}`);
    }

    public createCenter(name: string): Observable<Center> {
        return this.http.post<Center>(`${this.api}/centers`, {
            name,
        });
    }

    public getCenters(): Observable<Center[]> {
        return this.http.get<Center[]>(`${this.api}/centers`);
    }

    public deleteCenter(id: number): Observable<void> {
        return this.http.delete<void>(`${this.api}/centers/${id}`);
    }

    public createService(name: string): Observable<Service> {
        return this.http.post<Service>(`${this.api}/services`, {
            name,
        });
    }

    public getServices(args: string = ''): Observable<Service[]> {
        return this.http.get<Service[]>(`${this.api}/services${args}`);
    }

    public deleteService(id: number): Observable<void> {
        return this.http.delete<void>(`${this.api}/services/${id}`);
    }

    public createDataEntry(
        departmentId: number,
        centerId: number,
        year: number,
        month: number,
        serviceId: number,
        value: number
    ): Observable<DataEntry> {
        return this.http.post<DataEntry>(`${this.api}/data-entries`, {
            departmentId,
            centerId,
            serviceId,
            year,
            month,
            value,
        });
    }

    public updateDataEntryValue(
        id: number,
        value: number
    ): Observable<DataEntry> {
        return this.http.patch<DataEntry>(`${this.api}/data-entries/${id}`, {
            value,
        });
    }

    public deleteDataEntryValue(id: number): Observable<void> {
        return this.http.delete<void>(`${this.api}/data-entries/${id}`);
    }

    public getDataEntries(
        args = '?_expand=department&_expand=service'
    ): Observable<DataEntry[]> {
        return this.http.get<DataEntry[]>(`${this.api}/data-entries${args}`);
    }
}
