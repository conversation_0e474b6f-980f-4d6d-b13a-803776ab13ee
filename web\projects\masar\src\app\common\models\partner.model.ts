import { Operation } from './operation.model';
import { Item } from '@masar/common/models/item.model';
import { StrategicGoal } from '@masar/common/models/strategic-goal.model';
import { Plan } from '@masar/common/models/plan.model';
import { Service } from '@masar/common/models/service.model';
import { PartnershipType } from '@masar/pages/partnership-contract/interfaces';
import { User } from '@masar/common/models/user.model';
import { Department } from '@masar/common/models/department.model';

export interface Partner {
    id: string;
    name: string;
    partnershipContractCount: number;
    totalEvaluationPercentage: number;
    successFactors?: Item[];
    strategicGoals?: StrategicGoal[];
    operations?: Operation[];
    plans?: Plan[];
    services?: Service[];
    partnershipTypeCounts?: {
        partnershipType: PartnershipType;
        count: number;
    }[];
    createdByUser: User;
    createdByUserDepartment: Department;
    creationTime: Date;
}
