import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';
import { miscFunctions, Result } from 'mnm-webapp';
import { environment } from '@masar/env/environment';
import { ImprovementOpportunitySetting } from '@masar/common/models/improvement-opportunity-setting.model';

@Injectable()
export class ImprovementOpportunitySettingService {
    public constructor(private httpClient: HttpClient) {}

    public get(): Observable<ImprovementOpportunitySetting> {
        return this.httpClient
            .get<Result<ImprovementOpportunitySetting>>(
                `${environment.apiUrl}/setting/improvement-opportunity`
            )
            .pipe(map(res => res.extra));
    }

    public update(
        improvementOpportunitySetting: ImprovementOpportunitySetting
    ): Observable<ImprovementOpportunitySetting> {
        return this.httpClient
            .put<Result<ImprovementOpportunitySetting>>(
                `${environment.apiUrl}/setting/improvement-opportunity`,
                miscFunctions.objectToURLParams({
                    improvementOpportunitySetting: JSON.stringify(
                        improvementOpportunitySetting
                    ),
                })
            )
            .pipe(map(res => res.extra));
    }
}
