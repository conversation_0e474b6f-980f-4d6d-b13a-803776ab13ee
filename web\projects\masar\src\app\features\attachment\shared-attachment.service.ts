import { Injectable, NgModuleRef, NgZone } from '@angular/core';
import { functions } from '@masar/common/misc/functions';
import { Observable, Subject } from 'rxjs';
import { FileViewerComponent } from '@masar/features/masar/components';
import { miscFunctions, ModalService, Result } from 'mnm-webapp';
import { AttachmentFile, AttachmentOptions } from './interfaces';
import { AttachmentListComponent } from './attachment-list/attachment-list.component';
import { environment } from '@masar/env/environment';
import { map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { HttpQueryParameters } from '@masar/features/http-crud/http-query-parameters.interface';
import { getHttpParameters } from '@masar/features/http-crud/utils';

@Injectable()
export class SharedAttachmentService {
    public constructor(
        private readonly modalService: ModalService,
        private readonly moduleRef: NgModuleRef<any>,
        private readonly httpClient: HttpClient,
        private readonly ngZone: NgZone
    ) {}

    public showListAttachment(
        options?: AttachmentOptions
    ): Promise<AttachmentListComponent> {
        return this.modalService.show(AttachmentListComponent, {
            moduleRef: this.moduleRef,
            beforeInit: c => {
                c.options = options;
            },
        });
    }

    public listAttachments(
        id: string,
        endPoint: string,
        httpQueryParameters?: HttpQueryParameters
    ): Observable<AttachmentFile[]> {
        let params = getHttpParameters(httpQueryParameters);

        return this.httpClient
            .get<Result<AttachmentFile[]>>(
                `${environment.apiUrl}/${endPoint}/attachment/${id}`,
                { params }
            )
            .pipe(map(res => res.extra));
    }

    public createAttachment(
        id: string,
        attachment: AttachmentFile,
        endPoint: string
    ): Observable<AttachmentFile> {
        return this.httpClient
            .post<Result<AttachmentFile>>(
                `${environment.apiUrl}/${endPoint}/attachment/${id}`,
                miscFunctions.objectToURLParams({
                    attachment: JSON.stringify(attachment),
                })
            )
            .pipe(map(res => res.extra));
    }

    public deleteAttachment(
        attachmentId: string,
        endPoint: string
    ): Observable<string> {
        return this.httpClient
            .delete<Result>(
                `${environment.apiUrl}/${endPoint}/attachment?attachmentId=${attachmentId}`
            )
            .pipe(map(res => res.messages[0]));
    }

    public downloadAttachment(
        attachmentId?: string,
        endPoint?: string,
        fullEndPoint?: string
    ): Observable<Blob> {
        const url =
            fullEndPoint ||
            `${endPoint}/attachment/download?attachmentId=${attachmentId}`;

        return this.httpClient
            .get(`${environment.apiUrl}/${url}`, { responseType: 'blob' })
            .pipe(map(res => res));
    }

    public downloadBlob(file: Blob, type: string): void {
        if (type === 'application/pdf' || type.split('/')[0] === 'image') {
            const src = URL.createObjectURL(file);
            this.showFileModal(src);
            return;
        }

        this.ngZone.runOutsideAngular(() =>
            functions.downloadBlobIntoFile(file)
        );
    }

    public showFileModal(src: string): void {
        let subject = new Subject();
        this.modalService
            .show(FileViewerComponent, {
                moduleRef: this.moduleRef,
                onDismiss: () => {
                    subject.next();
                    subject.complete();
                },
                beforeInit: c => {
                    c.src = src;
                },
                size: { width: '100%', height: '100%' },
            })
            .then();
    }
}
