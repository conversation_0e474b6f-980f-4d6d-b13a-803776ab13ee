import { Component, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { miscFunctions } from 'mnm-webapp';
import { Observable, Subject } from 'rxjs';
import { Loader } from '@masar/common/misc/loader';
import {
    Department,
    Item,
    KpiReportFilter,
    KpiReportSetting,
} from '@masar/common/models';
import { AppSettingFetcherService, MiscApiService } from '@masar/core/services';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-kpi-report-filter',
    templateUrl: './kpi-report-filter.component.html',
})
export class KpiReportFilterComponent implements OnDestroy {
    public settings: KpiReportSetting = {
        columns: {
            units: false,
            formula: false,
            creationYear: false,
            measurementCycle: false,
            measuringDepartment: false,
            balancedBehaviorCard: false,
            formulaDescriptionA: false,
            formulaDescriptionB: false,
            source: false,
            direction: false,
            type: false,
            isTrend: false,
            isSpecial: false,
        },
        isGrouped: false,
    };

    public filter: KpiReportFilter = miscFunctions.getEmptyObject();
    public selectAllColumns: boolean = false;

    public years: number[];
    public types: Item[];
    public cycles: Item[];
    public tags: Item[];

    public isMeasuringDepartment: boolean = false;
    public kpiLoader: Loader<Item>;
    public measuringDepartmentParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public measuringDepartmentChildrenFetcher: (
        parentId: string
    ) => Observable<Item[]>;

    public departmentParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public departmentChildrenFetcher: (parentId: string) => Observable<Item[]>;
    private unsubscribeAll = new Subject();
    public constructor(
        private router: Router,
        miscApiService: MiscApiService,
        kpiSettingService: AppSettingFetcherService
    ) {
        miscApiService.years().subscribe(items => {
            this.years = items;
            if (items.length > 0) {
                // Years array is in descending order (newest to oldest)
                // Set default toYear to the latest year (first item in desc array)
                this.filter.toYear = items[0];
                // Set default fromYear to 3 years back or earliest year if less than 3 years available
                this.filter.fromYear =
                    items[items.length > 3 ? 2 : items.length - 1];
            }
        });
        miscApiService.kpiTypes().subscribe(items => (this.types = items));

        kpiSettingService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(data => {
                data.kpiSetting.optionalFields[0].name ===
                'measuring_department'
                    ? (this.isMeasuringDepartment =
                          data.kpiSetting.optionalFields[0].isEnabled)
                    : null;
            });

        miscApiService
            .getList('kpi-cycle')
            .subscribe(items => (this.cycles = items));

        miscApiService.kpiTags().subscribe(items => {
            {
                this.tags = items;
            }
        });

        this.departmentParentFetcher = (childId: string) =>
            miscApiService.parentDepartment(childId);
        this.departmentChildrenFetcher = (parentId: string) =>
            miscApiService.departments({
                parentDepartmentId: parentId,
                respectHierarchy: true,
            });
        this.measuringDepartmentParentFetcher = (childId: string) =>
            miscApiService.parentDepartment(childId);

        this.measuringDepartmentChildrenFetcher = (parentId: string) =>
            miscApiService.departments({
                parentDepartmentId: parentId,
                respectHierarchy: true,
                scope: 'all',
            });
        this.kpiLoader = new Loader<Item>(
            keyword =>
                miscApiService.kpis({
                    keyword,
                    scope: 'broad',
                }),
            []
        );
    }

    public ngOnDestroy(): void {
        this.kpiLoader.dispose();
    }

    public generateReport(): void {
        // Map the selected measuringDepartment objects to their ids
        if (Array.isArray(this.filter.measuringDepartmentIds)) {
            this.filter.measuringDepartmentIds =
                this.filter.measuringDepartmentIds.map(
                    department => department.id
                );
        }

        const queryParams = {
            settings: JSON.stringify(this.settings),
            filter: JSON.stringify(this.filter),
        };

        // Navigate to the report result page with the updated queryParams
        this.router.navigate(['', 'report', 'kpi', 'result'], { queryParams });
    }

    public checkAllColumnsAreSelected(): void {
        this.selectAllColumns = Object.keys(this.settings.columns).every(
            x => this.settings.columns[x]
        );
    }

    public toggle(): void {
        Object.keys(this.settings.columns).forEach(
            x => (this.settings.columns[x] = this.selectAllColumns)
        );
    }

    public setFilterDepartmentIds(items: Department[]): void {
        this.filter.departmentIds = items.map(x => x.id);
    }
    public onMeasurementDepartmentChange(value: any[]): void {
        this.filter.measuringDepartmentIds = value;
    }
}
