<app-content-loading
    [isLoading]="!item || !property"
    [isCentered]="propertyValueOptions.isCenter"
>
    <div
        *ngIf="
            propertyValueOptions.type === 'ring' ||
            (property.value !== null &&
                property.value !== undefined &&
                property.value !== '')
        "
        [dir]="propertyValueOptions.dir"
        [class.text-center]="propertyValueOptions.isCenter"
        [ngSwitch]="propertyValueOptions.type"
    >
        <ng-container *ngSwitchCase="'color'">
            <div
                class="flex h-10 w-10 items-center justify-center"
                [style.background]="property.value"
            ></div>
        </ng-container>

        <!-- Boolean -->
        <app-property-value-boolean
            *ngSwitchCase="'boolean'"
            [value]="property.value"
        ></app-property-value-boolean>

        <!-- Ring -->
        <app-property-value-ring
            *ngSwitchCase="'ring'"
            [value]="property.value"
            [radius]="propertyValueOptions.radius"
            [stroke]="propertyValueOptions.stroke"
        ></app-property-value-ring>

        <!-- Array -->
        <app-property-value-array
            *ngSwitchCase="'array'"
            [value]="property.value"
            [translateItemNamePipe]="propertyValueOptions.translateItemNamePipe"
        ></app-property-value-array>

        <!-- File -->
        <app-property-value-file
            *ngSwitchCase="'file'"
            [value]="property.value"
        ></app-property-value-file>

        <!-- Library File -->
        <ng-container *ngSwitchCase="'library-file'">
            <app-library-file-name
                *ngIf="property.value; else noFileTemplate"
                [libraryFile]="property.value"
            ></app-library-file-name>

            <ng-template #noFileTemplate>
                <span>{{ 'translate_no_file' | translate }}</span>
            </ng-template>
        </ng-container>

        <!-- Default -->
        <ng-container *ngSwitchDefault>
            <ng-container *ngIf="property.itemNavigate; else valueTemplate">
                <ng-container
                    *ngIf="
                        property.itemNavigate.permission as permission;
                        else linkTemplate
                    "
                >
                    <ng-container
                        *appHasPermissionId="
                            $any(permission);
                            else valueTemplate
                        "
                    >
                        <ng-container
                            [ngTemplateOutlet]="linkTemplate"
                        ></ng-container>
                    </ng-container>
                </ng-container>

                <ng-template #linkTemplate>
                    <a [routerLink]="property.itemNavigate.link">
                        <ng-container
                            [ngTemplateOutlet]="valueTemplate"
                        ></ng-container>
                    </a>
                </ng-template>
            </ng-container>

            <ng-template #valueTemplate>
                <ng-container
                    *ngIf="propertyValueOptions.useBadge; else textTemplate"
                >
                    <ng-container
                        [ngTemplateOutlet]="useBadgeTemplate"
                        [ngTemplateOutletContext]="{
                            color: propertyValueOptions.badgeColor
                                ? (propertyValueOptions.badgeColor
                                  | callFunction : item)
                                : 'gray'
                        }"
                    ></ng-container>
                </ng-container>
            </ng-template>

            <ng-template #useBadgeTemplate let-color="color">
                <span
                    class="inline-block whitespace-nowrap rounded-full px-2 py-1 text-xs font-semibold leading-none"
                    [ngClass]="[
                        'bg-' + color + '-200',
                        'text-' + color + '-800'
                    ]"
                >
                    <ng-container
                        [ngTemplateOutlet]="textTemplate"
                    ></ng-container>
                </span>
            </ng-template>

            <ng-template #textTemplate>
                <!-- Date -->
                <ng-container
                    *ngIf="
                        propertyValueOptions.type === 'date';
                        else plainTextTemplate
                    "
                >
                    {{ property.value | date }}
                </ng-container>

                <ng-template #plainTextTemplate>
                    <span>
                        {{
                            propertyValueOptions.translateItemNamePipe
                                ? (property.value
                                  | translateItem
                                      : propertyValueOptions.translateItemNamePipe
                                  | async)
                                : (property.value | translate)
                        }}
                    </span>
                </ng-template>
            </ng-template>
        </ng-container>
    </div>
</app-content-loading>
