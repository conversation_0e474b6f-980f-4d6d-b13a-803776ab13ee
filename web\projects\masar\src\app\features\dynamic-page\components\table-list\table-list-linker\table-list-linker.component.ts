import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TableListLinkerService } from '@masar/features/dynamic-page/components/table-list/table-list-linker/table-list-linker.service';
import { ListSectionData } from '@masar/features/dynamic-page/interfaces';

@Component({
    selector: 'app-table-list-linker',
    templateUrl: './table-list-linker.component.html',
    providers: [TableListLinkerService],
})
export class TableListLinkerComponent implements OnInit {
    @Input() public bodyParameterName: string;

    @Input() public endPoint: string;

    @Input() public data: ListSectionData<
        unknown,
        { submitted: EventEmitter<void> }
    >;

    @Output() public changed = new EventEmitter<void>();

    public constructor(
        public readonly tableListLinkerService: TableListLinkerService
    ) {}

    public ngOnInit(): void {
        this.tableListLinkerService.init(this.bodyParameterName, this.endPoint);
        this.tableListLinkerService.startList();
    }

    public delete(id: string): void {
        this.tableListLinkerService.delete(id, () => this.changed.emit());
    }
}
