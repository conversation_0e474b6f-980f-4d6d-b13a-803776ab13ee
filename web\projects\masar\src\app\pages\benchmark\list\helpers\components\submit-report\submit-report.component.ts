import {
    AfterViewInit,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    Output,
    ViewChild,
} from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { BenchmarkLibraryFile } from '@masar/common/models';
import { MiscApiService } from '@masar/core/services';
import { MnmFormState } from '@masar/shared/components';
import { finalize } from 'rxjs/operators';
import { BenchmarkService } from '../../../../benchmark.service';
import { fields } from './fields';

@Component({
    selector: 'app-submit-report',
    templateUrl: './submit-report.component.html',
})
export class SubmitReportComponent implements AfterViewInit {
    @Input() public benchmarkId: string;

    @Output() public submitted = new EventEmitter();

    @ViewChild('libraryFilesFieldRef')
    private libraryFilesFieldRef: ElementRef;

    public isSubmitting = false;
    public formState: MnmFormState;

    public constructor(
        private benchmarkService: BenchmarkService,
        private miscApiService: MiscApiService,
        private translateService: TranslateService,
        private notificationService: NotificationService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);
        this.loadItems();
    }

    public ngAfterViewInit(): void {
        this.formState.get('libraryFiles').customInputField =
            this.libraryFilesFieldRef;
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        this.benchmarkService
            .submitReport(this.benchmarkId, this.formState.group.getRawValue())
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(() => {
                this.translateService
                    .get('translate_item_added_successfully')
                    .subscribe(str => {
                        this.notificationService.notifySuccess(str);
                        this.submitted.next();
                    });
            });
    }

    public validLibraryFiles(
        items: BenchmarkLibraryFile[]
    ): BenchmarkLibraryFile[] {
        return items.filter(x => x.libraryFile);
    }

    private loadItems(): void {
        this.miscApiService.setMiscItems(this.formState, [
            ['benchmark-report-type', 'reportType'],
        ]);
    }
}
