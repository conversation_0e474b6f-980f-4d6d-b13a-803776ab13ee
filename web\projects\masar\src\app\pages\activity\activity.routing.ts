import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ActivityComponent } from './activity.component';
import { DetailComponent } from './detail/detail.component';
import { ListComponent } from './list/list.component';
import { NewComponent } from './new/new.component';

const routes: Routes = [
    {
        path: '',
        component: ActivityComponent,
        children: [
            {
                path: '',
                component: ListComponent,
                data: {
                    title: 'translate_activities',
                },
            },

            {
                path: 'new',
                component: NewComponent,
                data: {
                    title: 'translate_new_activity',
                },
            },

            {
                path: 'edit/:id',
                component: NewComponent,
                data: {
                    title: 'translate_edit_activity',
                },
            },

            {
                path: 'detail/:id',
                component: DetailComponent,
                data: {
                    title: 'translate_activity_details',
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class ActivityRoutingModule {}
