<app-content [contentTitle]="'translate_other_resources' | translate">
    <app-content-loading content [isLoading]="!plan">
        <table class="table-bordered table-striped table">
            <thead class="thead-dark">
                <th>{{ 'translate_classification' | translate }}</th>
                <th>{{ 'translate_resources' | translate }}</th>
                <th>{{ 'translate_count' | translate }}</th>
                <th>{{ 'translate_availability' | translate }}</th>
                <th>{{ 'translate_availability_mechanism' | translate }}</th>
            </thead>
            <tbody>
                <tr *ngFor="let item of plan?.otherResources">
                    <td class="max-w-[150px] break-words">
                        {{ item.planResourceClassification?.name }}
                    </td>
                    <td class="max-w-[150px] break-words">
                        {{ item.planClassifiedResource?.name }}
                    </td>
                    <td>{{ item.count }}</td>
                    <td>
                        {{
                            (item.isAvailable
                                ? 'translate_available'
                                : 'translate_unavailable'
                            ) | translate
                        }}
                    </td>
                    <td class="max-w-[150px] break-words">
                        {{ item.availabilityWay }}
                    </td>
                </tr>
            </tbody>
        </table>
    </app-content-loading>
</app-content>
