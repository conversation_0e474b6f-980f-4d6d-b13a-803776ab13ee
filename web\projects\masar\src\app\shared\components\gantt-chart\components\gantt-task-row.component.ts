import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    Output,
    TemplateRef,
} from '@angular/core';
import {
    GanttItem,
    GanttConfig,
    GanttTemplateContext,
    TaskPosition,
} from '../services';

@Component({
    selector: 'app-gantt-task-row',
    // cspell:disable
    template: `
        <div
            class="flex w-full border-b transition-colors duration-200 hover:bg-gray-50"
            [ngClass]="{
                'border-b-2 border-blue-200':
                    isExpanded && hasChildren && config.allowExpansion,
                'bg-gray-100': isPlanTaskRow()
            }"
            role="row"
            [attr.aria-expanded]="hasChildren ? isExpanded : null"
            [attr.aria-label]="getTaskAriaLabel()"
        >
            <div
                class="flex w-full min-w-[200px] items-center justify-between border-l border-r p-2 sm:w-1/3 sm:min-w-[300px]"
                [style.min-height.px]="config.taskHeight"
            >
                <div class="flex w-full items-center justify-between gap-2">
                    <div class="flex min-w-0 flex-1 items-center gap-2">
                        <button
                            *ngIf="hasChildren && config.allowExpansion"
                            class="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded text-gray-500 transition-colors hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                            (click)="onToggleTask()"
                            [attr.aria-label]="
                                isExpanded ? 'Collapse task' : 'Expand task'
                            "
                            type="button"
                        >
                            <em
                                class="fa transition-transform duration-300"
                                [ngClass]="{
                                    'fa-chevron-down': isExpanded,
                                    'fa-chevron-right': !isExpanded
                                }"
                                aria-hidden="true"
                            ></em>
                        </button>

                        <!-- Custom task template -->
                        <ng-container
                            *ngIf="taskTemplate; else defaultTaskTemplate"
                        >
                            <ng-container
                                *ngTemplateOutlet="
                                    taskTemplate;
                                    context: { $implicit: item }
                                "
                            ></ng-container>
                        </ng-container>

                        <!-- Default task template -->
                        <ng-template #defaultTaskTemplate>
                            <button
                                class="min-w-0 flex-1 rounded p-1 text-start text-sm transition-colors hover:text-blue-600 focus:text-blue-600 focus:outline-none sm:text-[16px]"
                                [appTooltip]="formatTooltip()"
                                style="
                                    white-space: normal;
                                    word-wrap: break-word;
                                    overflow-wrap: break-word;
                                    line-height: 1.2;
                                "
                                (click)="onTaskClick()"
                                [attr.aria-label]="'Task: ' + item.name"
                            >
                                {{ item.name }}
                            </button>
                        </ng-template>
                    </div>

                    <!-- Progress badge - hide on mobile, show as tooltip -->
                    <span
                        *ngIf="
                            config.showProgress && item.progress !== undefined
                        "
                        class="mt-1 hidden flex-shrink-0 self-start rounded px-1.5 py-0.5 text-xs sm:flex"
                        [ngClass]="config.taskColors.progressBadge"
                        [attr.aria-label]="getProgressAriaLabel()"
                    >
                        {{ getProgressDisplay() }}%
                    </span>
                </div>
            </div>

            <!-- Task timeline visualization -->
            <div
                class="relative flex-1"
                [style.min-height.px]="config.taskHeight"
            >
                <div
                    class="grid h-full w-full"
                    [style.grid-template-columns]="
                        'repeat(' + displayDatesLength + ', 1fr)'
                    "
                ></div>
                <div
                    class="absolute left-0 top-0 h-full px-1"
                    style="width: 100%"
                >
                    <div
                        class="absolute top-1/2 -mt-2.5 h-4 rounded-md transition-all duration-200 hover:shadow-md sm:h-5"
                        [ngClass]="config.taskColors.background"
                        [ngStyle]="taskPosition"
                        [appTooltip]="formatTooltip()"
                        [attr.aria-label]="getTaskTimelineAriaLabel()"
                        role="progressbar"
                        [attr.aria-valuenow]="getProgressValue()"
                        [attr.aria-valuemin]="0"
                        [attr.aria-valuemax]="100"
                    >
                        <div
                            *ngIf="
                                config.showProgress &&
                                item.progress !== undefined
                            "
                            class="h-full rounded-md transition-all duration-300"
                            [ngClass]="config.taskColors.progress"
                            [style.width]="item.progress * 100 + '%'"
                        ></div>
                    </div>
                </div>
            </div>
        </div>
    `,
    // cspell:enable
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GanttTaskRowComponent<T = unknown> {
    @Input() public item!: GanttItem<T>;
    @Input() public config!: GanttConfig;
    @Input() public taskPosition!: TaskPosition;
    @Input() public isExpanded = false;
    @Input() public hasChildren = false;
    @Input() public displayDatesLength = 0;
    @Input() public taskTemplate?: TemplateRef<GanttTemplateContext<T>>;

    @Output() public taskClick = new EventEmitter<GanttItem<T>>();
    @Output() public toggleTask = new EventEmitter<string>();

    public onTaskClick(): void {
        this.taskClick.emit(this.item);
    }

    public onToggleTask(): void {
        this.toggleTask.emit(this.item.id);
    }

    public getTaskAriaLabel(): string {
        const progressText =
            this.item.progress !== undefined
                ? `, ${Math.round(this.item.progress * 100)}% complete`
                : '';
        return `Task: ${this.item.name}${progressText}`;
    }

    public getProgressAriaLabel(): string {
        if (this.item.progress === undefined) return '';
        return `${Math.round(this.item.progress * 100)} percent complete`;
    }

    public getProgressDisplay(): string {
        if (this.item.progress === undefined) return '0';
        return (this.item.progress * 100).toFixed(0);
    }

    public getProgressValue(): number {
        return this.item.progress ? Math.round(this.item.progress * 100) : 0;
    }

    public getTaskTimelineAriaLabel(): string {
        const startDate = this.item.from
            ? this.formatDate(this.item.from)
            : 'Unknown start';
        const endDate = this.item.to
            ? this.formatDate(this.item.to)
            : 'Unknown end';
        return `${this.item.name} timeline: ${startDate} to ${endDate}`;
    }

    public formatTooltip(): string {
        const fromDate = this.item.from ? this.formatDate(this.item.from) : '';
        const toDate = this.item.to ? this.formatDate(this.item.to) : '';

        if (fromDate && toDate) {
            return `${fromDate} - ${toDate}`;
        } else if (fromDate) {
            return `Start: ${fromDate}`;
        } else if (toDate) {
            return `End: ${toDate}`;
        }

        return this.item.name;
    }

    public isPlanTaskRow(): boolean {
        return (
            (this.item as any).type === 'task' ||
            (this.item.data && (this.item.data as any).type === 'task') ||
            (this.item['originalData'] &&
                (this.item['originalData'] as any).type === 'task')
        );
    }

    private formatDate(date: Date | string): string {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return dateObj.toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
        });
    }
}
