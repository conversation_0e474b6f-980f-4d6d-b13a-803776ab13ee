<app-page pageTitle="{{ plan ? plan.name : '' }}">
    <!-- Tools -->
    <ng-container tools>
        <!-- List -->
        <a class="btn btn-sm btn-success" [routerLink]="['', 'plan']">
            <i class="fa-light fa-share"></i>
            <span class="hidden md:inline">
                {{ 'translate_plans_list' | translate }}
            </span>
        </a>

        <!-- Export PDF -->
        <app-export-pdf
            *ngIf="hierarchicalData && hierarchicalData.length > 0"
            [getElementReferences]="getGanttElementForExport.bind(this)"
            [pdfName]="'gantt-chart-' + (plan?.name || 'export')"
        ></app-export-pdf>

        <!-- Back to Plan -->
        <a
            *ngIf="hierarchicalData && hierarchicalData.length > 0"
            [routerLink]="['', 'plan', 'detail', plan?.id]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-eye me-2"></i>
            <span>{{ 'translate_plan_details' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <div content>
        <div class="p-4" #ganttChart>
            <!-- Reusable Gantt Chart Component -->
            <app-gantt-chart
                [rawData]="hierarchicalData"
                [dataMapper]="planDataMapper"
                [config]="ganttConfig"
                [isLoading]="isLoading"
                [title]="plan?.name"
                [startDate]="planStartDate"
                [endDate]="planEndDate"
                [progress]="plan?.progress"
                (taskClick)="onTaskClick($event)"
                (taskToggle)="onTaskToggle($event)"
            ></app-gantt-chart>
        </div>
    </div>
</app-page>
