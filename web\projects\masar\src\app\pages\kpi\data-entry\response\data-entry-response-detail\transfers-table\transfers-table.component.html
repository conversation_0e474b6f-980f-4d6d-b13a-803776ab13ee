<app-content [contentTitle]="'translate_transfer_history' | translate">
    <app-content-loading content [isLoading]="!transfers">
        <div *ngIf="transfers" class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th>{{ 'translate_created_by' | translate }}</th>
                        <th>{{ 'translate_moved_to' | translate }}</th>
                        <th>{{ 'translate_notes' | translate }}</th>
                        <th>{{ 'translate_date' | translate }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        *ngFor="let transfer of transfers"
                        [ngClass]="{
                            'bg-red-200': transfer.direction !== 'forward'
                        }"
                    >
                        <!-- Created By -->
                        <td>{{ transfer.createdBy.name }}</td>

                        <!-- Status -->
                        <td class="text-center">
                            <div
                                class="badge text-base"
                                [ngClass]="{
                                    'badge-primary':
                                        transfer.assignee === 'data_entry',
                                    'badge-info':
                                        transfer.assignee === 'level1' ||
                                        transfer.assignee === 'level2',
                                    'badge-warning':
                                        transfer.assignee === 'kpi_manager',
                                    'badge-success':
                                        transfer.assignee === 'done'
                                }"
                            >
                                {{
                                    'translate_' + transfer.assignee | translate
                                }}
                            </div>
                        </td>

                        <!-- Notes -->
                        <td>{{ transfer.notes }}</td>

                        <!-- Created At -->
                        <td>
                            {{
                                transfer.creationTime
                                    | date : 'yyyy-MM-dd hh:mm a'
                            }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </app-content-loading>
</app-content>
