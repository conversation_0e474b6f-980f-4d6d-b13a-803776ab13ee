import { Injectable } from '@angular/core';
import { HttpCrud } from '@masar/features/http-crud/http-crud.class';
import { NotificationService } from 'mnm-webapp';
import { HttpClient } from '@angular/common/http';

interface FilterData {
    keyword: string;
}

@Injectable()
export class TableListLinkerService extends HttpCrud<unknown, FilterData> {
    public constructor(
        notificationService: NotificationService,
        http: HttpClient
    ) {
        super(http, notificationService);
    }

    public init(bodyParameterName: string, endPoint: string): void {
        this.bodyParameterName = bodyParameterName;
        this.endPoint = endPoint;
    }

    public startList(): void {
        this.startTableController(
            filter =>
                this.list({
                    keyword: filter.data.keyword,
                    pageNumber: filter.pageNumber,
                    pageSize: filter.pageSize,
                }),
            {
                data: { keyword: '' },
            }
        );
    }
}
