<app-page pageTitle="{{ 'translate_strategic_plan_details' | translate }}">
    <!-- Tools -->
    <ng-container tools>
        <a
            class="btn btn-sm btn-white flex items-center gap-1"
            [routerLink]="['', 'system-list', 'strategic-plans']"
            ><i class="fas fa-left"></i> {{ 'translate_back' | translate }}</a
        >
        <a
            class="btn btn-sm btn-primary"
            [routerLink]="['', 'system-list', 'strategic-plans', 'new']"
            ><i class="fa-light fa-plus"></i>
            {{ 'translate_add_new' | translate }}</a
        >
        <a
            class="btn btn-sm btn-info"
            *ngIf="item"
            [routerLink]="[
                '',
                'system-list',
                'strategic-plans',
                'edit',
                item.id
            ]"
            ><i class="fa-light fa-edit"></i>
            {{ 'translate_edit' | translate }}</a
        >
    </ng-container>

    <!-- Content -->
    <ng-container content>
        <!-- Details -->
        <app-content class="mb-5">
            <table content>
                <tbody>
                    <!-- Name -->
                    <tr>
                        <td>
                            {{ 'translate_name' | translate }}
                        </td>
                        <td>
                            <app-value-loading
                                [isLoading]="!item"
                                [value]="item?.name"
                            ></app-value-loading>
                        </td>
                    </tr>

                    <!-- Start Year -->
                    <tr>
                        <td>
                            {{ 'translate_start_year' | translate }}
                        </td>
                        <td>
                            <app-value-loading
                                [isLoading]="!item"
                                [value]="item?.startYear"
                            ></app-value-loading>
                        </td>
                    </tr>

                    <!-- End Year -->
                    <tr>
                        <td>
                            {{ 'translate_end_year' | translate }}
                        </td>
                        <td>
                            <app-value-loading
                                [isLoading]="!item"
                                [value]="item?.endYear"
                            ></app-value-loading>
                        </td>
                    </tr>

                    <!-- Vision -->
                    <tr>
                        <td>
                            {{ 'translate_vision' | translate }}
                        </td>
                        <td>
                            <app-value-loading
                                [isLoading]="!item"
                                [value]="item?.vision"
                            ></app-value-loading>
                        </td>
                    </tr>

                    <!-- Mission -->
                    <tr>
                        <td>
                            {{ 'translate_mission' | translate }}
                        </td>
                        <td>
                            <app-value-loading
                                [isLoading]="!item"
                                [value]="item?.mission"
                            ></app-value-loading>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <!-- Strategic Goals -->
        <app-content
            class="mb-5"
            contentTitle="{{ 'translate_strategic_goals' | translate }}"
        >
            <ng-container tools>
                <a
                    class="btn btn-sm btn-outline-white"
                    [routerLink]="['', 'system-list', 'strategic-goal', 'new']"
                    [queryParams]="{ planId: item?.id }"
                >
                    <span>{{ 'translate_add_goal' | translate }}</span>
                </a>
            </ng-container>
            <ng-container content>
                <app-list-loading [items]="item.pillars">
                    <table>
                        <thead>
                            <tr>
                                <th>{{ 'translate_pillar' | translate }}</th>
                                <th>{{ 'translate_code' | translate }}</th>
                                <th>{{ 'translate_name' | translate }}</th>
                                <th>
                                    {{ 'translate_description' | translate }}
                                </th>
                                <th>{{ 'translate_category' | translate }}</th>
                                <th>{{ 'translate_weight' | translate }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Goals within pillars -->
                            <ng-container
                                *ngFor="
                                    let pillar of item.pillars;
                                    trackBy: trackByPillarId
                                "
                            >
                                <tr
                                    *ngFor="
                                        let goal of pillar.goals;
                                        trackBy: trackByGoalId
                                    "
                                >
                                    <td>
                                        <a
                                            [routerLink]="[
                                                '',
                                                'system-list',
                                                'strategic-plans',
                                                'strategic-pillar',
                                                'detail',
                                                pillar.id
                                            ]"
                                        >
                                            {{ pillar?.name }}
                                        </a>
                                    </td>
                                    <td>{{ goal.code }}</td>
                                    <td>
                                        <a
                                            [routerLink]="[
                                                '',
                                                'system-list',
                                                'strategic-goal',
                                                'detail',
                                                goal.id
                                            ]"
                                        >
                                            {{ goal.name }}
                                        </a>
                                    </td>
                                    <td>{{ goal.description }}</td>
                                    <td>
                                        <span
                                            [class]="
                                                getBadgeClass(goal.category)
                                            "
                                        >
                                            {{
                                                goal.category
                                                    | translateItem
                                                        : 'strategic-goal-category'
                                                    | async
                                            }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ getFormattedWeight(goal.weight) }}
                                    </td>
                                </tr>
                            </ng-container>

                            <!-- Standalone goals (without pillars) -->
                            <tr
                                *ngFor="
                                    let goal of item.goals;
                                    trackBy: trackByGoalId
                                "
                            >
                                <td>
                                    <span class="text-muted">
                                        {{ '-' }}
                                    </span>
                                </td>
                                <td>{{ goal.code || '-' }}</td>
                                <td>
                                    <a
                                        [routerLink]="[
                                            '',
                                            'system-list',
                                            'strategic-goal',
                                            'detail',
                                            goal.id
                                        ]"
                                    >
                                        {{ goal.name }}
                                    </a>
                                </td>
                                <td>{{ goal.description }}</td>
                                <td>
                                    <span
                                        [class]="getBadgeClass(goal.category)"
                                    >
                                        {{
                                            goal.category
                                                | translateItem
                                                    : 'strategic-goal-category'
                                                | async
                                        }}
                                    </span>
                                </td>
                                <td>
                                    {{ getFormattedWeight(goal.weight) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </app-list-loading>
            </ng-container>
        </app-content>

        <!-- Strategic Values -->
        <app-content
            *ngIf="hasStrategicValues()"
            class="mb-5"
            contentTitle="{{ 'translate_strategic_values' | translate }}"
        >
            <ng-container content>
                <app-list-loading [items]="item.values">
                    <table>
                        <thead>
                            <tr>
                                <th>{{ 'translate_name' | translate }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let value of item.values">
                                <td>{{ value.name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </app-list-loading>
            </ng-container>
        </app-content>
    </ng-container>
</app-page>
