<app-content [contentTitle]="'translate_attachments' | translate">
    <!-- Tools -->
    <ng-container tools>
        <ng-container
            *ngIf="options.createPermission; else createButtonTemplate"
        >
            <ng-container *appHasPermissionId="options.createPermission">
                <ng-container
                    [ngTemplateOutlet]="createButtonTemplate"
                ></ng-container>
            </ng-container>
        </ng-container>

        <ng-template #createButtonTemplate>
            <button
                *ngIf="options?.canCreate !== false"
                (click)="showCreateAttachmentDialog()"
                class="btn btn-sm btn-outline-white"
            >
                <i class="fa-light fa-plus"></i>
                <span> {{ 'translate_new_attachment' | translate }}</span>
            </button>
        </ng-template>
    </ng-container>

    <!-- Content -->
    <ng-container content>
        <!-- Table -->
        <app-list-loading [items]="items">
            <div class="table-responsive">
                <table class="table-bordered table-hover table">
                    <thead class="thead-dark">
                        <tr>
                            <th>{{ 'translate_type' | translate }}</th>
                            <th>{{ 'translate_name' | translate }}</th>
                            <ng-container
                                *ngIf="
                                    options.deletePermission;
                                    else actionsTemplate
                                "
                            >
                                <ng-container
                                    *appHasPermissionId="
                                        options.deletePermission
                                    "
                                >
                                    <ng-container
                                        [ngTemplateOutlet]="actionsTemplate"
                                    ></ng-container>
                                </ng-container>
                            </ng-container>

                            <ng-template #actionsTemplate>
                                <th *ngIf="options?.canDelete !== false"></th>
                            </ng-template>
                        </tr>
                    </thead>

                    <tbody>
                        <tr *ngFor="let item of items; let idx = index">
                            <!-- Icon -->
                            <td>
                                <app-file-icon
                                    [contentType]="item.contentType"
                                ></app-file-icon>
                            </td>

                            <!-- File name -->
                            <td>
                                <a
                                    href="#"
                                    onclick="return false"
                                    (click)="
                                        download(item.id, item.contentType)
                                    "
                                >
                                    {{ item.name }}
                                </a>
                            </td>

                            <ng-container
                                *ngIf="
                                    options.deletePermission;
                                    else actionsValueTemplate
                                "
                            >
                                <ng-container
                                    *appHasPermissionId="
                                        options.deletePermission
                                    "
                                >
                                    <ng-container
                                        [ngTemplateOutlet]="
                                            actionsValueTemplate
                                        "
                                    ></ng-container>
                                </ng-container>
                            </ng-container>

                            <ng-template #actionsValueTemplate>
                                <!-- Control -->
                                <td
                                    *ngIf="options?.canDelete !== false"
                                    style="width: 1%; white-space: nowrap"
                                >
                                    <button
                                        *appHasPermissionId="
                                            options.deletePermission
                                        "
                                        class="btn btn-sm btn-danger"
                                        [appTooltip]="
                                            'translate_delete' | translate
                                        "
                                        [disabled]="
                                            currentlyDeleting.includes(item.id)
                                        "
                                        (confirm)="delete(item.id)"
                                        [swal]="{
                                            title:
                                                'translate_delete_this_item_question_mark'
                                                | translate,
                                            confirmButtonText:
                                                'translate_yes' | translate,
                                            cancelButtonText:
                                                'translate_cancel' | translate,
                                            showCancelButton: true,
                                            showCloseButton: true
                                        }"
                                    >
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </ng-template>
                        </tr>
                    </tbody>
                </table>
            </div>
        </app-list-loading>
    </ng-container>
</app-content>
