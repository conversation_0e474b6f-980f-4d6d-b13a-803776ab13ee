import {
    <PERSON>ttp<PERSON><PERSON>,
    <PERSON>ttp<PERSON><PERSON><PERSON>,
    HttpInterceptor,
    HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable()
export class TimezoneInterceptor implements HttpInterceptor {
    private readonly timezone: string;

    public constructor() {
        this.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    }

    public intercept(
        req: HttpRequest<any>,
        next: <PERSON>ttp<PERSON>and<PERSON>
    ): Observable<HttpEvent<any>> {
        // set the language
        req = req.clone({
            headers: req.headers.set('timezone', this.timezone),
        });

        return next.handle(req);
    }
}
