import { Injectable } from '@angular/core';

export interface DateRange {
    start: Date;
    end: Date;
}

@Injectable()
export class GanttDateService {
    /**
     * Parses a date input to UTC Date
     */
    public parseUtcDate(dateInput: string | Date): Date | null {
        if (!dateInput) return null;

        if (typeof dateInput === 'string') {
            return new Date(dateInput);
        } else {
            return new Date(dateInput.getTime());
        }
    }

    /**
     * Adds days to a date and returns ISO string
     */
    public addDays(dateInput: string | Date, days: number): string {
        if (!dateInput) return '';

        let date: Date;
        if (typeof dateInput === 'string') {
            date = new Date(dateInput);
        } else {
            date = new Date(dateInput.getTime());
        }

        date.setUTCDate(date.getUTCDate() + days);
        return date.toISOString();
    }

    /**
     * Formats a date as DD/MM/YYYY
     */
    public formatDate(dateInput: string | Date): string {
        if (!dateInput) return '';

        try {
            const date = this.parseUtcDate(dateInput);
            if (!date) return '';

            const day = date.getUTCDate().toString().padStart(2, '0');
            const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
            const year = date.getUTCFullYear();

            return `${day}/${month}/${year}`;
        } catch (error) {
            return '';
        }
    }

    /**
     * Calculates the first day of the month for a given date
     */
    public getFirstDayOfMonth(date: Date): Date {
        return new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), 1));
    }

    /**
     * Calculates the last day of the month for a given date
     */
    public getLastDayOfMonth(date: Date): Date {
        return new Date(
            Date.UTC(date.getUTCFullYear(), date.getUTCMonth() + 1, 0)
        );
    }

    /**
     * Calculates the difference in days between two dates
     */
    public getDaysDifference(startDate: Date, endDate: Date): number {
        return Math.round(
            (endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000)
        );
    }

    /**
     * Normalizes a date range to month boundaries
     */
    public normalizeToMonthBoundaries(start: Date, end: Date): DateRange {
        return {
            start: this.getFirstDayOfMonth(start),
            end: this.getLastDayOfMonth(end),
        };
    }

    /**
     * Calculates the number of months between two dates
     */
    public getMonthsDifference(startDate: Date, endDate: Date): number {
        return (
            (endDate.getUTCFullYear() - startDate.getUTCFullYear()) * 12 +
            (endDate.getUTCMonth() - startDate.getUTCMonth()) +
            1
        );
    }
}
