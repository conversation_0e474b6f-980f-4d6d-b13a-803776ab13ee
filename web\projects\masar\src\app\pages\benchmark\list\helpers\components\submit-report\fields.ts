import { MnmFormField } from '@masar/shared/components';
import { Validators } from '@angular/forms';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'reportTime',
        type: 'date',
        label: 'translate_report_time',
        validators: [Validators.required],
    },

    {
        name: 'reportType',
        type: 'select',
        label: 'translate_report_type',
        bindLabel: 'name',
        bindValue: 'id',
        compareWith: (a, b) => a.id === b,
        validators: [Validators.required],
    },

    {
        name: 'reportDetails',
        type: 'textarea',
        label: 'translate_report_details',
        validators: [Validators.required],
    },

    {
        name: 'libraryFiles',
    },
];
