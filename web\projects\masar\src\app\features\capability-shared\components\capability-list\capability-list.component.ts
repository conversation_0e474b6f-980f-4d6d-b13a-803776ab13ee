import {
    Component,
    EventEmitter,
    Input,
    NgModuleRef,
    OnDestroy,
    Output,
    TemplateRef,
    ViewChild,
} from '@angular/core';
import { Capability } from '@masar/common/models';
import { Subject } from 'rxjs';
import { permissionList } from '@masar/common/constants';
import { KpiLinkerComponent } from '../../../kpi-shared/components/kpi-linker/kpi-linker.component';
import { LibraryFileLinkerComponent } from '@masar/features/masar/components';
import { ModalService } from 'mnm-webapp';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { SharedCapabilityService } from '@masar/shared/services';

export type Field = 'name' | 'type' | 'year' | 'kpi_count' | 'file_count';

@Component({
    selector: 'app-capability-list',
    templateUrl: './capability-list.component.html',
})
export class CapabilityListComponent implements OnDestroy {
    @ViewChild('list') public list: CapabilityListComponent;

    @Input() public items: Capability[];
    @Input() public kpiResultFromYear: number = null;
    @Input() public enableDrag: boolean = false;
    @Input() public kpiResultToYear: number = null;
    @Input() public shownFields: Field[] = [
        'name',
        'type',
        'year',
        'kpi_count',
        'file_count',
    ];

    @Input() public customFields: {
        name: string;
        template: TemplateRef<any>;
    }[];

    @Output() public order = new EventEmitter<CdkDragDrop<Capability>>();

    public permissionList = permissionList;

    private unsubscribeAll = new Subject();

    public constructor(
        private modalService: ModalService,
        private capabilityService: SharedCapabilityService,
        private moduleRef: NgModuleRef<any>
    ) {}

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public async showKpiListModal(item: Capability): Promise<void> {
        await this.modalService.show(KpiLinkerComponent, {
            size: { width: '70%' },
            beforeInit: c => {
                c.mode = 'view';
                c.listLinkedKpisCallback = (
                    keyword: string,
                    pageNumber: number,
                    pageSize: number
                ) =>
                    this.capabilityService.listLinkedKpis(
                        item.id,
                        keyword,
                        this.kpiResultFromYear,
                        this.kpiResultToYear,
                        pageNumber,
                        pageSize
                    );
            },
            moduleRef: this.moduleRef,
        });
    }

    public async showLibraryFileListModal(item: Capability): Promise<void> {
        await this.modalService.show(LibraryFileLinkerComponent, {
            beforeInit: c => {
                c.mode = 'view';
                c.listLinkedFilesCallback = (
                    keyword: string,
                    pageNumber: number,
                    pageSize: number
                ) =>
                    this.capabilityService.listLinkedLibraryFiles(
                        item.id,
                        keyword,
                        pageNumber,
                        pageSize
                    );
            },
            moduleRef: this.moduleRef,
        });
    }
}
