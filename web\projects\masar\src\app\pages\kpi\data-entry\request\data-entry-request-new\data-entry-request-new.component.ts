import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { Loader } from '@masar/common/misc/loader';
import { Item, KpiResultDataEntryRequest } from '@masar/common/models';
import { HelperService, MiscApiService } from '@masar/core/services';
import { MnmFormState } from '@masar/shared/components';
import { combineLatest, forkJoin, Subject } from 'rxjs';
import {
    finalize,
    first,
    map,
    startWith,
    takeUntil,
    tap,
} from 'rxjs/operators';
import { KpiResultDataEntryRequestService } from '../../../kpi-result-data-entry-request.service';
import { KpiResultRequestService } from '../../../kpi-result-request.service';
import { fields } from './fields';
import Swal from 'sweetalert2';

@Component({
    selector: 'app-data-entry-request-new',
    templateUrl: './data-entry-request-new.component.html',
})
export class DataEntryRequestNewComponent implements OnInit, OnDestroy {
    public isSubmitting = false;
    public mode: 'new' | 'edit';
    public formState: MnmFormState;
    private readonly kpiLoader: Loader<Item>;
    private unsubscribeAll = new Subject();
    private hasAvailableKpis = false;

    public constructor(
        private activatedRoute: ActivatedRoute,
        private notificationService: NotificationService,
        private kpiResultDataEntryRequestService: KpiResultDataEntryRequestService,
        private translateService: TranslateService,
        private readonly helperService: HelperService,
        private kpiResultRequestService: KpiResultRequestService,
        miscApiService: MiscApiService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);

        miscApiService.setMiscItems(this.formState, [
            ['kpi-result-data-entry-request-path', 'path'],
            ['kpi-cycle', 'measurementCycle'],
        ]);

        miscApiService
            .years()
            .subscribe(items => (this.formState.get('year').items = items));

        miscApiService
            .departments()
            .subscribe(
                items => (this.formState.get('departments').items = items)
            );
        miscApiService
            .kpiTypes()
            .subscribe(items => (this.formState.get('KpiTypes').items = items));

        this.kpiLoader = new Loader<Item>(keyword => {
            const fieldsName = [
                'year',
                'measurementCycle',
                'departments',
                'isSpecial',
                'shouldExcludeResultsWithValues',
                'KpiTypes',
            ];

            const [
                year,
                measurementCycle,
                departments,
                isSpecial,
                shouldExcludeResultsWithValues,
                kpiTypes,
            ] = fieldsName.map(f => this.formState.group.controls[f].value);

            const departmentIds = departments?.map(d => d.id) || [];

            return kpiResultRequestService
                .requestKpi({
                    keyword,
                    year,
                    measurementCycle,
                    departmentIds,
                    isSpecial,
                    shouldExcludeResultsWithValues,
                    kpiTypes,
                })
                .pipe(
                    map(kpis =>
                        kpis.map(kpi => ({
                            id: kpi.id,
                            name: `${kpi.code} - ${kpi.name}`,
                        }))
                    ),
                    tap(kpis => {
                        if (kpis.length === 0 && year) {
                            Swal.fire({
                                title: this.translateService.instant(
                                    'translate_no_kpis'
                                ),
                                icon: 'warning',
                                confirmButtonText:
                                    this.translateService.instant(
                                        'translate_ok'
                                    ),
                                confirmButtonColor: '#3085d6',
                            });
                        }
                    })
                );
        });
        this.formState.get('kpis').loader = this.kpiLoader;

        this.monitorForm();
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[1].path) {
                case 'new':
                    this.mode = 'new';
                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            const id = params.id;
                            this.kpiResultDataEntryRequestService
                                .get(id, true)
                                .subscribe(item => {
                                    this.fillForm(item);
                                });
                        });

                    // Disable cycle and periods fields.
                    this.formState.group.controls['measurementCycle'].disable();
                    this.formState.group.controls['periods'].disable();
                    this.formState.group.controls['year'].disable();

                    break;
            }
        });
    }

    public ngOnDestroy(): void {
        this.kpiLoader.dispose();

        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }
    public submit(): void {
        this.formState.setTriedToSubmit();
        if (this.formState.group.invalid) {
            return;
        }
        // First check if user has selected any KPIs
        const selectedKpis = this.formState.group.controls['kpis'].value;
        if (!selectedKpis || selectedKpis.length === 0) {
            // Check if there are any available KPIs
            if (!this.hasAvailableKpis) {
                Swal.fire({
                    title: this.translateService.instant('translate_no_kpis'),
                    icon: 'warning',
                    confirmButtonText:
                        this.translateService.instant('translate_ok'),
                    confirmButtonColor: '#3085d6',
                });
                return;
            }
        }
        this.isSubmitting = true;
        const observable =
            this.mode == 'new'
                ? this.kpiResultDataEntryRequestService.create(
                      this.formState.group.getRawValue()
                  )
                : this.kpiResultDataEntryRequestService.update(
                      this.formState.group.getRawValue()
                  );
        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(item => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'kpi', 'data-entry-request'],
                    item.id
                );
            });
    }

    private fillForm(item: KpiResultDataEntryRequest): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }

    private monitorForm(): void {
        // Monitor changes to the measurement cycle,
        // and update the selection items of the periods
        // field.
        this.formState.group.controls['measurementCycle'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const periodsControl = this.formState.group.controls['periods'];
                const periodsField = this.formState.get('periods');
                const setPeriods = (): void => {
                    periodsField.items = (
                        value === 'annual'
                            ? [0]
                            : value === 'semi_annual'
                            ? [0, 1]
                            : value === 'quarter'
                            ? [0, 1, 2, 3]
                            : [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
                    ).map(x => ({ id: x, name: `${x + 1}` }));
                };

                setPeriods();

                // Cycle and periods are disabled during edits.
                if (this.mode === 'edit') return;

                periodsField.items = [];
                periodsControl.setValue([]);

                if (!value) {
                    periodsControl.disable();
                    return;
                }

                setPeriods();

                periodsControl.enable();
            });

        // Monitor changes to the path select and
        // update the note accordingly.
        this.formState.group.controls['path'].valueChanges
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(value => {
                const pathField = this.formState.get('path');

                forkJoin([
                    this.translateService.get(
                        'translate_kpi_data_entry_default_note'
                    ),
                    this.translateService.get(
                        'translate_kpi_data_entry_immediate_note'
                    ),
                ]).subscribe(([defaultNote, immediateNote]) => {
                    pathField.note =
                        value === 'default'
                            ? defaultNote
                            : value === 'immediate'
                            ? immediateNote
                            : '';
                });
            });

        /* Resetting the kpis field when any of the fields in the array changes. */
        {
            const fieldsName = [
                'year',
                'measurementCycle',
                'departments',
                'isSpecial',
                'shouldExcludeResultsWithValues',
            ];
            const arr = fieldsName.map(f =>
                this.formState.group.controls[f].valueChanges.pipe(
                    startWith(null)
                )
            );
            combineLatest(arr)
                .pipe(takeUntil(this.unsubscribeAll))
                .subscribe(_ => {
                    this.checkForAvailableKpis();
                });
        }
    }
    private checkForAvailableKpis(): void {
        const fieldsName = [
            'year',
            'measurementCycle',
            'departments',
            'isSpecial',
            'shouldExcludeResultsWithValues',
        ];

        const [
            year,
            measurementCycle,
            departments,
            isSpecial,
            shouldExcludeResultsWithValues,
        ] = fieldsName.map(f => this.formState.group.controls[f].value);

        const departmentIds = departments?.map(d => d.id) || [];

        // Check if there are any KPIs available with the current filters
        this.kpiResultRequestService
            .requestKpi({
                keyword: '', // Empty keyword to get all KPIs
                year,
                measurementCycle,
                departmentIds,
                isSpecial,
                shouldExcludeResultsWithValues,
            })
            .subscribe(kpis => {
                this.hasAvailableKpis = kpis.length > 0;
            });
    }
}
