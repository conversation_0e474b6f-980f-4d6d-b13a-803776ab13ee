import { Component, OnD<PERSON>roy } from '@angular/core';
import { DashboardStyle1Setting, Kpi } from '@masar/common/models';
import { DashboardSettingService } from '@masar/pages/system-settings/setting/dashboard-setting.service';
import { DomSanitizer, SafeStyle } from '@angular/platform-browser';
import { DashboardTopItemDto } from '@masar/pages/dashboard/main-dashboard/dtos';
import { DashboardService } from '@masar/pages/dashboard/dashboard.service';
import { switchMap, takeUntil } from 'rxjs/operators';
import {
    AppSettingFetcherService,
    MiscApiService,
    YearService,
} from '@masar/core/services';
import { Subject } from 'rxjs';
import { DashboardGoalsResponse } from '../../interfaces/dashboard-goals.interface';

@Component({
    selector: 'app-dashboard-style-1',
    templateUrl: 'dashboard-style-1.component.html',
})
export class DashboardStyle1Component implements OnDestroy {
    public setting: DashboardStyle1Setting;
    public bannerUrl: SafeStyle;

    public topItems?: DashboardTopItemDto;

    public goalKpiTypeIds: string[];
    public goalsResponse: DashboardGoalsResponse;
    public isOverallPerformanceEnabled: boolean;

    public primaryKpis: Kpi[];
    public isPrimaryKpisEnabled = false;

    private unsubscribeAll = new Subject();

    public constructor(
        public readonly dashboardSettingService: DashboardSettingService,
        private readonly dashboardService: DashboardService,
        private readonly sanitizer: DomSanitizer,
        private readonly yearService: YearService,
        private readonly miscApiService: MiscApiService,
        private readonly appSettingFetcherService: AppSettingFetcherService
    ) {
        this.appSettingFetcherService.get$.subscribe(item => {
            this.isOverallPerformanceEnabled =
                item.strategicGoalSetting.isOverallPerformanceCalculationEnabled;
        });
        this.loadDashboardSetting();
        this.loadDashboardTopItems();
        this.loadKpiTypeIds();
        this.initGoalsLoader();
        this.initPrimaryKpisLoader();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    private loadDashboardSetting(): void {
        this.dashboardSettingService.getStyle1Setting().subscribe(item => {
            this.setting = item;

            if (this.setting.bannerFile) {
                this.dashboardSettingService
                    .getStyle1File(this.setting.bannerFile.name)
                    .subscribe(blob => {
                        this.bannerUrl =
                            this.sanitizer.bypassSecurityTrustStyle(
                                `url(${URL.createObjectURL(blob)})`
                            );
                    });
            }
        });
    }

    private loadDashboardTopItems(): void {
        this.dashboardService
            .topItems()
            .subscribe(result => (this.topItems = result));
    }

    private loadKpiTypeIds(): void {
        this.miscApiService.kpiTypes().subscribe(items => {
            this.goalKpiTypeIds = items
                .filter(x => x.isUsedToComputeGoalAchievement)
                .map(x => x.id);
        });
    }

    private initGoalsLoader(): void {
        this.yearService.changes$
            .pipe(takeUntil(this.unsubscribeAll))
            .pipe(switchMap(() => this.dashboardService.goals()))
            .subscribe(items => (this.goalsResponse = items));
    }

    private initPrimaryKpisLoader(): void {
        this.yearService.changes$
            .pipe(takeUntil(this.unsubscribeAll))
            .pipe(switchMap(() => this.dashboardService.primaryKpis()))
            .subscribe(items => (this.primaryKpis = items));

        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(settings => {
                this.isPrimaryKpisEnabled =
                    settings.kpiSetting.optionalFields.find(
                        field => field.name === 'is_primary'
                    )?.isEnabled || false;
            });
    }
}
