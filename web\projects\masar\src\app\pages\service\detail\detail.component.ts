import { Component, Ng<PERSON><PERSON>, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { first, takeUntil } from 'rxjs/operators';
import { Kpi, Service } from '@masar/common/models';
import { ServiceService } from '../service.service';
import { TableResult } from '@masar/common/misc/table';
import { Observable, Subject } from 'rxjs';
import { AppSettingFetcherService } from '@masar/core/services';
import { functions } from '@masar/common/misc/functions';

@Component({
    selector: 'app-detail',
    templateUrl: './detail.component.html',
})
export class DetailComponent implements OnDestroy {
    public service: Service;

    public serviceId: string;

    public totalServiceFees?: number;

    public optionalSections: Record<string, boolean> = {};

    private unsubscribeAll = new Subject();

    public constructor(
        private serviceService: ServiceService,
        private activatedRoute: ActivatedRoute,
        private readonly appSettingFetcherService: AppSettingFetcherService,
        private ngZone: NgZone
    ) {
        this.activatedRoute.params.pipe(first()).subscribe(params => {
            {
                this.serviceId = params.id;
                this.serviceService.get(this.serviceId).subscribe(item => {
                    this.service = item;
                    this.totalServiceFees = item.fees?.reduce(
                        (acc, fee) => acc + fee.value,
                        0
                    );
                });
            }
        });

        this.syncServiceSettings();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public listLinkedKpis(): (
        keyword: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Kpi>> {
        return (keyword: string, pageNumber: number, pageSize: number) =>
            this.serviceService.listLinkedKpis(
                this.serviceId,
                keyword,
                null,
                null,
                pageNumber,
                pageSize
            );
    }

    public print(): void {
        this.serviceService
            .printService(this.serviceId)
            .pipe()
            .subscribe({
                next: file => {
                    this.ngZone.runOutsideAngular(() => {
                        functions.downloadBlobIntoFile(
                            file,
                            `service-${this.serviceId}.pdf`
                        );
                    });
                },
                error: error => {
                    console.error('Error downloading PDF:', error);
                },
            });
    }
    public listUnlinkedKpis(): (
        keyword: string,
        kpiNumber: string,
        pageNumber: number,
        pageSize: number
    ) => Observable<TableResult<Kpi>> {
        return (
            keyword: string,
            kpiNumber: string,
            pageNumber: number,
            pageSize: number
        ) =>
            this.serviceService.listUnlinkedKpis(
                this.serviceId,
                keyword,
                kpiNumber,
                pageNumber,
                pageSize
            );
    }

    public linkKpi(): (kpiId: string) => Observable<string> {
        return (kpiId: string) =>
            this.serviceService.linkKpi(this.serviceId, kpiId);
    }

    public unlinkKpi(): (kpiId: string) => Observable<string> {
        return (kpiId: string) =>
            this.serviceService.unlinkKpi(this.serviceId, kpiId);
    }

    private syncServiceSettings(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(({ serviceSetting }) => {
                const isSectionEnabled: (name: string) => boolean = (
                    name: string
                ) =>
                    serviceSetting.optionalSections.find(x => x.name === name)
                        .isEnabled;

                const sections = ['evaluation'];

                sections.forEach(
                    section =>
                        (this.optionalSections[section] =
                            isSectionEnabled(section))
                );
            });
    }
}
