import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Kpi } from '@masar/common/models';
import { environment } from '@masar/env/environment';
import { map } from 'rxjs/operators';
import { miscFunctions, Result } from 'mnm-webapp';
import { TableResult } from '@masar/common/misc/table';

interface StrategicGoalKpiWeight {
    id: string;
    kpiWeights: {
        id: string;
        weight: number;
    }[];
}

@Injectable()
export class KpiWeightsService {
    public constructor(private httpClient: HttpClient) {}

    public getKpis(strategicGoalId: string): Observable<Kpi[]> {
        return this.httpClient
            .get<Result<TableResult<Kpi>>>(
                `${environment.apiUrl}/strategic-goal/kpi/${strategicGoalId}`
            )
            .pipe(map(result => result.extra.items));
    }

    public updateKpiWeights(payload: StrategicGoalKpiWeight): Observable<any> {
        return this.httpClient
            .put<Result<any>>(
                `${environment.apiUrl}/strategic-goal/update-kpi-weight`,
                miscFunctions.objectToURLParams({
                    strategicGoalKpiWeight: JSON.stringify(payload),
                })
            )
            .pipe(map(result => result.extra));
    }
}
