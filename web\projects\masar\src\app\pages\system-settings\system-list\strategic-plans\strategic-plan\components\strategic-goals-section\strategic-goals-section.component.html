<div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
    <div class="mb-4 flex items-center justify-between">
        <div class="flex items-center">
            <h3 class="text-lg font-semibold text-gray-900">
                {{ 'translate_strategic_goals' | translate }}
            </h3>
        </div>
        <span
            class="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
        >
            {{ strategicPlan?.pillars?.length || 0 }}
        </span>
    </div>

    <div
        *ngIf="strategicPlan?.pillars?.length; else noGoals"
        class="overflow-hidden"
    >
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                        {{ 'translate_code' | translate }}
                    </th>
                    <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                        {{ 'translate_name' | translate }}
                    </th>
                    <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                        {{ 'translate_description' | translate }}
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
                <tr
                    *ngFor="let goal of strategicPlan.pillars; let i = index"
                    [class.bg-gray-50]="i % 2 === 1"
                >
                    <td class="whitespace-nowrap px-4 py-3">
                        <span
                            *ngIf="goal.code"
                            class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
                        >
                            {{ goal.code }}
                        </span>
                        <span *ngIf="!goal.code" class="text-sm text-gray-400"
                            >—</span
                        >
                    </td>
                    <td class="px-4 py-3">
                        <div class="text-sm font-medium text-gray-900">
                            {{ goal.name }}
                        </div>
                    </td>
                    <td class="px-4 py-3">
                        <div
                            class="text-sm text-gray-600"
                            *ngIf="goal.description"
                        >
                            {{ goal.description }}
                        </div>
                        <span
                            *ngIf="!goal.description"
                            class="text-sm text-gray-400"
                            >—</span
                        >
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <ng-template #noGoals>
        <div class="py-8 text-center">
            <p class="text-sm text-gray-500">
                {{ 'translate_no_strategic_goals_found' | translate }}
            </p>
        </div>
    </ng-template>
</div>
