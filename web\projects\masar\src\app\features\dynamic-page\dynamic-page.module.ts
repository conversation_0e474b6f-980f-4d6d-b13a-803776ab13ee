import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
    ActionButtonComponent,
    DetailPageComponent,
    DetailSectionComponent,
    ListPageComponent,
    ListSectionComponent,
    NewFormComponent,
    NewPageComponent,
    PropertyValueComponent,
    StatisticsPageComponent,
    TableActionsColumnComponent,
    TableListComponent,
    TableListLinkerComponent,
    PropertyValueBooleanComponent,
    TableListFiltersComponent,
    DetailSectionsComponent,
    TableListBasicComponent,
    ProgressRingSectionComponent,
    PropertyValueRingComponent,
    PropertyValueArrayComponent,
    PropertyValueFileComponent,
    StatisticsSectionComponent,
} from './components';
import { SharedModule } from '@masar/shared/shared.module';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MasarModule } from '@masar/features/masar/masar.module';
import { FormsModule } from '@angular/forms';
import { FlowModule } from '@masar/features/flow/flow.module';
import { AttachmentModule } from '@masar/features/attachment';
import {
    GetTableListActionsPipe,
    GetTableListTabsPipe,
    IsNotEqualFalsePipe,
    ShowFlowMoveButtonForItemPipe,
} from './components/table-list/pipes';
import { GetAlertsPipe } from './components/detail-page/pipes';
import { StatisticalGroupHasValuePipe } from './components/statistics-section/statistical-group-has-value.pipe';
import { NgSelectModule } from '@ng-select/ng-select';

const sharedComponents = [
    DetailPageComponent,
    PropertyValueComponent,
    TableActionsColumnComponent,
    TableListComponent,
    ListPageComponent,
    ListSectionComponent,
    NewPageComponent,
    ActionButtonComponent,
    NewFormComponent,
    StatisticsPageComponent,
    DetailSectionComponent,
    PropertyValueBooleanComponent,
    TableListFiltersComponent,
];

@NgModule({
    declarations: [
        sharedComponents,
        GetTableListActionsPipe,
        GetTableListTabsPipe,
        TableListLinkerComponent,
        TableListBasicComponent,
        DetailSectionsComponent,
        ShowFlowMoveButtonForItemPipe,
        IsNotEqualFalsePipe,
        GetAlertsPipe,
        ProgressRingSectionComponent,
        PropertyValueRingComponent,
        PropertyValueArrayComponent,
        PropertyValueFileComponent,
        StatisticsSectionComponent,
        StatisticalGroupHasValuePipe,
    ],
    imports: [
        CommonModule,
        SharedModule,
        RouterModule,
        TranslateModule,
        MasarModule,
        FormsModule,
        FlowModule,
        AttachmentModule,
        NgSelectModule,
    ],
    exports: [sharedComponents],
})
export class DynamicPageModule {}
