<app-detail-page
    *ngIf="data"
    [data]="data"
    [getItemCb]="getItemCb()"
    (itemChange)="item = $any($event)"
>
    <!-- Risk-specific tools -->
    <ng-container tools>
        <!-- Add New Risk Management Procedure Dialog -->
        <button
            *appHasPermissionId="permissionList.riskWrite"
            (click)="openNewDialog()"
            class="btn btn-sm btn-outline-white flex flex-row items-center gap-1"
        >
            <i class="fa fa-plus"></i>
            <span>{{
                'translate_add_risk_management_procedure' | translate
            }}</span>
        </button>
    </ng-container>
    <app-content
        *ngIf="item"
        contentTitle="{{ 'translate_risk_weight_matrix' | translate }}"
    >
        <app-risk-matrix
            content
            [selection]="{
                impactId: item.impact.id,
                probabilityId: item.probability.id
            }"
        ></app-risk-matrix>
    </app-content>

    <app-content
        *ngIf="item?.impactAfterMitigation && item?.probabilityAfterMitigation"
        [contentTitle]="
            'translate_risk_weight_matrix_after_mitigation' | translate
        "
    >
        <app-risk-matrix
            content
            [selection]="{
                impactId: item.impactAfterMitigation.id,
                probabilityId: item.probabilityAfterMitigation.id
            }"
        ></app-risk-matrix>
    </app-content>

    <!-- Lists -->
    <div *ngIf="item" class="flex flex-col gap-2 lg:flex-row">
        <!-- Strategic goals -->
        <app-content
            contentTitle="{{ 'translate_strategic_goals' | translate }}"
            class="flex-1"
            *ngIf="item.goals.length > 0"
        >
            <table content>
                <tbody>
                    <tr *ngFor="let goal of item.goals">
                        <td>
                            {{ goal.name }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <!-- Plans -->
        <app-content
            contentTitle="{{ 'translate_plans' | translate }}"
            class="flex-1"
            *ngIf="item.plans.length > 0 && isPlansEnabled"
        >
            <table content>
                <tbody>
                    <tr *ngFor="let plan of item.plans">
                        <td>
                            <app-plan-link [item]="plan"></app-plan-link>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>

        <!-- Operations -->
        <app-content
            contentTitle="{{ 'translate_operations' | translate }}"
            class="flex-1"
            *ngIf="item.operations.length > 0"
        >
            <table content>
                <tbody>
                    <tr *ngFor="let operation of item.operations">
                        <td>
                            <app-operation-link
                                [item]="operation"
                            ></app-operation-link>
                        </td>
                    </tr>
                </tbody>
            </table>
        </app-content>
    </div>

    <!-- Management procedures -->
    <app-risk-management-procedure-list
        [riskId]="item.id"
        *ngIf="item"
    ></app-risk-management-procedure-list>

    <!-- Evaluation -->
    <app-evaluation-instance-list
        *ngIf="item"
        [item]="item"
        [type]="'risk'"
    ></app-evaluation-instance-list>
</app-detail-page>
