import { DepartmentUserLink } from './department-user-link.model';
import { Permission } from './permission.model';

export interface User {
    id: string;
    name: string;
    employeeNumber: string;
    email: string;
    creationTime: Date;
    type: 'normal' | 'ldap' | 'office_365';
    status: 'active' | 'suspended' | 'deleted';
    departmentLinks: DepartmentUserLink[];
    permissions: Permission[];
    phoneNumber: string;
    rank: string;
}
