import { Item } from '../models';
import { TranslateService } from '@ngx-translate/core';

export const appFunctions = {
    genders: [
        { id: 'male', name: 'translate_male' },
        { id: 'female', name: 'translate_female' },
    ],

    translateList: (
        list: Item[],
        translateService: TranslateService
    ): Item[] => {
        return list.map(x => {
            x.name = translateService.instant(x.name);
            return x;
        });
    },
};
