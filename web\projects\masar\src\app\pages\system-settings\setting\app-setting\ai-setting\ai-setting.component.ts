import { Component } from '@angular/core';
import { AiSetting } from '@masar/common/models';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from 'mnm-webapp';
import { finalize } from 'rxjs/operators';
import { AiSettingService } from '../ai-setting.service';

@Component({
    selector: 'app-ai-setting',
    templateUrl: './ai-setting.component.html',
})
export class AiSettingComponent {
    public aiSetting: AiSetting;
    public isSubmitting = false;

    public constructor(
        private aiSettingService: AiSettingService,
        private translateService: TranslateService,
        private notificationService: NotificationService
    ) {
        aiSettingService.get().subscribe(item => (this.aiSetting = item));
    }

    public save(): void {
        if (this.isSubmitting) return;

        this.isSubmitting = true;

        this.aiSettingService.update(this.aiSetting).subscribe(() => {
            this.translateService
                .get('translate_item_updated_successfully')
                .pipe(finalize(() => (this.isSubmitting = false)))
                .subscribe(str => {
                    this.notificationService.notifySuccess(str);
                });
        });
    }
}
