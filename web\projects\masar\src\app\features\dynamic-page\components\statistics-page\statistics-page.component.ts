import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    Input,
    TemplateRef,
} from '@angular/core';
import { Department } from '@masar/common/models';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import {
    StatisticsGroup,
    StatisticsGroupCallbackMap,
    StatisticsGroupLinkMap,
} from '@masar/features/dynamic-page/components/statistics-section/interfaces';

@Component({
    selector: 'app-statistics-page',
    templateUrl: './statistics-page.component.html',
})
export class StatisticsPageComponent implements AfterViewInit {
    @Input() public statisticsGroups?: StatisticsGroup[];
    @Input() public statisticsGroupLinkMap?: StatisticsGroupLinkMap;
    @Input() public statisticsGroupCallbackMap?: StatisticsGroupCallbackMap;

    @Input() public statisticsTitle: string = '';
    @Input() public toolsTemplateRef: TemplateRef<any>;
    @Input() public departmentStatisticTemplateRef: TemplateRef<any>;

    @Input() public departmentStatisticsLoader: <
        T extends { parent: Department; children: S[] },
        S extends { department: Department }
    >(
        parentId?: string
    ) => Observable<T | null>;

    public departmentStatistics: {
        parent: Department;
        children: any[];
    };

    public previousParents: Department[] = [];

    public isLoadingDepartments = false;

    public constructor(private changeDetectorRef: ChangeDetectorRef) {}

    public ngAfterViewInit(): void {
        this.refresh();
        this.changeDetectorRef.detectChanges();
    }

    public refresh(): void {
        this.refreshDepartmentStatistics(this.departmentStatistics?.parent);
    }

    public goToPreviousDepartmentStatistics(): void {
        const previousDepartment = this.previousParents.pop() ?? null;
        this.refreshDepartmentStatistics(previousDepartment);
    }

    public goToNextDepartmentStatistics(parentDepartment: Department): void {
        this.previousParents.push(this.departmentStatistics.parent);
        this.refreshDepartmentStatistics(parentDepartment);
    }

    private refreshDepartmentStatistics(parentDepartment?: Department): void {
        if (!this.departmentStatisticsLoader) return;

        this.isLoadingDepartments = true;

        this.departmentStatisticsLoader(parentDepartment?.id)
            .pipe(finalize(() => (this.isLoadingDepartments = false)))
            .subscribe(item => {
                this.departmentStatistics = item;
            });
    }
}
