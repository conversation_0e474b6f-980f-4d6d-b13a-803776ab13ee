import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnInit,
    ViewChild,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { finalize } from 'rxjs/operators';
import { forkJoin } from 'rxjs';
import { PlanTaskService } from '@masar/pages/plan-task/plan-task.service';
import { PlanTask, PlanSubtask, PlanSubsubtask } from '@masar/common/models';
import {
    GanttChartComponent,
    GanttDataMapper,
    GanttConfig,
} from '@masar/shared/components';
import { PlanSubtaskListService } from '@masar/features/plan-shared/components/plan-subtask-list/plan-subtask-list.service';

// Union type for task-related data
type TaskItemData = PlanTask | PlanSubtask | PlanSubsubtask;

// Extended type to handle the mixed hierarchy
interface TaskHierarchyItem {
    id: string;
    name?: string;
    from: Date | string;
    to: Date | string;
    progress?: number;
    type: 'task' | 'subtask' | 'subsubtask';
    parentId?: string;
    originalData: TaskItemData;
    children?: TaskHierarchyItem[];
}

@Component({
    selector: 'app-plan-task-gantt',
    templateUrl: './plan-task-gantt.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PlanTaskGanttComponent implements OnInit {
    @ViewChild(GanttChartComponent, { static: false })
    public ganttChart: GanttChartComponent<TaskHierarchyItem>;

    public task: PlanTask;
    public isLoading = true;
    public error: string | null = null;
    public hierarchicalData: TaskHierarchyItem[] = [];

    // Computed properties for template
    public get taskStartDate(): Date | undefined {
        return this.task?.from ? new Date(this.task.from) : undefined;
    }

    public get taskEndDate(): Date | undefined {
        return this.task?.to ? new Date(this.task.to) : undefined;
    }

    // Gantt configuration - tasks will be initially expanded
    public ganttConfig: GanttConfig = {
        initiallyExpanded: true,
        showProgress: true,
        allowExpansion: true,
    };

    // Data mapper for the hierarchical task structure
    public taskDataMapper: GanttDataMapper<TaskHierarchyItem> = {
        getId: (item: TaskHierarchyItem) => item.id,
        getName: (item: TaskHierarchyItem) => {
            if (item.name) {
                return item.name;
            }
            // Fallback for items without names (like PlanSubsubtask)
            return `${item.type} ${item.id}`;
        },
        getFromDate: (item: TaskHierarchyItem) => item.from,
        getToDate: (item: TaskHierarchyItem) => item.to,
        getProgress: (item: TaskHierarchyItem) => item.progress || 0,
        getChildren: (item: TaskHierarchyItem) => item.children || [],
    };

    public constructor(
        private route: ActivatedRoute,
        private planTaskService: PlanTaskService,
        private planSubtaskListService: PlanSubtaskListService,
        private cdr: ChangeDetectorRef
    ) {}

    public ngOnInit(): void {
        this.loadTask();
    }

    public onTaskClick(_item: TaskHierarchyItem): void {
        // Handle task click - navigate to task details, etc.
    }

    public onTaskToggle(_taskId: string): void {
        // Handle task expansion/collapse
    }

    // Method to get Gantt container element for PDF export
    public getGanttElementForExport(): HTMLElement[] {
        return this.ganttChart?.getGanttElementForExport() || [];
    }

    private loadTask(): void {
        const taskId = this.route.snapshot.paramMap.get('id');
        if (!taskId) {
            this.error = 'Task ID not found';
            this.isLoading = false;
            this.cdr.markForCheck();
            return;
        }

        this.isLoading = true;
        this.error = null;

        // Load both task and subtasks in parallel
        forkJoin({
            task: this.planTaskService.get(taskId),
            subtasks: this.planSubtaskListService.list(
                '', // keyword
                null, // assigneeType
                [], // departmentIds
                false, // includeChildDepartments
                [], // teamIds
                [], // userIds
                [], // planIds
                [taskId], // taskIds - filter by current task
                null, // from
                null, // to
                null, // status
                'from:ascending', // orderBy
                0, // pageNumber
                1000, // pageSize - large number to get all subtasks
                true // considerPlanFilterAsBase
            ),
        })
            .pipe(
                finalize(() => {
                    this.isLoading = false;
                    this.cdr.markForCheck();
                })
            )
            .subscribe({
                next: result => {
                    this.task = result.task;
                    // Update task with the detailed subtasks from the API
                    this.task.subtasks = result.subtasks.items;
                    this.transformTaskToHierarchicalData();
                    this.cdr.markForCheck();
                },
                error: _error => {
                    this.error = 'Failed to load task and subtasks';
                    this.cdr.markForCheck();
                },
            });
    }

    private transformTaskToHierarchicalData(): void {
        if (!this.task) {
            this.hierarchicalData = [];
            return;
        }

        // Create a single item array with the task as the root
        this.hierarchicalData = [this.convertTaskToHierarchyItem(this.task)];
    }

    private convertTaskToHierarchyItem(task: PlanTask): TaskHierarchyItem {
        const hierarchyItem: TaskHierarchyItem = {
            id: task.id || '',
            name: task.name || '',
            from: task.from,
            to: task.to,
            progress: task.progress || 0,
            type: 'task',
            originalData: task,
        };

        // Convert subtasks to children
        if (task.subtasks && task.subtasks.length > 0) {
            hierarchyItem.children = task.subtasks.map(subtask =>
                this.convertSubtaskToHierarchyItem(subtask, task.id)
            );
        }

        return hierarchyItem;
    }

    private convertSubtaskToHierarchyItem(
        subtask: PlanSubtask,
        parentId: string
    ): TaskHierarchyItem {
        const hierarchyItem: TaskHierarchyItem = {
            id: subtask.id || '',
            name: subtask.name || '',
            from: subtask.from,
            to: subtask.to,
            progress: subtask.progress || 0,
            type: 'subtask',
            parentId,
            originalData: subtask,
        };

        // Convert subsubtasks to children
        if (subtask.subsubtasks && subtask.subsubtasks.length > 0) {
            hierarchyItem.children = subtask.subsubtasks.map(subsubtask =>
                this.convertSubsubtaskToHierarchyItem(subsubtask, subtask.id)
            );
        }

        return hierarchyItem;
    }

    private convertSubsubtaskToHierarchyItem(
        subsubtask: PlanSubsubtask,
        parentId: string
    ): TaskHierarchyItem {
        return {
            id: subsubtask.id || '',
            // PlanSubsubtask doesn't have a name, so we create one
            name: `Subsubtask ${subsubtask.id}`,
            from: subsubtask.from,
            to: subsubtask.to,
            progress: subsubtask.progress || 0,
            type: 'subsubtask',
            parentId,
            originalData: subsubtask,
        };
    }
}
