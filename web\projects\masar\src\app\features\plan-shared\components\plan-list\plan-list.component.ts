import {
    Component,
    EventEmitter,
    Input,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>roy,
    OnInit,
    Output,
} from '@angular/core';
import { Item, Plan } from '@masar/common/models';
import { finalize, takeUntil } from 'rxjs/operators';
import { TableController } from '@masar/common/misc/table/table-controller';
import { NotificationService } from 'mnm-webapp';
import { AppSettingFetcherService, MiscApiService } from '@masar/core/services';
import { functions } from '@masar/common/misc/functions';
import { permissionList } from '@masar/common/constants';
import { ActivatedRoute, Router } from '@angular/router';
import { Loader } from '@masar/common/misc/loader';
import { TranslateService } from '@ngx-translate/core';
import { getOrderByAsString } from '@masar/common/utils';
import { PlanListService } from './plan-list.service';
import { Subject } from 'rxjs';

interface PlanFilter {
    keyword?: string;
    years?: string[];
    assigneeType?: string | null;
    departmentIds?: string[];
    includeChildDepartments?: boolean;
    teamIds?: string[];
    userIds?: string[];
    categoryIds?: string[];
    from?: Date;
    to?: Date;
    governmentStrategicGoalIds?: string[];
    strategicGoalIds?: string[];
    kpiIds?: string[];
    partnerIds?: string[];
    initiatives?: string;
    status?: '' | 'new' | 'approved';
    progressStatus?: '' | 'completed' | 'in_progress';
    partneringDepartmentIds?: Item[];
}

type ShownFilters =
    | 'keyword'
    | 'years'
    | 'departmentIds'
    | 'includeChildDepartments'
    | 'teamIds'
    | 'userIds'
    | 'categoryIds'
    | 'from'
    | 'to'
    | 'governmentStrategicGoalIds'
    | 'strategicGoalIds'
    | 'kpiIds'
    | 'partnerIds'
    | 'initiatives'
    | 'status'
    | 'progressStatus'
    | 'assigneeType'
    | 'partneringDepartmentIds';

type ShownFields =
    | 'progress'
    | 'serialNumber'
    | 'planTitle'
    | 'assigned'
    | 'approvingDepartment'
    | 'from'
    | 'to'
    | 'approvalStatus'
    | 'actions';

@Component({
    selector: 'app-plan-list',
    templateUrl: './plan-list.component.html',
    providers: [PlanListService],
})
export class PlanListComponent implements OnInit, OnDestroy {
    @Input() public mode: 'approved' | 'not_approved' | 'all' = 'approved';
    @Input() public listTitle: string = 'translate_operational_plans';
    @Input() public isFilterInUrl = false;
    @Input() public categoryTypes: string[];

    @Input() public shownFilters: Partial<Record<ShownFilters, boolean>> = {
        keyword: true,
        years: true,
        departmentIds: true,
        includeChildDepartments: true,
        teamIds: true,
        userIds: true,
        categoryIds: true,
        from: true,
        to: true,
        governmentStrategicGoalIds: true,
        strategicGoalIds: true,
        kpiIds: true,
        partnerIds: true,
        initiatives: true,
        status: true,
        progressStatus: true,
        assigneeType: true,
        partneringDepartmentIds: true,
    };

    @Input() public shownFields: Partial<Record<ShownFields, boolean>> = {
        from: true,
        to: true,
        progress: true,
        serialNumber: true,
        planTitle: true,
        assigned: true,
        approvingDepartment: true,
        approvalStatus: true,
        actions: true,
    };

    @Output() public tableControllerChange = new EventEmitter<
        TableController<Plan, PlanFilter>
    >();

    public tableController: TableController<Plan, PlanFilter>;
    public years: Item[];
    public assigneeTypes: Item[];
    public departments: Item[];
    public teams: Item[];
    public users: Item[];
    public categories: Item[];
    public governmentStrategicGoals: Item[];
    public strategicGoals: Item[];
    public partners: Item[];
    public progressStatuses: Item[];
    public kpiLoader: Loader<Item>;

    public currentlyProcessing: string[] = [];

    public permissionList = permissionList;

    public isInternalPartner: boolean;
    public shouldRuleCheckBeforeSubmission: boolean;
    public showLabel: boolean = false;
    private unsubscribeAll = new Subject();

    public constructor(
        private planListService: PlanListService,
        private notificationService: NotificationService,
        private ngZone: NgZone,
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private appSettingFetcherService: AppSettingFetcherService,
        miscApiService: MiscApiService,
        translateService: TranslateService
    ) {
        miscApiService
            .getList('plan-assignee-type')
            .subscribe(items => (this.assigneeTypes = items));

        miscApiService
            .getList('creation-year')
            .subscribe(items => (this.years = items));

        miscApiService
            .getList('plan-category')
            .subscribe(items => (this.categories = items));

        miscApiService
            .departments()
            .subscribe(items => (this.departments = items));

        miscApiService
            .getList('team', { linkedWith: 'plan' }, true)
            .subscribe(items => (this.teams = items));

        miscApiService
            .getList('user', { linkedWith: 'plan' }, true)
            .subscribe(items => (this.users = items));

        miscApiService
            .getList('government-strategic-goal', undefined, true)
            .subscribe(items => (this.governmentStrategicGoals = items));

        miscApiService
            .getList('strategic-goal', undefined, true)
            .subscribe(items => (this.strategicGoals = items));

        miscApiService
            .getList('partner', undefined, true)
            .subscribe(items => (this.partners = items));

        this.kpiLoader = new Loader<Item>(keyword =>
            miscApiService.kpis({ keyword, isLinkableToPlans: true })
        );
        this.progressStatuses = [
            {
                id: 'completed',
                name: translateService.instant('translate_completed'),
            },
            {
                id: 'in_progress',
                name: translateService.instant('translate_in_progress'),
            },
        ];

        //this.createFetchers();

        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(data => {
                const internalPartnerField = data.planSetting.optionalFields
                    .filter(field => field.name === 'internal_partners')
                    .pop(); // Gets the last matching item or undefined

                if (internalPartnerField) {
                    this.isInternalPartner = internalPartnerField.isEnabled;
                }
            });
    }

    public ngOnInit(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(settings => {
                const ministryStrategicGoalsSetting =
                    settings.planSetting.optionalFields.find(
                        field => field.name === 'ministry_strategic_goals'
                    );
                this.shownFilters.strategicGoalIds =
                    ministryStrategicGoalsSetting?.isEnabled ?? false;
                const categorySettings =
                    settings.planSetting.optionalFields.find(
                        field => field.name === 'plan_category'
                    );
                this.shownFilters.categoryIds =
                    categorySettings.isEnabled ?? false;
                this.shouldRuleCheckBeforeSubmission =
                    settings.planSetting.shouldRuleCheckBeforeSubmission;
            });
        this.tableController = new TableController<Plan, PlanFilter>(
            filter => {
                return this.planListService.list(
                    filter.data.keyword,
                    filter.data.years,
                    filter.data.assigneeType,
                    filter.data.departmentIds,
                    filter.data.includeChildDepartments,
                    filter.data.teamIds,
                    filter.data.userIds,
                    filter.data.categoryIds,
                    filter.data.from ? new Date(filter.data.from) : null,
                    filter.data.to ? new Date(filter.data.to) : null,
                    filter.data.partneringDepartmentIds,
                    filter.data.governmentStrategicGoalIds,
                    filter.data.strategicGoalIds,
                    filter.data.kpiIds,
                    filter.data.partnerIds,
                    filter.data.initiatives,
                    filter.data.status,
                    filter.data.progressStatus ?? '',
                    this.mode === 'not_approved',
                    getOrderByAsString(filter.orderBy),
                    filter.pageNumber,
                    filter.pageSize,
                    this.categoryTypes
                );
            },
            {
                data: {
                    keyword: '',
                    years: [],
                    assigneeType: null,
                    departmentIds: [],
                    teamIds: [],
                    userIds: [],
                    categoryIds: [],
                    from: null,
                    to: null,
                    partneringDepartmentIds: [],
                    governmentStrategicGoalIds: [],
                    strategicGoalIds: [],
                    kpiIds: [],
                    partnerIds: [],
                    initiatives: '',
                    status: '',
                    progressStatus: null,
                },
            },
            this.isFilterInUrl
                ? {
                      routingControls: {
                          router: this.router,
                          activatedRoute: this.activatedRoute,
                      },
                  }
                : {}
        );

        this.tableController.start();

        this.tableControllerChange.emit(this.tableController);
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
        this.kpiLoader.dispose();
    }

    public delete(item: Plan): void {
        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyProcessing.push(item.id);
        this.planListService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyProcessing = this.currentlyProcessing.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }

    public duplicate(item: Plan): void {
        this.currentlyProcessing.push(item.id);
        this.planListService
            .duplicate(item.id)
            .pipe(
                finalize(() => {
                    this.currentlyProcessing = this.currentlyProcessing.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(_ => {
                this.tableController.filter$.next(false);
            });
    }

    public export(item: Plan): void {
        if (this.currentlyProcessing.includes(item.id)) {
            return;
        }

        this.currentlyProcessing.push(item.id);

        this.planListService
            .export(item.id)
            .pipe(
                finalize(
                    () =>
                        (this.currentlyProcessing =
                            this.currentlyProcessing.filter(x => x !== item.id))
                )
            )
            .subscribe(file => {
                this.ngZone.runOutsideAngular(() => {
                    functions.downloadBlobIntoFile(file);
                });
            });
    }

    public navigateToGanttView(plan: Plan): void {
        this.router.navigate(['/plan', 'gantt', plan.id]);
    }

    public togglePin(item: Plan): void {
        this.currentlyProcessing.push(item.id);
        this.planListService
            .togglePin(item.id, !item.pinned)
            .pipe(
                finalize(
                    () =>
                        (this.currentlyProcessing =
                            this.currentlyProcessing.filter(x => x !== item.id))
                )
            )
            .subscribe(_ => {
                this.tableController.filter$.next();
            });
    }
}
