import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { StrategicPlanComponent } from '@masar/pages/strategic-plan/strategic-plan.component';
import { DashboardComponent } from './dashboard/dashboard.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full',
    },
    {
        path: 'dashboard',
        component: StrategicPlanComponent,
        children: [
            {
                path: '',
                component: DashboardComponent,
                data: {
                    title: 'translate_strategic_plan_dashboard',
                },
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class StrategicPlanRoutingModule {}
