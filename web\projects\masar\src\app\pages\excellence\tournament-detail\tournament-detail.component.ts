import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { catchError, filter } from 'rxjs/operators';
import { flippingCard } from '@masar/common/animations';
import { Tournament } from '@masar/common/models';
import { ExcellenceService } from '../excellence.service';

@Component({
    selector: 'app-tournament-detail',
    templateUrl: './tournament-detail.component.html',
    animations: [...flippingCard],
})
export class TournamentDetailComponent {
    public tournament: Tournament;

    public constructor(
        activatedRoute: ActivatedRoute,
        router: Router,
        excellenceService: ExcellenceService
    ) {
        const tournamentId =
            activatedRoute.snapshot.paramMap.get('tournamentId');
        if (!tournamentId) {
            router.navigate(['']);
            return;
        }

        excellenceService
            .getTournament(tournamentId)
            .pipe(
                catchError(() => {
                    router.navigate(['']);
                    return of(null);
                }),
                filter(x => x !== null)
            )
            .subscribe(item => (this.tournament = item));
    }
}
