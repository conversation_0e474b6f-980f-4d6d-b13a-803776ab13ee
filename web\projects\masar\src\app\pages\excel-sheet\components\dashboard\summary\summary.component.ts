import { Component, Input } from '@angular/core';
import { DataEntry } from '../../data-entry/data-entry.component';
import { Center } from '../../centers/centers.component';
import { colors } from '../../../constants';

@Component({
    selector: 'app-summary',
    templateUrl: './summary.component.html',
})
export class SummaryComponent {
    @Input() public centers!: Center[];

    @Input() public isLoadingEntries!: boolean;

    @Input() public set dataEntries(dataEntries: DataEntry[]) {
        this.items = this.centers.map(c => {
            const total = dataEntries
                .filter(e => e.centerId === c.id)
                .map(e => e.value)
                .reduce((a, b) => a + b, 0);

            return { label: c.name, value: total };
        });
    }

    public readonly colors = colors;

    public items: { label: string; value: number }[] = [];
}
