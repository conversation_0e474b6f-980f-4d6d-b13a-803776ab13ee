.ai-button {
    @apply px-4 py-2;

    position: relative;
    display: flex;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    border: 0;
    border-radius: 12px;
    color: #fff;
    cursor: pointer;
    gap: 8px;
    letter-spacing: 0.5px;
    transform: translateZ(0);
    transition: all 0.4s ease;
}

/* Rainbow button - works on any background */
.ai-button.rainbow {
    animation: gradient-slide 12s ease infinite;
    background: linear-gradient(45deg, #ff1b6b, #ff930f, #9000ff, #ff1b6b);
    background-size: 400% 100%;
    box-shadow: 0 4px 20px rgb(255 27 107 / 30%);
}

.ai-button:hover {
    animation-play-state: paused;
    transform: translateY(-2px) translateZ(0);
}

.ai-button.rainbow:hover {
    box-shadow: 0 10px 30px -5px rgb(255 255 255 / 40%),
        0 0 20px rgb(255 255 255 / 30%);
}

.ai-button:active {
    transform: translateY(1px) translateZ(0);
}

.ai-button::after {
    position: absolute;
    top: 0;
    left: -150%;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background: linear-gradient(
        90deg,
        rgb(255 255 255 / 0%) 0%,
        rgb(255 255 255 / 20%) 25%,
        rgb(255 255 255 / 60%) 50%,
        rgb(255 255 255 / 20%) 75%,
        rgb(255 255 255 / 0%) 100%
    );
    content: '';
    transform: skewX(-25deg);
    transition: all 0.2s ease;
}

.ai-button:hover::after {
    animation: shine-sweep 1.2s infinite;
}

.button-text {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.button-text svg {
    transition: transform 0.3s ease;
}

.ai-button:hover .button-text svg {
    transform: translateX(3px) scale(1.1);
}

@keyframes gradient-slide {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes shine-sweep {
    0% {
        left: -150%;
    }

    100% {
        left: 150%;
    }
}

/* Pulse effect for icons */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.8;
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(1.05);
    }

    100% {
        opacity: 0.8;
        transform: scale(1);
    }
}

/* Background label */
.bg-label {
    position: fixed;
    bottom: 20px;
    left: 20px;
    color: rgb(255 255 255 / 70%);
}
