import {
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { StatisticalReportCategoryResult } from '@masar/common/models';
import { StatisticalReportService } from '@masar/pages/statistical-report/statistical-report.service';
import { StatisticalReportCategoryRejectionResult } from '@masar/common/models/statistical-report-category-rejection-result';
import { finalize, takeUntil } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-statistical-report-history',
    templateUrl: './statistical-report-history.component.html',
})
export class StatisticalReportHistoryComponent implements OnInit, OnDestroy {
    @Input() public result: StatisticalReportCategoryResult;
    @Output() public dismiss = new EventEmitter();

    public rejectionDetails: StatisticalReportCategoryRejectionResult[] = [];
    public isLoading = true;

    // Pre-translated headers to avoid repeated pipe executions
    public dateHeader: string;
    public rejectedByHeader: string;
    public notesHeader: string;
    public valueRemovedHeader: string;
    public attachmentRemovedHeader: string;
    private destroy$ = new Subject<void>();

    public constructor(
        private statisticalReportService: StatisticalReportService,
        private translateService: TranslateService
    ) {}

    public ngOnInit(): void {
        // Pre-translate all headers at once
        this.translateHeaders();
        this.loadRejectionHistory();
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    // TrackBy function improves ngFor performance by tracking identity
    public trackById(
        _index: number,
        item: StatisticalReportCategoryRejectionResult
    ): string {
        return item.id;
    }

    private translateHeaders(): void {
        // Get all translations at once to avoid multiple service calls
        this.translateService
            .get([
                'translate_date',
                'translate_rejected_by',
                'translate_notes',
                'translate_value',
                'translate_attachment',
                'translate_no_rejection_history',
            ])
            .subscribe(translations => {
                this.dateHeader = translations['translate_date'];
                this.rejectedByHeader = translations['translate_rejected_by'];
                this.notesHeader = translations['translate_notes'];
                this.valueRemovedHeader = translations['translate_value'];
                this.attachmentRemovedHeader =
                    translations['translate_attachment'];
            });
    }

    private loadRejectionHistory(): void {
        this.isLoading = true;

        this.statisticalReportService
            .viewNotes(this.result.id)
            .pipe(
                takeUntil(this.destroy$), // Prevent memory leaks
                finalize(() => {
                    this.isLoading = false;
                })
            )
            .subscribe(response => {
                this.rejectionDetails = response || [];
            });
    }
}
