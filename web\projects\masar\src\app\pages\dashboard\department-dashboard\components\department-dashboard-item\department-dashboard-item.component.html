<div class="rounded-lg bg-primary p-6 shadow-lg">
    <!-- Department Section -->
    <div class="mb-4 flex items-center justify-between">
        <!-- Title -->
        <div class="text-xl font-bold text-white">
            {{ department.name }}
        </div>
        <button
            class="text-sm text-gray-200 underline transition hover:text-white"
            *ngIf="department.childCount > 0"
            [routerLink]="['', 'dashboard', 'department']"
            [queryParams]="{ parentId: department.id }"
        >
            {{ 'translate_departments_count' | translate }}
            ({{ department.childCount }})
        </button>
    </div>

    <!-- Stats and Boxes Container -->
    <div class="grid grid-cols-2 gap-2 md:grid-cols-4 md:gap-4 lg:gap-6">
        <ng-container *ngFor="let card of cards">
            <ng-container
                [ngTemplateOutlet]="cardStatsTemplate"
                [ngTemplateOutletContext]="{
                    link: card.link,
                    queryParams: card.queryParams,
                    count: card.count,
                    label: card.label
                }"
            >
            </ng-container>
        </ng-container>
    </div>
</div>

<!-- Card stats template -->
<ng-template
    #cardStatsTemplate
    let-link="link"
    let-queryParams="queryParams"
    let-count="count"
    let-label="label"
>
    <a
        *ngIf="count > 0; else currentCardStatTemplate"
        [routerLink]="link"
        [queryParams]="queryParams"
        class="transform cursor-pointer no-underline transition hover:scale-105 hover:no-underline"
    >
        <ng-container
            [ngTemplateOutlet]="currentCardStatTemplate"
        ></ng-container>
    </a>

    <ng-template #currentCardStatTemplate>
        <ng-container
            [ngTemplateOutlet]="cardContentStatsTemplate"
            [ngTemplateOutletContext]="{
                count: count,
                label: label
            }"
        ></ng-container>
    </ng-template>
</ng-template>

<ng-template #cardContentStatsTemplate let-count="count" let-label="label">
    <div
        [ngClass]="{
            'bg-white': count > 0,
            'bg-gray-300': count === 0
        }"
        class="h-full w-full transform rounded-lg p-2 text-center text-sm shadow-md transition hover:shadow-lg"
    >
        <div class="mb-2 font-medium text-gray-700">
            {{ label | translate }}
        </div>
        <div class="text-3xl font-bold text-gray-900">
            {{ count }}
        </div>
    </div>
</ng-template>
