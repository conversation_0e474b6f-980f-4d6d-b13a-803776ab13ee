import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ExcelSheetComponent } from './excel-sheet.component';
import { SharedModule } from '@masar/shared/shared.module';
import { ExcelSheetRoutingModule } from './excel-sheet.routing';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { MasarModule } from '@masar/features/masar/masar.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { ChartsModule } from 'ng2-charts';
import { DepartmentsComponent } from './components/departments/departments.component';
import { ServicesChartComponent } from './components/dashboard/services-chart/services-chart.component';
import { ServicesComponent } from './components/services/services.component';
import { CentersComponent } from './components/centers/centers.component';
import { ExcelComponent } from './components/excel/excel.component';
import { SummaryComponent } from './components/dashboard/summary/summary.component';
import { DataEntryComponent } from './components/data-entry/data-entry.component';
import { CentersChartComponent } from './components/dashboard/centers-chart/centers-chart.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { ExcelSheetService } from './excel-sheet.service';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslationModule } from '@ng-omar/translation';

@NgModule({
    declarations: [
        ExcelSheetComponent,
        DepartmentsComponent,
        CentersComponent,
        ServicesComponent,
        DataEntryComponent,
        DashboardComponent,
        SummaryComponent,
        ServicesChartComponent,
        CentersChartComponent,
        ExcelComponent,
    ],
    imports: [
        CommonModule,
        SharedModule,
        ExcelSheetRoutingModule,
        TranslationModule,
        SweetAlert2Module,
        ReactiveFormsModule,
        FormsModule,
        MasarModule,
        NgSelectModule,
        ChartsModule,
    ],
    providers: [ExcelSheetService],
})
export class ExcelSheetModule {}
